{"name": "global", "version": "0.1.0", "scripts": {"dev": "vite --host 0.0.0.0", "test": "vite --mode test2", "build:test": "vite build --mode test ", "build:release": "vite build --mode release", "build:pro": "vite build --mode production", "build": "vite build", "build:example": "vite build --mode example", "serve:test": "http-server ./dist-test -o", "serve": "http-server ./dist -o", "serve:example": "http-server ./dist-example -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "lint:eslint": "eslint src/**/*.{js,vue} --fix", "lint:stylelint": "stylelint src/**/*.{vue,scss,less} --fix", "prepare": "husky install", "build:all": "concurrently \"npm run build:test\" \"npm run build:release\" \"npm run build:pro\" "}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.1.0", "@tinymce/tinymce-vue": "^4.0.5", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "next", "axios": "^0.24.0", "concurrently": "^7.0.0", "dayjs": "^1.10.7", "echarts": "^5.4.2", "element-plus": "2.6.0", "elui-china-area-dht": "^2.0.0", "emoj-vue-chao": "^1.2.0", "fork": "^1.3.1", "hotkeys-js": "^3.8.7", "js-cookie": "^3.0.1", "mavon-editor": "^3.0.0-beta", "md-editor-v3": "^4.13.1", "mitt": "^3.0.0", "mockjs": "^1.1.0", "naming-style": "^1.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.0", "postcss-html": "^1.3.0", "postcss-syntax": "^0.36.2", "qs": "^6.11.0", "screenfull": "^6.0.0", "sortablejs": "^1.15.0", "tinymce": "^5.10.2", "vue": "^3.2.22", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.2", "vue-router": "^4.0.12", "vuex": "^4.0.2", "wangeditor": "^4.7.15"}, "devDependencies": {"@vitejs/plugin-vue": "^1.10.0", "@vue/compiler-sfc": "^3.2.22", "eslint": "^8.3.0", "eslint-plugin-vue": "^8.1.1", "http-server": "^14.0.0", "husky": "^7.0.4", "js-sha256": "^0.9.0", "lint-staged": "^12.1.2", "plop": "^2.7.6", "postcss-html": "^1.3.0", "postcss-scss": "^4.0.2", "prettier": "3.2.5", "sass": "1.43.5", "svgo": "^2.8.0", "unplugin-auto-import": "^0.4.18", "unplugin-vue-components": "^0.17.2", "uuid": "^8.3.2", "vite": "^2.6.14", "vite-plugin-banner": "^0.1.3", "vite-plugin-compression": "^0.3.6", "vite-plugin-html": "^2.1.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-restart": "^0.0.2", "vite-plugin-spritesmith": "^0.1.1", "vite-plugin-svg-icons": "^1.0.5", "vite-plugin-vue-setup-extend": "^0.1.0", "vue-eslint-parser": "^8.0.1"}}