// 全局样式
a {
  text-decoration: none; /* 去除下划线 */
  outline: none; /* 去除点击时的轮廓 */
  padding: 0; /* 去除默认内边距 */
  margin: 0; /* 去除默认外边距 */
  background-color: transparent; /* 去除背景颜色 */
}

a:hover {
  text-decoration: none; /* 去除鼠标悬停时的下划线 */
  color: inherit; /* 可以根据需要设置悬停颜色 */
}

a:active {
  text-decoration: none; /* 去除点击时的下划线和其他效果 */
  color: inherit; /* 可以根据需要设置点击时的颜色 */
}

a:visited {
  color: inherit; /* 去除访问后的颜色变化 */
}
.span-html a{
  color: #0b82fd;
}
::-webkit-scrollbar {
  width: 13px;
  height: 13px;
}
::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 7px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
::-webkit-scrollbar-track:hover {
  background-color: #f8fafc;
}
html,
body {
  height: 100%;
}
body {
  margin: 0;
  color: #333;
  box-sizing: border-box;
  font-family: Lato, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  -webkit-tap-highlight-color: transparent;
  &.hidden {
    overflow: hidden;
  }
}
* {
  box-sizing: inherit;
}
// 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width]
[data-fixed-calc-width] {
  position: fixed;
  left: 50%;
  right: 0;
  width: calc(100% - #{$g-sub-sidebar-width});
}
[data-sidebar-no-collapse] {
  [data-fixed-calc-width] {
    transform: translateX(-50%) translateX(division($g-sub-sidebar-width, 2));
  }
  &[data-menu-mode='head'],
  &[data-menu-mode='single'] {
    [data-fixed-calc-width] {
      width: calc(100% - #{$g-sub-sidebar-width});
      transform: translateX(-50%) translateX(division($g-sub-sidebar-width, 2));
    }
  }
}
[data-sidebar-collapse] {
  [data-fixed-calc-width] {
    width: calc(100% - #{$g-main-sidebar-width});
    transform: translateX(-50%) translateX(division($g-main-sidebar-width, 2));
  }
  &[data-menu-mode='head'],
  &[data-menu-mode='single'] {
    [data-fixed-calc-width] {
      width: calc(100% - #{$g-main-sidebar-width});
      transform: translateX(-50%) translateX(32px);
    }
  }
}
[data-mode='mobile'] {
  [data-fixed-calc-width] {
    width: 100% !important;
    transform: translateX(-50%) !important;
  }
}
// textarea 字体跟随系统
textarea {
  font-family: inherit;
}
// 列表页全局样式
.el-table.list-table {
  margin: 20px 0;
  .el-button + .el-dropdown {
    margin-left: 10px;
  }
}

.el-table {
  --el-table-text-color: #000;
  -el-table-header-text-color: #303133;
}
.el-table thead {
  color: #303133;
}
.el-button.is-text {
  color: #0b82fd;
}
:root {
  --el-table-header-text-color: #303133;
  --el-table-text-color: #000;
  --el-fill-color-light: #f8faff;
  --el-text-color-placeholder: #666;
  .el-popover.el-popper {
    min-width: 0;
  }
  .el-input-group__prepend div.el-select .el-input__wrapper {
    background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
  }
  .el-button.is-text:hover {
    color: rgba(11, 130, 253, 0.7);
  }
  .el-input {
    --el-input-hover-border-color: #0b82fd;
    --el-input-text-color: #000;
    --el-input-placeholder-color: #666;
  }
  .rules {
    .el-input {
      --el-input-placeholder-color: #b0b0b0;
    }
    .el-textarea {
      --el-input-placeholder-color: #b0b0b0;
    }
  }
  .el-textarea {
    --el-input-text-color: #000;
    --el-input-placeholder-color: #666;
  }
  .el-table--default .cell {
    padding: 0 5px;
  }
  .el-divider--vertical {
    margin: 0 10px;
  }
  .is-leaf.el-table__cell {
    background-color: #f9fafb;
  }
  .el-table.is-scrolling-none th.el-table-fixed-column--left,
  .el-table.is-scrolling-none th.el-table-fixed-column--right {
    background-color: #f9fafb;
  }
  .el-pagination.is-background .el-pager li.is-active {
    background-color: #0b82fd;
  }
  .el-checkbox.el-checkbox--large {
    height: 30px;
  }
  .el-select {
    --el-select-border-color-hover: #0b82fd;
  }
  .el-date-editor {
    --el-input-hover-border-color: #0b82fd;
  }
  .el-drawer.rtl {
    z-index: 10;
  }
  #tabel-component{
    .el-popper.is-dark{
      max-width: 40vw;
    }
  }
}

.button_text {
  color: #0b82fd;
}
.button_text:hover {
  color: rgba(11, 130, 253, 0.7);
}

.checkBlue {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #0b82fd;
    border-color: #0b82fd;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #0b82fd;
  }
}
.checkRed {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #ff0000;
    border-color: #ff0000;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #ff0000;
  }
}
.el-checkbox__inner {
  border-color: #979797;
  border-radius: 0;
}
#tabel-component {
  .el-table__placeholder {
    display: none;
  }
}
