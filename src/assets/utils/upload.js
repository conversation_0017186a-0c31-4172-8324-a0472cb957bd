import axios from 'axios'
import Cookies from 'js-cookie'
import { v4 as uuidv4 } from 'uuid'
import { sha256 } from 'js-sha256'
const timestamp = new Date().getTime()
const request_id = uuidv4()
const signature = sha256(`${request_id}&&${timestamp}&&cK1!dJ0&aA0`)
const api = axios.create({
  baseURL:
          import.meta.env.VITE_OPEN_PROXY === 'true' ?
            '/proxy/' :
            import.meta.env.VITE_APP_API_BASEURL,
  timeout: 5000000,
  responseType: 'json'
})
api.interceptors.request.use(request => {
  /**
       * 全局拦截请求发送前提交的参数
       * 以下代码为示例，在请求头里带上 token 信息
       */
  //   request.headers['X-REQUEST-ID'] = request_id
  //   request.headers['X-TIMESTAMP'] = timestamp
  //   request.headers['X-SIGNATURE'] = signature
  request.headers['Content-Type'] = 'multipart/form-data'
  return request
})
export function uploads(formData) {
  let url = 'https://qy.zjol.com.cn/tmadmin/tmMan/upload/commonUploadPic'
  return new Promise(function(resove, reject) {
    api
      .post(url, formData)
      .then(res => {
        // 返回值抛出
        resove(res)
      })
      .catch(res => {
        // 异常抛出
        reject(res)
      })
  })
}
