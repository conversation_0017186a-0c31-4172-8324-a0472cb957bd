const Layout = () => import('@/layout/index.vue')
export default {
  path: '/auditManagement',
  component: Layout,
  redirect: '/auditManagement/manager',
  name: 'auditManagement',
  meta: {
    title: '审核管理',
    icon: 'sidebar-examine'
  },

  children: [{
    path: 'adopt',
    name: 'adopt',
    component: () => import('@/views/auditManagement/adopt/index.vue'),
    meta: {
      title: '待审核'
    }
  },
  {
    path: 'examine',
    name: 'examine',
    component: () => import('@/views/auditManagement/examine/index.vue'),
    meta: {
      title: '已通过'
    }
  },
  {
    path: 'delete',
    name: 'delete',
    component: () => import('@/views/auditManagement/delete/index.vue'),
    meta: {
      title: '已删除'
    }
  }

  ]
}
