const Layout = () => import('@/layout/index.vue')
export default {
  path: '/system',
  component: Layout,
  redirect: '/system/user',
  name: 'system',
  meta: {
    title: '系统管理',
    icon: 'sidebar-system'
  },
  children: [
    {
      path: 'user',
      name: 'user',
      component: () => import('@/views/system/user/index.vue'),
      meta: {
        title: '用户管理'
      }
    },
    {
      path: 'role',
      name: 'role',
      component: () => import('@/views/system/role/index.vue'),
      meta: {
        title: '角色管理'
      }
    },
    {
      path: 'jurisdiction',
      name: 'jurisdiction',
      component: () => import('@/views/system/jurisdiction/index.vue'),
      meta: {
        title: '权限管理'
      }
    },
    {
      path: 'parameter',
      name: 'parameter',
      component: () => import('@/views/system/parameter/index.vue'),
      meta: {
        title: '参数配置'
      }
    },
    {
      path: 'operationLog',
      name: 'operationLog',
      component: () => import('@/views/system/operationLog/index.vue'),
      meta: {
        title: '操作日志'
      }
    }
  ]
}
