const Layout = () => import('@/layout/index.vue')
export default {
  path: '/operationManagement',
  component: Layout,
  redirect: '/operationManagement/manager',
  name: 'operationManagement',
  meta: {
    title: '运营管理',
    icon: 'sidebar-operate'
  },

  children: [{
    path: 'hotComment',
    name: 'hotComment',
    component: () => import('@/views/operationManagement/hotComment/index.vue'),
    meta: {
      title: '热评管理'
    }
  },
  {
    path: 'blacklist',
    name: 'blacklist',
    component: () => import('@/views/operationManagement/blacklist/index.vue'),
    meta: {
      title: '黑名单'
    }
  }

  ]
}
