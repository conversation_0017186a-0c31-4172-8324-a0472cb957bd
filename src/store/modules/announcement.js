import { requests } from '@/api/system/notification'

export default {
  namespaced: true,
  state() {
    return {
      announcementVisible: false,
      htmlContent: '',
      announcementTitle: '',
      author: '',
      time: ''
    }
  },
  mutations: {
    SET_ANNOUNCEMENT_VISIBLE(state, visible) {
      state.announcementVisible = visible
    },
    SET_HTML_CONTENT(state, content) {
      state.htmlContent = content
    },
    SET_ANNOUNCEMENT_TITLE(state, title) {
      state.announcementTitle = title
    },
    SET_AUTHOR(state, author) {
      state.author = author
    },
    SET_TIME(state, time) {
      state.time = time
    },
    RESET_ANNOUNCEMENT(state) {
      state.announcementVisible = false
      state.htmlContent = ''
      state.announcementTitle = ''
      state.author = ''
      state.time = ''
    }
  },
  actions: {
    showAnnouncementWithApi({ commit }, announcementId) {
      requests.getSingleAnnouncementDetailApi(announcementId).then(res => {
        const data = res.data
        commit('SET_ANNOUNCEMENT_TITLE', data.notice_system.notice_title)
        commit('SET_HTML_CONTENT', data.notice_system.notice_content)
        commit('SET_AUTHOR', data.notice_system.subscribe)
        commit('SET_TIME', data.notice_system.update_at_string)
        commit('SET_ANNOUNCEMENT_VISIBLE', true)
      })
    },
    showAnnouncement({ commit }, { title, content, author, time }) {
      commit('SET_ANNOUNCEMENT_TITLE', title)
      commit('SET_HTML_CONTENT', content)
      commit('SET_AUTHOR', author)
      commit('SET_TIME', time)
      commit('SET_ANNOUNCEMENT_VISIBLE', true)
    },
    closeAnnouncement({ commit }) {
      commit('RESET_ANNOUNCEMENT')
    }
  }
}
