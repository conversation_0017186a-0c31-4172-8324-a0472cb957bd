import api from '@/api'
import Cookies from 'js-cookie'
import { logout } from '@/api/system/index'
import { useStore } from 'vuex'
const store = useStore()
const route = useRoute()
const state = () => ({
  account: localStorage.account || '',
  token: localStorage.token || '',
  failure_time: localStorage.failure_time || '',
  permissions: []
})

const getters = {
  isLogin: state => {
    let ren = true
    if (Cookies.get('login')) {
      ren = true
    } else {
      ren = false
    }
    return ren
  }
}

const actions = {
  login({ commit }, data) {
    return new Promise((resolve, reject) => {

    //   // 通过 mock 进行登录
    //   api.post('member/login', data, {
    //     baseURL: '/mock/'
    //   })
    //     .then(res => {
    //       commit('setUserData', { account: ********,
    //         token: '@string',
    //         failure_time: Math.ceil(new Date().getTime() / 1000) + 24 * 60 * 60 })
    //       resolve()
    //     })
    //     .catch(error => {
    //       commit('setUserData', { account: ********,
    //         token: '@string',
    //         failure_time: Math.ceil(new Date().getTime() / 1000) + 24 * 60 * 60 })
    //       resolve()
    //     })
    })
  },
  logout({ commit }) {
    commit('removeUserData')
    commit('menu/invalidRoutes', null, { root: true })
    commit('menu/removeRoutes', null, { root: true })
    logout({}).then(res => {
      Cookies.remove('login')
      Cookies.remove('ZJOL-JSESSIONID')
      // 清除已读消息记录
      store.dispatch('notification/clearReadMessages', null, { root: true })
      window.location.reload()
    })

  },
  // 获取我的权限
  getPermissions({ state, commit }) {
    return new Promise(resolve => {
      // 通过 mock 获取权限
      let permissions = [
        'permission.browse',
        'permission.create',
        'permission.edit',
        'permission.remove'
      ]
      commit('setPermissions', permissions)
      resolve(permissions)
      //   api.get('member/permission', {
      //     baseURL: '/mock/',
      //     params: {
      //       account: state.account
      //     }
      //   }).then(res => {

    //   })
    })
  },
  editPassword({ state }, data) {
    return new Promise(resolve => {
      api.post(
        'member/edit/password',
        {
          account: state.account,
          password: data.password,
          newpassword: data.newpassword
        },
        {
          baseURL: '/mock/'
        }
      ).then(() => {
        resolve()
      })
    })
  }
}

const mutations = {
  setUserData(state, data) {
    localStorage.setItem('account', data.account)
    localStorage.setItem('token', data.token)
    localStorage.setItem('failure_time', data.failure_time)
    state.account = data.account
    state.token = data.token
    state.failure_time = data.failure_time
  },
  removeUserData(state) {
    localStorage.removeItem('account')
    localStorage.removeItem('token')
    localStorage.removeItem('failure_time')
    state.account = ''
    state.token = ''
    state.failure_time = ''
  },
  setPermissions(state, permissions) {
    state.permissions = permissions
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
