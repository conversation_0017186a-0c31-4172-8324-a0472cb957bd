import { requests } from '@/api/system/notification.js'

export default {
  namespaced: true,
  state() {
    return {
      sumCount: 0, // 总数
      pendingCount: 0, // 待审核
      reportCount: 0, // 举报数
      appealCount: 0, // 申诉数
      noticeCount: 0, // 公告数
      followMessageCount: 0, // 跟评数
      latestAnnouncement: {
        read_or_no: true // 默认已读状态
      }, // 最新的公告
      readMessageIds: JSON.parse(localStorage.getItem('readMessageIds') || '[]'),
    }
  },
  mutations: {
    SET_SUM_COUNT(state, count) {
      state.sumCount = count
    },
    SET_PENDING_COUNT(state, count) {
      state.pendingCount = count
    },
    SET_REPORT_COUNT(state, count) {
      state.reportCount = count
    },
    SET_APPEAL_COUNT(state, count) {
      state.appealCount = count
    },
    SET_NOTICE_COUNT(state, count) {
      state.noticeCount = count
    },
    SET_FOLLOW_MESSAGE_COUNT(state, count) {
      state.followMessageCount = count
    },
    SET_LATEST_ANNOUNCEMENT(state, announcement) {
      state.latestAnnouncement = announcement
    },
    SET_READ_MESSAGE(state, messageId) {
      if (!state.readMessageIds.includes(messageId)) {
        state.readMessageIds.push(messageId)
        localStorage.setItem('readMessageIds', JSON.stringify(state.readMessageIds))
      }
    },
  },
  actions: {
    fetchNotificationsCount({ commit }) {
      requests
        .getNotificationsCountApi()
        .then(res => {
          commit('SET_SUM_COUNT', res.data.sum_count)
          commit('SET_PENDING_COUNT', res.data.pending_count)
          commit('SET_REPORT_COUNT', res.data.report_count)
          commit('SET_APPEAL_COUNT', res.data.appeal_count)
          commit('SET_NOTICE_COUNT', res.data.notice_count)
          commit('SET_FOLLOW_MESSAGE_COUNT', res.data.follow_message_count)
        })
        .catch(err => {
          console.error('Error fetching unread count:', err)
        })
    },
    fetchNewestAnnouncement({ commit }) {
      requests.getLatestAnnouncementApi().then(res => {
        if (Array.isArray(res.data.notice) && res.data.notice.length > 0) {
          commit('SET_LATEST_ANNOUNCEMENT', res.data.notice[0])
        }
      })
    },
    fetchAllNotifications({ dispatch }) {
      dispatch('fetchNotificationsCount')
      dispatch('fetchNewestAnnouncement')
    },
    markMessageAsRead({ commit }, { messageId }) {
      commit('SET_READ_MESSAGE', messageId)
    },
    clearReadMessages({ commit }) {
      commit('SET_READ_MESSAGE', [])
      localStorage.removeItem('readMessageIds')
    }
  },
  getters: {
    sumCount: state => state.sumCount,
    pendingCount: state => state.pendingCount,
    reportCount: state => state.reportCount,
    appealCount: state => state.appealCount,
    noticeCount: state => state.noticeCount,
    followMessageCount: state => state.followMessageCount,
    latestAnnouncement: state => state.latestAnnouncement,
    isMessageRead: state => messageId => state.readMessageIds.includes(messageId),
  }
}
