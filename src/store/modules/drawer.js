import { markRaw } from 'vue'

export default {
  namespaced: true,
  state() {
    return {
      visible: false,
      size: '30%',
      component: null,
      componentProps: {}
    }
  },
  mutations: {
    SET_VISIBLE(state, visible) {
      state.visible = visible
    },
    SET_COMPONENT(state, component) {
      state.component = component
    },
    RESET_DRAWER(state) {
      state.visible = false
      state.component = null
      state.componentProps = {}
    },
    SET_SIZE(state, size) {
      state.size = size
    },
    SET_COMPONENT_PROPS(state, componentProps) {
      state.componentProps = componentProps
    }
  },
  actions: {
    openDrawer({ commit }, component, size) {
      commit('SET_COMPONENT', markRaw(component))
      commit('SET_SIZE', size)
      commit('SET_COMPONENT_PROPS', componentProps)
      commit('SET_VISIBLE', true)
    },
    closeDrawer({ commit }) {
      commit('SET_VISIBLE', false)
      commit('SET_COMPONENT', null)
      commit('SET_COMPONENT_PROPS', {})
    }
  }
}
