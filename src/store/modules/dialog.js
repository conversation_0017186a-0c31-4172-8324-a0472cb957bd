const state = {
  dialogVisible: false,
  currentDialogComponent: null
}

const mutations = {
  SET_DIALOG_VISIBLE(state, visible) {
    state.dialogVisible = visible
  },
  SET_CURRENT_DIALOG_COMPONENT(state, component) {
    state.currentDialogComponent = component
  }
}

const actions = {
  openDialog({ commit }, component) {
    commit('SET_CURRENT_DIALOG_COMPONENT', component)
    commit('SET_DIALOG_VISIBLE', true)
  },
  closeDialog({ commit }) {
    commit('SET_DIALOG_VISIBLE', false)
    commit('SET_CURRENT_DIALOG_COMPONENT', null)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
