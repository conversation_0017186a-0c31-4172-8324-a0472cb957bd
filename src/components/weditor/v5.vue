<template>
  <div style="border: 1px solid #ccc; width: 100% ;position: relative; z-index: 999;">
    <Toolbar
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
      style="border-bottom: 1px solid #ccc"
    />
    <!-- <Toolbar
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    /> -->
    <Editor
      v-model="valueHtml"
      :default-config="editorConfig"
      :mode="mode"
      style="height: 134px"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @onBlur="handleBlue"
      @onFocus="handleFocus"
    />
  </div>
</template>

<script setup>
import '@wangeditor/editor/dist/css/style.css'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, shallowRef, onMounted, defineExpose, defineEmits, watch } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
const emit = defineEmits(['contentVal'])
const props = defineProps({
  comment_content: {
    type: String
  }
})
const isInsertingLink = ref(false)
// 编辑器实例
const editorRef = shallowRef()
// 内容 HTML
const valueHtml = ref('')
const changeText = ref(false) // ai 插入组件控制

watch(
  () => props.comment_content,
  (newValue, old) => {
    if (newValue) {
      if (changeText.value) {
        if (editorRef.value) {
          // 等待更新完成，然后设置光标位置
          nextTick(() => {
            // 将光标移动到第一行
            editorRef.value.select({
              anchor: { path: [0, 0], offset: 0 },
              focus: { path: [0, 0], offset: 0 }
            })
          })
        }
        console.log('changeText', newValue)
        valueHtml.value = newValue
        
        changeText.value = false // 确保这个操作只执行一次
      }
    } else if (old) {
      clearEditor()
    }
  }
)

const toolbarConfig = {
  toolbarKeys: [
    // {
    //   key: 'group-link',
    //   title: '链接',
    //   menuKeys: ['insertLink', 'unLink', 'viewLink']
    // }
    'insertLink', 'unLink'
  ]
  
}
const editorConfig = {
  placeholder: '请输入评论内容',
  MENU_CONF: {
    insertLink: {
      checkLink: customCheckLinkFn
    }
  },
  maxLength: 255
}
const hasLink = ref(false)
function customCheckLinkFn (text, link) {
  const content = editorRef.value.getHtml()
  hasLink.value = /<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1/.test(content)

  if (!hasLink.value) {
    const allowedDomains = [
      'https://www.tidenews.com.cn',
      'https://h5.tidenews.com.cn',
      'https://tidenews.com.cn'
    ]
    try {
      const domain = new URL(link).origin
      if (!allowedDomains.includes(domain)) {
        ElMessage({
          type: 'error',
          message: '仅支持的域名 <p>https://www.tidenews.com.cn</p> <p> https://h5.tidenews.com.cn</p> <p> https://tidenews.com.cn</p>',
          dangerouslyUseHTMLString: true
        })
      } else {
        return allowedDomains.includes(domain)
      }
    } catch (error) {
      ElMessage({
        type: 'error',
        message: '请输入正确url'
      })
    }
  } else {
    ElMessage({
      type: 'error',
      message: '只能输入一个超链接'
    })
  }
}
const clearEditor = () => {
  editorRef.value.clear()
  changeText.value = false
}

// 黏贴
const handlePaste = event => {
  if (!isInsertingLink.value) {
    event.preventDefault()
    // 获取粘贴板中的文本内容
    const text = event.clipboardData.getData('text/plain')
    // 微信复制成倍问题
    if (!editorRef.value.getText().includes(text)) {
      editorRef.value.insertText(text)
    }

  }
}
// 失去焦点
const handleBlue = () => {
  isInsertingLink.value = true
}
// 获取焦点
const handleFocus = () => {
  isInsertingLink.value = false
}
// 编辑器回调函数

const handleCreated = editor => {
  editorRef.value = editor
  const editorConfig = editor.getConfig()
  editorConfig.customPaste = (editor, event) => {
    const text = event.clipboardData.getData('text/plain').replace(/\s+/g, ' ')
    editorRef.value.insertText(text.trim())

    event.preventDefault()
    return false
  }
}
const handleChange = editor => {
  let htmlContent = editor.getHtml()
  emit('contentVal', htmlContent)
}

defineExpose({
  clearEditor,
  valueHtml,
  editorRef,
  changeText
})
</script>
<style>
.w-e-hover-bar {
  display: none !important;
}
.w-e-modal{
  padding: 10px 15px 0 15px !important;
}
.w-e-text-container a{
  color: #0B82FD;
  text-decoration: none;
}
.w-e-modal .babel-container span{
  margin-bottom: 0 !important;
}
.w-e-max-length-info{
  bottom: 0 !important;
}
.w-e-text-placeholder {
    font-style: normal !important;
    top: 10px !important;
}
</style>