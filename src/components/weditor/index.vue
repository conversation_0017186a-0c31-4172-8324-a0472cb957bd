<template>
  <div class="weditor">
    <div ref="editorRef">
    </div>
    <div class="count">{{ charCount }}/255</div>
  </div>
</template>

<script setup name='weditor'>
import { ref, onMounted, defineExpose, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import WangEditor from 'wangeditor'
const emit = defineEmits(['contentVal'])
const editor = ref(null)
const editorRef = ref(null)
const charCount = ref(0)
const hasLink = ref(false)
const props = defineProps({
  comment_content: {
    type: String
  }
})
watch(
  () => props.comment_content,
  (newValue, old) => {
    if (newValue) {
      if (editor.value) {
        editor.value.txt.html(newValue)
      }
    } else if (old) {
      clearEditor()
    }
  },
  { immediate: true }
)

onMounted(() => {
  editor.value = new WangEditor(editorRef.value)
  // 只保留超链
  
  editor.value.config.menus = ['link']
  editor.value.config.linkCheck = function (text, link) {

    const allowedDomains = ['https://www.tidenews.com.cn', 'https://h5.tidenews.com.cn', 'https://tidenews.com.cn']
    try {
      const domain = new URL(link).origin
      if (!allowedDomains.includes(domain)) {
        ElMessage({
          type: 'error',
          message: '当前域名不支持添加'
        })
      } else {
        return allowedDomains.includes(domain)
      }
    } catch (error) {
      ElMessage({
        type: 'error',
        message: '请输入正确url'
      })
    }
  }
  // 删除全屏节点
  editor.value.config.showMenuTooltips = false
  editor.value.config.showFullScreen = false

  editor.value.create()

  // change 内容事件
  editor.value.config.onchange = val => {
    // 只能加一条链接
    const regex = /<a\b[^>]*>(.*?)<\/a>/gi
    const matches = val.match(regex)
    hasLink.value = matches && matches.length > 0
    if (hasLink.value) {
      const nodeElem = editor.value.$toolbarElem.elems[0].querySelector('[data-title="链接"]')
      nodeElem.style.opacity = '0'
    } else {
      const nodeElem = editor.value.$toolbarElem.elems[0].querySelector('[data-title="链接"]')
      nodeElem.style.opacity = '1'
    }
    
    emit('contentVal', val)
  }
  const editorContainer = editorRef.value.querySelector('.w-e-text-container')
  editorContainer.style.height = '170px'
  // 限制输入255字符
  editor.value.txt.eventHooks.changeEvents.push(() => {
    const content = editor.value.txt.text()
    charCount.value = content.length
    if (content.length > 255) {
      editor.value.txt.text(content.substring(0, 255))
      editor.value.txt.html(editor.value.txt.html().substring(0, 255))
    }
  })
})
// 清空内容
const clearEditor = () => {
  editor.value.txt.clear()
}
defineExpose({
  clearEditor
})
</script>
<style>
.w-e-text-container p {
  font-size: 14px !important;
}
</style>
<style lang='scss' scoped>
.weditor {
  width: 100%;
  position: relative;
  z-index: 99;
}
.count {
  position: absolute;
  right: 10px;
  bottom: 5px;
  z-index: 99999999;
  color: #666;
  font-size: 12px;
}
</style>