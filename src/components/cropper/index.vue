<template>
  <el-dialog
    v-model="dialogVisible"
    title="图片裁剪"
    width="900px"
    top="5vh"
  >
    <div class="cropper-container">
      <div class="cropper-left">
        <div class="cropper-content">
          <VueCropper
            ref="cropperRef"
            :img="imgObj.url"
            :output-size="option.outputSize"
            :output-type="option.outputType"
            :info="option.info"
            :full="option.full"
            :can-move="option.canMove"
            :can-move-box="option.canMoveBox"
            :fixed-box="option.fixedBox"
            :original="option.original"
            :auto-crop="option.autoCrop"
            :auto-crop-width="option.autoCropWidth"
            :auto-crop-height="option.autoCropHeight"
            :center-box="option.centerBox"
            :high="option.high"
            :info-true="option.infoTrue"
            :enlarge="option.enlarge"
            :fixed="option.fixed"
            :fixed-number="option.fixedNumber"
            :mode="option.mode"
            @realTime="realTime"
          />
        </div>
        <div class="cropper-footer">
          <el-button type="primary" plain @click="changeSize(1)">1 : 1</el-button>
          <el-button type="primary" plain @click="changeSize(2)">3 : 4</el-button>
          <el-button type="primary" plain @click="changeSize(3)">4 : 3</el-button>
          <el-button :icon="ZoomIn" type="primary" plain @click="onCropperzoom(1)" />

          <el-button :icon="ZoomOut" type="primary" plain @click="onCropperzoom(-1)" />

          <el-button :icon="RefreshLeft" type="primary" plain @click="onRotateLeft" />

          <el-button :icon="RefreshRight" type="primary" plain @click="onRotateRight" />
          <!-- <el-button type="primary" @click="onSave">保存</el-button> -->
        <!-- <el-button type="info" @click="onCancle">取消</el-button> -->
        </div>
      </div>
      <div class="cropper-right">
        <div :style="previews?.div" class="avatar-upload-preview">
          <img :src="previews?.url" :style="previews?.img" />
        </div>
      </div>
    </div>
    <el-upload
      ref="uploadRef"
      :file-list="fileList"
      class="upload-demo"
      action="https://qy.zjol.com.cn/tmadmin/tmMan/upload/commonUploadPic"
      style="display:none"
      :auto-upload="false"
      :limit="1"
      :on-success="uploadSuccess"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>
    </el-upload>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSave">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { VueCropper } from 'vue-cropper'
import { ref, defineExpose } from 'vue'
import { uploads } from '@/assets/utils/upload.js'
// import { deepAssign } from '@/utils'
import {
  ZoomIn,
  ZoomOut,
  RefreshLeft,
  RefreshRight
} from '@element-plus/icons-vue'
const dialogVisible = ref(false)
const cropperRef = ref()
const props = defineProps({
  url: { // 图片路径，支持url 地址, base64, blob
    type: [String, Object],
    default: () => null
  },
  imgObj: { // 图片信息
    type: Object,
    default: () => null
  }
})
const fileList = ref([])
const uploadRef = ref()
const option = ref({
  url: 'https://qystatic.tidenews.com.cn/upload/2023/11/15/1700018778247.jpg',
  size: 1,
  outputType: 'jpeg || png || webp', // 裁剪生成图片的格式
  outputSize: 1, // 裁剪生成图片的质量
  full: false, // 输出原图比例截图 props名full
  autoCrop: true, //    是否默认生成截图框
  canMove: true, // 上传图片是否可以移动
  canMoveBox: true, // 截图框能否拖动
  fixedBox: true, // 固定截图框大小 不允许改变
  original: false, // 上传图片按照原始比例渲染
  autoCropWidth: 300, // 默认生成截图框宽度
  autoCropHeight: 281, // 默认生成截图框高度
  centerBox: false, // 截图框是否被限制在图片里面
  high: true, // 是否按照设备的dpr 输出等比例图片
  infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
  enlarge: 1, // 图片根据截图框输出比例倍数
  maxImgSize: 2000, // 限制图片最大宽度和高度
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [4, 3],
  info: true,
  mode: '100%'
})
// option.value = deepAssign(option.value, props.defaultProps)
const previews = ref(null)
const emits = defineEmits(['confirm', 'cancle', 'saveData', 'fixedNumber'])
/**
   * 取消
   */
const onCancle = () => {
  emits('cancle', null)
}
/**
   * 保存
   */
const onSave = e => {
  cropperRef.value.getCropBlob(blob => {
    const newDate = Date.now()
    const fileName = `cropper${newDate}.png`
    const raw = new File([blob], fileName, { type: 'image/png', lastModified: newDate })
    raw.uid = newDate
    const url = window.URL.createObjectURL(blob)
    const newImgObj = {
      name: fileName,
      url: url,
      raw,
      status: props.imgObj?.status || 'ready',
      percentage: props.imgObj?.percentage || 0,
      size: raw.size,
      uid: raw.uid
    }
    var formData = new FormData()
    formData.append('file', raw)
    emits('saveData', formData)
    // 接口上传

    // uploads(formData).then(res => {
    //   console.log(res)
    // })
    // // element组件上传
    // fileList.value.push(newImgObj)
    // uploadRef.value.submit()

    // dialogVisible.value = false
  })
}
// 上传成功
function uploadSuccess(val) {
  console.log(val)
  fileList.value = []
  emits('confirm', val)
  dialogVisible.value = false
}
// 修改比例
function changeSize(val) {
  if (val === 1) {
    cropperRef.value.cropW = 300
    cropperRef.value.cropH = 300
  } else if (val === 2) {
    cropperRef.value.cropW = 300
    cropperRef.value.cropH = 400
    emits('fixedNumber', '3:4')

  } else if (val === 3) {
    cropperRef.value.cropW = 300
    cropperRef.value.cropH = 225
    emits('fixedNumber', '4:3')
  }
}
// 缩放
const onCropperzoom = (num = 1) => {
  cropperRef.value.changeScale(num)
}

// 左旋转
const onRotateLeft = () => {
  cropperRef.value.rotateLeft()
}
/**
       * 右旋转
       */
const onRotateRight = () => {
  cropperRef.value.rotateRight()
}
/**
     * 实时预览事件
     */
const realTime = res => {
  previews.value = res
}
/**
   *  图片加载的回调, 返回结果 success, error
   */
// const imgLoad = () => { }
defineExpose({
  dialogVisible  })
</script>
  <style lang="scss" scoped>
  .cropper-container{
    width: 800px;
    height: 700px;
    margin: 0 auto;
  }
    .cropper-content{
        width: 800px;
        height: 600px;
    }
    .cropper-right{
        position: fixed;
        right: 50px;
        top: 50%;
        display: none;
    }
    .cropper-footer{
      display: flex;
      margin-top: 30px;
    }
  </style>
