<template>
  <div class="reply">
    <div v-if="!drawer" class="floating" @click="drawer = true;"></div>

    <el-drawer
      v-model="drawer" :closable="true" :append-to-body="true" direction="btt" :size="700" :modal="false" class="drawerStyle" style="
        width: 760px;
        left: auto;
        box-shadow: 0px 0px 8px 0px rgba(141, 141, 141, 0.5);
        border-radius: 6px;
        border: 1px solid #dcdcdc;
        margin-bottom:20px;
        margin-right:1%;
      " :with-header="false" :before-close="handleClose"
    >
      <div class="closeCircle" @click="handleClose"></div>
      <div v-if="replyFlag" class="topReplyText">
        回复 {{ ruleForm.user_placeholder }}：
        <div class="reply-content" v-html="ruleForm.content"></div>
        </div>
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" class="ruleForm" status-icon>
        <el-form-item prop="comment_content" style="margin-bottom: 20px; margin-top:20px">
          <!-- <el-input v-model="ruleForm.comment_content" type="textarea" maxlength="255" show-word-limit
            placeholder="请输入评论内容" :rows="5" /> -->
          <WeditorV5 ref="weditorRef" :comment_content="ruleForm.comment_content" @contentVal="handleContentVal"></WeditorV5>
          <div v-show="sensitive.length > 0" style="width: 100%; text-align:left">
            <span style="color:red;">发现敏感词：{{ sensitive }}</span>
          </div>
          <div v-if="!module.moduleBox" style="margin-top: 15px; width:100%">
            <div
              v-show="!ruleForm.expression_url && !replyFlag" v-auth="'comment_operation:module'" class="module"
              @click="showModel"
            >
              <span class="moduleIcon"></span>组件
            </div>
            <div class="emoji" @click="emojiClick">
              <span class="emojiIcon"></span>emoji
            </div>
            <picManagement
              v-if="!ruleForm.activity_url2" ref="picmanagementRef"
              @succ="handlePicManagement" @onOpen="showEmojis = false"
            >
            </picManagement>
            <div v-if="!replyFlag" v-auth="'comment_operation:ai_generate'" class="AI">
              <div class="aiAction" @click="getComments">
                <span class="aiIcon"></span>AI辅助撰写
              </div>
            </div>
          </div>
          <!-- emoji表情 -->
          <Picker
            v-show="showEmojis" :data="emojiIndex" set="apple" :i18n="I18n" :show-preview="false"
            :show-search="false" :emoji-tooltip="false" :show-skin-tones="false" :auto-focus="true" @select="showEmoji"
          />
          <!-- 组件预览 -->
          <div v-if="module.moduleBox" class="moduleBox">
            <el-icon class="icon1" @click="handleMouduleDel">
              <Delete />
            </el-icon>
            <iframe :src="ruleForm.activity_url2" frameborder="0" :height="module.ifHeight" scrolling="no"></iframe>
          </div>
        </el-form-item>
        <div class="commentBox">
          <div class="line"><i></i><span>评论人</span> </div>
          <div class="tabLine">
            <span v-show="commentSwitch" class="sel" @click="commentSwitch = false"><i></i>我的收藏</span>
            <span v-show="!commentSwitch" class="def" @click="commentSwitch = true"><i></i>随机生成</span>
          </div>
        </div>

        <div v-show="commentSwitch">
          <Commentator ref="commentatorRef" :is-circle="true" @selectCommentator="handleCommentator">></Commentator>
        </div>
        <div v-show="!commentSwitch">
          <myCollection
            ref="myCollectionRef" :is-circle="true"
            @selectMyCollection="handleMyCollection" @delMyCollection="handleDelMyCollection"
          ></myCollection>
        </div>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button @click="handleClose()">
            取消
          </el-button>
          <el-button :loading="loading" type="primary" @click="submitForm('ruleFormRef')">
            <i class="el-icon-search"></i>
            发送
          </el-button>
        </div>
      </template>
      <el-dialog
        v-model="module.moduleShow" title="互动组件" width="960" align-center
        :before-close="moduleCancel"
      >
      <interact v-if="module.moduleShow" ref="interactRef" @setModule="handleModule"></interact>
      </el-dialog>
      <AI :id="ruleForm.article_id" ref="ai" :is-circle="true" @setComment="choiseComment" />
      <CommentTime ref="commentTimeRef" :is-circle="true"></CommentTime>
</el-drawer>
</div>
</template>

<script setup name='reply'>
import CommentTime from '@/views/operationManagement/commentOpera/modules/comment/commentTime.vue'
import AI from '@/views/operationManagement/commentOpera/AI/index.vue'
import { Picker, EmojiIndex } from 'emoj-vue-chao/src'
import picManagement from '@/views/operationManagement/commentOpera/modules/picManagement.vue'
import Commentator from '@/views/operationManagement/commentOpera/modules/comment/commentator.vue'
import myCollection from '@/views/operationManagement/commentOpera/modules/comment/myCollection.vue'
import WeditorV5 from '@/components/weditor/v5.vue'
import interact from '@/components/interact/index.vue'
import data from '@/assets/google.json'
import 'emoj-vue-chao/css/emoji-mart.css'
import { requests } from '@/api/business/commentOpera'
import { ref, watch, nextTick, onMounted, computed, defineEmits, defineProps } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const props = defineProps({
  replyData: {
    type: Object
  },
  comment: {
    type: Object
  },
  replyHierarchy: {
    type: Object
  }
})
const emit = defineEmits(['replySuccess'])
const commentTimeRef = ref(null)
const interactRef = ref(null)
const replyFlag = ref(false)
const ai = ref(null)
const commentSwitch = ref(true)
const commentatorRef = ref()
const myCollectionRef = ref()
const module = ref({
  width: '',
  height: '',
  url: '',
  moduleShow: false,
  moduleBox: false,
  ifHeight: ''
})
const sensitive = ref('')
const ruleForm = ref({
  article_title: '',
  article_id: '',
  article_url: '',
  article_user_id: '',
  article_published_at: '',
  channel_id: '',
  channel_name: '',
  comment_content: '',
  comment_user_id: '',
  comment_user_name: '',
  list_pics: '',
  comment_user_portrait: '',
  comment_at: '',
  task_type: 1,
  id: '',
  activity_url2: '',
  activity_ratio_width: '',
  activity_ratio_height: '',
  expression_id: '',
  expression_url: '',
  component_type: '',
  project_id: ''
})
const I18n = ref({
  search: 'Search',
  notfound: 'No Emoji Found',
  categories: {
    recent: '常用',
    people: '人物'
  }
})
const emojiIndex = new EmojiIndex(data)
const showEmojis = ref(false)
const rules = ref({
  comment_content: [
    { required: true, message: '请输入评论内容', trigger: 'blur' | 'change' }
  ]
})
const loading = ref(false)
const drawer = ref(false)
const ruleFormRef = ref(null)
const picmanagementRef = ref(null)
// 组件列表
const moduleOptions = ref(
  [
    {
      label: '观点PK组件',
      value: 'dual_options_poll'
    },
    {
      label: '多选投票组件',
      value: 'more_options_poll'
    }
  ]
)
// 项目列表
const projectOptions = ref([])
const weditorRef = ref()
watch(drawer, newValue => {
  if (!newValue) {
    ruleFormRef.value.resetFields()
    commentatorRef.value.handleSwitch()
    commentSwitch.value = true
  } else {
    if (commentTimeRef.value) {
      commentTimeRef.value.taskType = 1
      commentTimeRef.value.comment_at = ''
    }

    // 创建赋值
    ruleForm.value.article_id = props.comment.id
    ruleForm.value.article_title = props.replyData.list_title
    ruleForm.value.article_url = props.replyData.url
    ruleForm.value.channel_id = props.replyData.channel_id
    ruleForm.value.channel_name = props.replyData.channel_name

    // 回复赋值
    ruleForm.value.second_comment_id = props.replyHierarchy.second_comment_id
    ruleForm.value.user_placeholder = props.replyHierarchy.user_placeholder
    ruleForm.value.parent_comment_id = props.replyHierarchy.parent_comment_id
    ruleForm.value.top_comment_id = props.replyHierarchy.top_comment_id
    ruleForm.value.comment_level = props.replyHierarchy.comment_level
    ruleForm.value.content = props.replyHierarchy.content
    ruleForm.value.article_user_id = props.replyData.account_id

  }
})
// 富文本内容
const handleContentVal = val => {
  ruleForm.value.comment_content = val
}

// 随机评论事件
const handleCommentator = () => {
  myCollectionRef.value.vestList.forEach(v => { v.isClick = false })
}
// 收藏评论点击事件
const handleMyCollection = () => {
  commentatorRef.value.vestList.forEach(v => { v.isClick = false })
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
}
// 收藏马甲号删除事件
const handleDelMyCollection = () => {
  myCollectionRef.value.commentInfo = {
    comment_user_id: '',
    comment_user_name: '',
    comment_user_portrait: '',
    comment_user_location: ''
  }
}
// 自动生成评论
function getComments () {
  if (!ai.value.showCard) {
    ai.value.show()
  } else {
    ai.value.hide()
  }
}
// 选择评论
function choiseComment (val) {
  weditorRef.value.changeText = true
  let content = removeTagsExceptA(ruleForm.value.comment_content.trim())
  if (content && content != '<br>') {
    ElMessageBox.confirm(
      '您当前有评论内容！替换后，原评论内容将清空。',
      '确定替换当前评论内容？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        ruleForm.value.comment_content = val
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消'
        })
      })
  } else {
    ruleForm.value.comment_content = val
  }
}
// 预设 本地传值
function handlePicManagement(url, id) {
  ruleForm.value.expression_id = id
  ruleForm.value.expression_url = url
}
// 选择组件类型
function componentTypeChage (val) {
  if (val) {
    getModelsList(val)
  }
}
// 获取组件列表
function getModelsList (val) {
  requests.getModelsList({ component_type: val }).then(res => {
    projectOptions.value = res.data.list
  })
}

// 取消关联组件
function moduleCancel () {
  module.value.moduleShow = false
  ruleForm.value.project_id = ''
  ruleForm.value.component_type = ''
}
// 展示组件
function showModel() {
  module.value.moduleShow = true
  showEmojis.value = false
}
// 删除组件
function handleMouduleDel () {
  ElMessageBox.confirm(
    '是否删除当前组件，删除后不可恢复',
    '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'Warning',
      dangerouslyUseHTMLString: true,
      center: true
    }
  ).then(() => {
    module.value.moduleBox = false
    ruleForm.value.activity_url2 = ''
    ruleForm.value.project_id = ''
    ruleForm.value.component_type = ''
    ruleForm.value.comment_content = ''
  })
}
// 插入组件
function handleModule (data) {
  weditorRef.value.changeText = true
  ruleForm.value.activity_url2 = data.url
  ruleForm.value.comment_content = data.project_name
  ruleForm.value.project_id = data.project_id || data.id
  ruleForm.value.component_type = data.component_type
  module.value.moduleShow = false
  module.value.moduleBox = true
  if (data.component_type == 'more_options_poll') {
    module.value.ifHeight = data.ratio_height
  } else {
    module.value.ifHeight = 120
    ruleForm.value.component_type = 'dual_options_poll'
  }

}
// 展示emoji
function showEmoji (emoji) {
  // ruleForm.value.comment_content += emoji.native
  weditorRef.value.editorRef.focus()
  weditorRef.value.editorRef.insertText(emoji.native)
}

// 关闭窗口
function handleClose () {
  var scrollBox = document.getElementsByClassName('el-drawer__body')[0]
  scrollBox.scrollTo(0, 0)
  ai.value.hide()
  commentatorRef.value.vestSearch.nick_name = ''
  commentatorRef.value.dhtVal = []
  commentatorRef.value.addressVal = ''
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
  myCollectionRef.value.vestList.forEach(v => { v.isClick = false })
  myCollectionRef.value.commentInfo = {
    comment_user_id: '',
    comment_user_name: '',
    comment_user_portrait: '',
    comment_user_location: ''
  }
  drawer.value = false
  sensitive.value = ''
  showEmojis.value = false
  module.value.moduleBox = false
  module.value.ifHeight = 120
  ruleForm.value.activity_url2 = ''
  ruleForm.value.component_type = ''
  ruleForm.value.project_id = ''
  ruleForm.value.second_comment_id = ''
  ruleForm.value.article_user_id = ''
  loading.value = false
  replyFlag.value = false
  if (picmanagementRef.value) {
    picmanagementRef.value.presetData = {
      activeTab: 'tab1',
      imgValue: '',
      imgBox: false,
      cover: [],
      selectId: '',
      imgList: []
    }
    picmanagementRef.value.showCustom = false
  }

}

// 提交 检测敏感词
function submitForm(formName) {
  if (commentTimeRef.value.taskType == 2) {
    ruleForm.value.comment_at = commentTimeRef.value.comment_at
    ruleForm.value.task_type = commentTimeRef.value.taskType
  } else {
    getNowTime()
  }

  // 必填效验
  ruleFormRef.value.validate(valid => {
    console.log(valid)
    if (valid) {
      // loading 节流控制
      loading.value = true
      // 敏感词接口
      requests.checkSensitiveWord({
        comment_content: ruleForm.value.comment_content.replace(/<[^>]+>/g, '')
      }).then(res => {
        // 校验
        if (res.code == 0) {
          // 存在敏感词
          if (res.data.result.length) {
            // 提取敏感词数组
            let detectResult = res.data.result
            let arr = []
            for (let i = 0; i < detectResult.length; i++) {
              arr.push(detectResult[i].content)
            }
            const sensitiveText = arr.toString()
            sensitive.value = sensitiveText
            // 如果有敏感词
            ElMessageBox.confirm(
              `发现<b style="color:red;">${sensitiveText}</b>为敏感词，是否继续？`,
              '提示', {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'Warning',
                dangerouslyUseHTMLString: true,
                center: true
              }
            )
              .then(() => {
                // 忽略敏感词 继续提交
                submitInit()
                sensitive.value = ''

              })
              .catch(() => {
                loading.value = false
                ElMessage({
                  type: 'info',
                  message: '已取消！'
                })
              })
          } else {
            // 没有敏感词
            console.log('没有敏感词')
            submitInit()
          }
        } else {
          loading.value = false
          ElMessage({
            type: 'error',
            message: '敏感词校验接口失败！'
          })
        }
      })
        .catch(error => {
          loading.value = false
        })

    } else {
      return false
    }
  })
}

function getNowTime () {
  // 效验评论时间
  if (ruleForm.value.task_type == 1) {
    const a = new Date().getTime() // 获取到当前时间戳
    const b = new Date(a)
    ruleForm.value.comment_at = nowDate(b)
  }
}
function nowDate (now) {
  let year = now.getFullYear() // 年份
  let month = now.getMonth() + 1 // 月份（0-11）
  let date = now.getDate() // 天数（1到31）
  let hour = now.getHours() // 小时数（0到23）
  let minute = now.getMinutes() // 分钟数（0到59）
  let second = now.getSeconds() // 秒数（0到59）
  month < 10 ? month = '0' + month : month
  date < 10 ? date = '0' + date : date
  hour < 10 ? hour = '0' + hour : hour
  minute < 10 ? minute = '0' + minute : minute
  second < 10 ? second = '0' + second : second
  return (
    year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
  )
}
// 去除html标签 A除外
function removeTagsExceptA (html) {
  // 删除除了a标签以外的所有HTML标签
  if (weditorRef.value.editorRef.getText().length > 0) {
    // 匹配所有<p>标签对
    const pTagRegex = /<p[^>]*>[^]*?<\/p>/gi
    const pTags = html.match(pTagRegex) || []
    // 如果只有一个<p>标签对，删除所有<p>标签对
    if (pTags.length === 1) {
      return html.replace(pTagRegex, match => match.replace(/<\/?p[^>]*>/g, ''))
    }
    // 正则表达式匹配<p>和<a>标签对及其内容
    const regex = /<(p|a)(\s[^>]*)?>[^]*?<\/\1>|<(\/?)(p|a)(\s[^>]*)?>/gi
    let result = html.replace(regex, (match, p1, p2, p3, p4, p5) => {
      // 如果是<p>或<a>标签，保留
      return match
    })
    // 删除其他所有标签
    result = result.replace(/<\/?(?!p|a)\b[^>]*>/gi, '')
    return result
  }
}
// 打开emoji弹窗
function emojiClick() {
  showEmojis.value = !showEmojis.value
  picmanagementRef.value.presetData.imgBox = false
  picmanagementRef.value.showCustom = false
}
function submitInit () {
  if (commentTimeRef.value.taskType == 2) {
    ruleForm.value.task_type = 2
  }
  // 马甲号插入
  if (!commentSwitch.value) {

    ruleForm.value.comment_user_id = myCollectionRef.value.commentInfo.comment_user_id
    ruleForm.value.comment_user_name = myCollectionRef.value.commentInfo.comment_user_name
    ruleForm.value.comment_user_portrait = myCollectionRef.value.commentInfo.comment_user_portrait
    ruleForm.value.comment_user_location = myCollectionRef.value.commentInfo.comment_user_location
  } else {
    ruleForm.value.comment_user_id = commentatorRef.value.commentInfo.comment_user_id
    ruleForm.value.comment_user_name = commentatorRef.value.commentInfo.comment_user_name
    ruleForm.value.comment_user_portrait = commentatorRef.value.commentInfo.comment_user_portrait
    ruleForm.value.comment_user_location = commentatorRef.value.commentInfo.comment_user_location
  }
  // 去除内容a标签以外的html标签
  ruleForm.value.comment_content = removeTagsExceptA(ruleForm.value.comment_content)
  if (replyFlag.value) {
    // 二级三级回复
    requests.vest_comment_reply(ruleForm.value).then(res => {
      if (res.code == 0) {
        loading.value = false

        ElMessage({
          message: '回复成功',
          type: 'success'
        })
        clear()
        commentatorRef.value.handleSwitch()
        emit('replySuccess')
      }

    }).catch(e => {
      console.log(e)
      loading.value = false
    })
  } else {
    // 新增
    requests.taskSave(ruleForm.value).then(res => {
      if (res.code == 0) {
        loading.value = false
        ruleForm.value.task_id = res.data.comment_task.id
        ElMessage({
          message: '添加任务成功',
          type: 'success'
        })
        clear()
        commentatorRef.value.handleSwitch()
        emit('replySuccess')
      }
    }).catch(() => {
      loading.value = false
    })
  }

}
// 成功后push到临时表单
function clear () {
  ai.value.hide()
  // 还原部分数据
  ruleFormRef.value.resetFields()
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
  commentatorRef.value.dhtVal = []
  commentatorRef.value.addressVal = ''
  commentatorRef.value.vestSearch.nick_name = ''
  commentatorRef.value.vestSearch.location = ''
  ruleForm.value.comment_content = ''
  ruleForm.value.comment_at = '',
  ruleForm.value.task_type = 1
  ruleForm.value.task_id = ''
  sensitive.value = ''
  showEmojis.value = false
  module.value.moduleBox = false
  module.value.ifHeight = 120
  ruleForm.value.activity_url2 = ''
  ruleForm.value.expression_id = ''
  ruleForm.value.expression_url = ''
  ruleForm.value.component_type = ''
  ruleForm.value.project_id = ''
  ruleForm.value.article_user_id = ''
  if (picmanagementRef.value) {
    picmanagementRef.value.presetData = {
      activeTab: 'tab1',
      imgValue: '',
      imgBox: false,
      cover: [],
      selectId: '',
      imgList: []
    }
  }
  drawer.value = false

}
defineExpose({
  drawer,
  ruleForm,
  replyFlag
})
</script>

<style>
.el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 455px !important;
}
.emoji-mart {
  width: 400px !important;
  margin-top: 10px !important;
}
 .topReplyText a:-webkit-any-link {
    color: #0B82FD;
    text-decoration: none;
}
.w-e-text-container{
  padding:10px 0px !important;
}
.w-e-text-container p {
  margin:5px 0 !important
}
</style>
<style lang="scss" scoped>
.closeCircle{
  width: 16px;
  height: 16px;
  position: absolute;
  right: 12px;
  top: 8px;
  background: url("@/assets/images/circle/closeCircle.png") no-repeat;
  background-size: 16px auto;
  cursor: pointer;
}
.reply {
  font-size: 14px;
}

.floating {
  width: 95%;
  height: 60px;
  position: absolute;
  bottom: 3%;
  left: 3%;
  margin: auto;
  cursor: pointer;
  background: url("@/assets/images/circle/bottom.png") no-repeat center center / 100% auto;
}

.topReplyText {
  font-size: 13px;
  color: rgba(51, 51, 51, 0.8);
  line-height: 18px;
  margin-bottom: 10px;
  min-height: 22px;
  display: flex;
  align-items: flex-start;

}
.reply-content {
  flex: 1;
  margin-left: 8px;
  min-height: 0;
  line-height: 24px;
  word-break: break-all;
  margin-top: -3px;
}

/* 如果内容是 <p> 标签，可能需要额外的样式来确保顶部对齐 */

.aiAction {
  float: right;
  height: 26px;
  line-height: 26px;
  background: linear-gradient(90deg, #b6e7ff 0%, #94b7ff 100%);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  cursor: pointer;
  margin-left: 10px;
  margin-top: 4px;

  .aiIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/AI.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 5px;
    margin-right: 5px;
  }
}

.commentMore {
  height: 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid #f8f8f8;

  span {
    float: left;
    width: 100px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid #f8f8f8;
  }

  .act {
    color: #fff;
    background-color: #0b82fd;
  }
}

.noComment {
  width: 100%;
  height: 30px;
  background-color: #f8f8f8;
  text-align: center;
  line-height: 30px;
  color: #bebebe;
  margin-bottom: 30px;
  font-size: 14px;
  margin-top: -10px;
}

.module {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  cursor: pointer;
  margin-right: 6px;

  .moduleIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/module.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 4px;
    margin-right: 5px;
  }
}

.emoji {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  margin-right: 5px;
  cursor: pointer;

  .emojiIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/emoji.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 7px;
    margin-right: 5px;
  }
}

.line {
  height: 35px;
  line-height: 35px;
  position: relative;
  margin-bottom: 15px;
  border-radius: 2px;

  i {
    position: absolute;
    left: 0;
    top: 10px;
    width: 5px;
    height: 16px;
    background-color: #46a5e2;
  }

  span {
    font-size: 13px;
    position: absolute;
    left: 15px;
  }
}

.AI {
  position: absolute;
  top: 187px;
  right: 0;
}

.el-icon-search {
  width: 23px;
  height: 21px;
  background: url("@/assets/images/circle/fly.png") no-repeat;
  background-size: 21px auto;
}

.comment-content {
  display: flex;
  align-items: center;
}

.comment-image {
  width: 30px;
  height: 30px;
}

.comment-text {
  flex: 1;
}

.commentBox {
  height: 40px;
  position: relative;
  overflow: hidden;
  margin-bottom: 10px;

  .tabLine {
    position: absolute;
    right: 0px;
    top: 0;
    overflow: hidden;

    span {
      cursor: pointer;
      float: left;
      height: 26px;
      line-height: 26px;
      display: block;
      text-align: center;
      font-size: 12px;
      border-radius: 13px;
      padding: 0 15px;
    }

    i {
      float: left;
      width: 15px;
      height: 14px;
      margin: 6px 5px 0 0;
    }

    .def {
      background: rgba(11, 130, 253, 0.05);
      color: #0b82fd;

      i {
        background: url("@/assets/images/comment_icon.png") no-repeat;
        background-size: 15px auto;
      }
    }

    .sel {
      background: rgba(11, 130, 253, 0.05);
      color: #0b82fd;

      i {
        background: url("@/assets/images/collection_icon.png") no-repeat;
        background-size: 15px auto;
      }
    }
  }
}

.emoji-mart-category-label {
  display: none;
}

.icon {
  position: absolute;
  right: 20px;
  top: 120px;
  cursor: pointer;
}

.comment-center {
  padding: 20px 24px;
  overflow-y: auto;
  font-size: 16px;
  line-height: 30px;
  height: 500px;

  .child:hover {
    background: #c6e2ff;
    border-radius: 3px;
    cursor: pointer;
  }
}

.moduleBox {
  clear: both;
  width: 375px;
  position: relative;
  margin-top: 20px;

  .icon {
    position: absolute;
    right: -50px;
    top: 5px;
    z-index: 99;
    cursor: pointer;
  }

  .icon1 {
    position: absolute;
    right: -30px;
    top: 5px;
    z-index: 99;
    cursor: pointer;
  }

  iframe {
    width: 375px;
    position: relative;
    left: 0;
    top: 0;
    z-index: 1;
  }
}
</style>
