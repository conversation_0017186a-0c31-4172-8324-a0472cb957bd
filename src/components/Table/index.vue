<template>
  <div id="tabel-component" style="height: 100%;">
    <el-table :data="data.list" :height="data.height">
      <el-table-column
        v-for="(item, index) in props.table.columns" :key="index" v-loading="loading" :label="item.label"
        :width="item.width"
      >
        <template #default="scope">
          <span v-if="!item.switch && !item.link">
            {{ item.options ? handelTranslate(scope.row[item.name], item.options) : item.time ?
              handleTime(scope.row[item.name]) : scope.row[item.name] }}
          </span>
          <span v-else-if="item.link && !item.switch" style="cursor: pointer;color: #0b82fd" @click="item.func(scope.row)">
            {{ scope.row[item.name] || 0 }}
          </span>
          <span v-else>
            <el-switch
              v-model="scope.row[item.name]" v-auth="item.auth" active-color="#13ce66" inactive-color="#ff4949"
              :active-value="item.switchActive || 1" :inactive-value="item.switchInactive || 0"
              @change="item.fun(scope.row)"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column v-if="props.table.operation" label="操作栏" :width="props.table.operationWidth">
        <template #default="scope">
          <el-button
            v-for="(item, index) in props.table.operation" :key="index" v-auth="item.auth" type="text"
            size="small" :disabled="isDisabled(scope.row, item.disabled)" @click="item.fun(scope.row)"
          >
            <span class="button_text">{{ item.label }}</span>
          </el-button>
          <el-popover v-if="props.table.moreOperation" placement="left" width="50px" trigger="click">
            <template #reference>
              <el-button type="text" size="small">
                更多
              </el-button>
            </template>
            <el-button
              v-for="(item, index) in props.table.moreOperation" :key="index" v-auth="item.auth" type="text"
              size="small" :disabled="isDisabled(scope.row, item.disabled)" @click="item.fun(scope.row)"
            >
              {{ item.label }}
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      small background class="pagin" :current-page="data.current" :page-sizes="[10, 20, 50, 100]"
      :page-size="data.size" layout="total,sizes, prev, pager, next,jumper" :total="data.total"
      @size-change="handleSizeChange" @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup>
import { getTabel } from '@/api/components'
import { nextTick, onMounted, ref } from 'vue'
import {
  Delete
} from '@element-plus/icons-vue'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
// table参数：
// columns  列信息  (label 表头名、 name 字段名、 options 字段翻译、 switch 开关、fun 回调函数、 switchActive 默认开启参数、switchInactive 开关关闭参数  )
// path  接口名称
// backName 返回参数名称
// operationWidth 操作栏固定宽度
// operation 操作栏 (label 标签名、 fun 回调方法、 disabled 展示条件)
// operation 操作栏更多选项 (label 标签名、 fun 回调方法、 disabled 展示条件)

// search 搜索信息
const props = defineProps({
  table: Object
})
const data = ref({
  list: [

  ],
  loading: false,
  current: 1,
  size: 10,
  total: 0,
  height: null
})
const search = ref({})
onMounted(() => {
  init()
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName(
    'el-pagination'
  )[0].offsetHeight
  data.value.height = height - bottom
})
// 翻译函数
function handelTranslate (name, options) {
  let data = options.filter(v => v.label == name)
  if (data.length) {
    return data[0].value
  }
  return name
}
// 时间转换
function handleTime (time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s

}
// 是否可点击
function isDisabled (row, disabled) {
  let state = false
  for (let i in disabled) {
    if (disabled[i].indexOf(row[i]) != -1) {
      state = true
    }
  }
  return state
}
// 初始化
function init (requestData = search.value) {
  let dataObj = Object.assign({}, requestData)
  dataObj.current = data.value.current
  dataObj.size = data.value.size
  data.value.loading = true
  getTabel(props.table.path, dataObj).then(res => {
    console.log(res.data[props.table.backName])
    data.value.loading = false
    data.value.list = res.data[props.table.backName].records
    data.value.total = res.data[props.table.backName].total
  }).catch(v => {

    data.value.loading = false
  })
}
// 换页
function handleCurrentChange (val) {
  data.value.current = val
  init()
}
// 换一页行数
function handleSizeChange (val) {
  data.value.size = val
  data.value.current = 1
  init()
}
// 搜索
function handleSearch (val) {
  data.value.current = 1
  search.value = val
  init()
}
// 暴露参数
defineExpose({
  init,
  handleSearch
})
</script>
<style lang="scss" scoped>
#tabel-component {
  height: 100%;

  .pagin {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}
</style>
