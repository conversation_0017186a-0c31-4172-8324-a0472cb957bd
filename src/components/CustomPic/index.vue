<template>
  <div class="picBox" v-loading="loading" element-loading-text="等待图片审核中...">
    <div class="header">
      <el-upload
        ref="upload"
        :before-upload="beforeUpload"
        :limit="1"
        :accept="'.jpg,.jpeg,.png,.gif'"
      >
        <div class="add"  v-if="!isdel">添加</div>
      </el-upload>

      <div class="del" v-if="!isdel" @click="isdel = true"></div>
      <div class="add" style="padding: 0" v-else @click="endDel">完成</div>
    </div>
    <div class="bodys">
      <div
        class="child"
        v-for="item in imgList"
        :key="item.image_collection_id"
        @click="selsect(item)"
      >
        <div :class="setClass(item.image_collection_id)" v-if="isdel"></div>
        <img :src="item.image_url" alt="" />
      </div>
    </div>
    <div class="footer" v-if="isdel">
      <span @click="confirmDel"
        >删除 <span v-if="delList.length > 0">({{ delList.length }})</span></span
      >
    </div>
  </div>
  <cropper
    ref="cropperRef"
    :img-obj="addImg"
    @saveData="handleSaveData"
    @fixedNumber="handleFixed"
  ></cropper>
</template>
<script setup name="customPic">
import cropper from '@/components/cropper/index.vue'
import { requests } from '@/api/business/emote'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, onMounted } from 'vue'
const emit = defineEmits(['onSelect'])

onMounted(() => {
  getList()
})
const imgList = ref([])
const addImg = ref({
  url: ''
})
const loading = ref(false)
const beforeImg = ref('')
const scale = ref('')
const cropperRef = ref(null)
const isdel = ref(false)
const delList = ref([])
// 获取收藏图片列表
function getList() {
  requests.collectList({size: 30}).then(res => {
    console.log(res)
    imgList.value = res.data.page.records
    console.log(imgList.value)
  })
}

// 设置勾选或不勾选的class

function setClass(id) {
  let data = delList.value.filter(v => {
    return id === v
  })
  if (data.length) {
    return 'check'
  } else {
    return 'none'
  }
}
// 开启删除状态
function endDel() {
  isdel.value = false
  delList.value = []
}
// 添加或移除选择删除数据
function selsect(val) {
  if (isdel.value) {
    let data = delList.value.filter(v => {
      return val.image_collection_id === v
    })
    if (data.length) {
      delList.value = delList.value.filter(v => {
        return val.image_collection_id !== v
      })
    } else {
      delList.value.push(val.image_collection_id)
    }
  } else {
    emit('onSelect', val, scale.value)
  }
}
// 删除
function confirmDel() {
  if(delList.value.length === 0){
    ElMessage.error('请选择要删除的图片')
    return
  }
  requests
    .collectDel({
      uuIds: delList.value.join(',')
    })
    .then(res => {
      delList.value = []
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      isdel.value = false
      getList()
    })
}
function save(url) {
  requests
    .collectSave({
      image_url: url
    })
    .then(res => {
      console.log(res)
      getList()
    })
}
// 保存
// 上传前的验证
const beforeUpload = file => {
  if (imgList.value.length >= 30) {
    ElMessage.error('上传数量超出限制')
    return
  }
  const isJpgOrPng =
    file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
  const isLt10K = file.size < 500 * 1024

  if (isJpgOrPng && isLt10K) {
    const formData = new FormData()
    formData.append('file', file) // 将文件添加到表单中
    console.log(formData, 'formData')
    requests.upload_expression(formData).then(res => {
      if (res.code === 0) {
        addImg.value.url = res.data.url
        if (file.type === 'image/jpeg' || file.type === 'image/png') {
          cropperRef.value.dialogVisible = true
        } else {
          handleSaveData(formData)
        }
      } else {
        ElMessage.error('图片上传失败')
      }
    })
    return false
  }

  if (!isJpgOrPng) {
    ElMessage.error('只能上传 JPG、PNG、gif格式的图片')
  }
  if (!isLt10K) {
    ElMessage.error('图片大小不能超过 500KB')
  }
  return isJpgOrPng && isLt10K
}
// 传截图数据
const handleSaveData = formData => {
  requests.upload_expression(formData).then(res => {
    if (res.code === 0) {
      beforeImg.value = res.data.url
      loading.value = true
      cropperRef.value.dialogVisible = false
      requests.audit_sync({ image_url: beforeImg.value }).then(v => {
        if (v.data.resultCode === 0) {
          loading.value = false
          ElMessage.success(v.data.resultMsg)
          cropperRef.value.dialogVisible = false
          // 保存图片
          save(beforeImg.value)
        } else {
          ElMessageBox.confirm(
            '<span style="color: red;">' + v.data.resultMsg + '</span> 是否确认上传?',
            {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          )
            .then(() => {
              loading.value = false
              cropperRef.value.dialogVisible = false
              // 保存图片
              save(beforeImg.value)
            })
            .catch(() => {
              loading.value = false
            })
        }
      })
    } else {
      ElMessage.error('图片上传失败')
    }
  })
}
// 取比例
const handleFixed = v => {
  scale.value = v
}
defineExpose({
  getList
})
</script>
<style lang="scss" scoped>
.picBox {
  width: 320px;
  height: 426px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  margin-top: 30px;
  .header {
    width: 100%;
    height: 28px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    align-items: center;
    border-bottom: 1px solid #efefef;
    .add {
      font-size: 12px;
      color: #0b82fd;
      font-weight: 400;
      cursor: pointer;
      padding-top: 10px;
    }
    .del {
      width: 14px;
      height: 14px;
      background: url(@/assets/images/customPic/del.png) no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
  .bodys {
    box-sizing: border-box;
    width: 320px;
    height: 370px;
    padding: 8px 15px 10px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .child {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      margin-right: 10px;
      margin-bottom: 10px;
      background: #ccc;
      position: relative;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .check {
        width: 14px;
        height: 14px;
        top: 2px;
        right: 2px;
        position: absolute;
        background: url(@/assets/images/customPic/check.png) no-repeat center center;
        background-size: 100% 100%;
      }
      .none {
        width: 14px;
        height: 14px;
        top: 2px;
        right: 2px;
        position: absolute;
        background: url(@/assets/images/customPic/none.png) no-repeat center center;
        background-size: 100% 100%;
      }
    }
    .child:nth-child(5n) {
      margin-right: 0;
    }
  }
  .footer {
    width: 100%;
    height: 28px;
    border-top: 1px solid #efefef;
    padding: 0 15px;
    display: flex;
    justify-content: right;
    line-height: 28px;
    font-size: 12px;
    color: #ff3636;
    span {
      cursor: pointer;
    }
  }
}
</style>
