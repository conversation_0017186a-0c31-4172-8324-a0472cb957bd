<template>
  <page-main>
    <headModel ref="searchBox" @search="handleSearch" @add="handleAdd" />
    <div v-if="tableShow">
<div id="tabel-component">
      <el-table
        v-loading="loading"
        :data="data.records"
        height="578"
        highlight-current-row
>
<el-table-column
          prop="project_name"
          label="组件标题"
          min-width="330"
        />
        
        <el-table-column prop="created_at_as_string" label="创建时间" width="180" />

        <el-table-column v-slot="scope" align="left" label="操作" width="150" fixed="right">
          <el-button
            v-auth="'dual_option_poll_project:update'"
            :disabled="scope.row.is_used !== 0"
            text
            style="padding-left: 0"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            :disabled="scope.row.is_used !== 0"
            text
            style="margin-left: 0;"
            @click="handleModule(scope.row)"
          >
            选用
          </el-button>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        small
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :page-size="data.size"
        :current-page="data.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
    <Add ref="add" :data="editData" @init="handleSearch" />
  </page-main>
</template>
<script setup>
import Add from './modules/add.vue'
import headModel from './modules/headModule.vue'
import { ref, onMounted } from 'vue'
import { requests } from '@/api/business/interact'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
const emit = defineEmits(['setModule'])
const route = useRoute(),
  router = useRouter()
const add = ref(null)
const searchBox = ref(null)
const tableShow = ref(true)
const loading = ref(false)
const datail = ref(null)
const data = ref({
  height: 0,
  records: [],
  total: 0,
  size: 10,
  current: 1,
  used: 0,
  start_time: '',
  end_time: '',
  project_name: '',
  article_title: ''
})
const editData = ref({})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  console.log(document.getElementsByClassName('el-pagination'))
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 20
  data.value.height = height - bottom
  init()
})
// 初始化
function init() {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    start_time: data.value.start_time,
    end_time: data.value.end_time,
    project_name: data.value.project_name,
    article_title: data.value.article_title,
    used: data.value.used
  }
  loading.value = true
  requests
    .getMoreList(searchData)
    .then(res => {
      loading.value = false
      data.value.total = res.data.total
      data.value.records = res.data.records
    })
    .catch(() => {
      loading.value = false
    })
}
// 搜索
function handleSearch(val) {
  tableShow.value = true
  searchBox.value.tabAction = 0
  add.value.drawer = false
  data.value = {
    ...data.value,
    size: 100,
    current: 1,
    ...val
  }
  init()
}
// 添加
function handleAdd() {
  const defaultValues = {
    project_name: '',
    end_time: '',
    least: 1,
    atmost: 2,
    ratio_width: 375,
    more_option_poll_viewpoint_dtos: [
      { point_name: '' },
      { point_name: '' }
    ]
  }

  add.value.drawer = true
  tableShow.value = false

  for (const key in defaultValues) {
    add.value.ruleForm[key] = defaultValues[key]
  }

}
// 编辑
function handleEdit(val) {
  requests.getMoreDetail({ project_id: val.id }).then(res => {
    let arr = []
    res.data.more_option_poll_viewpoint_list.forEach(v => {
      arr.push({
        id: v.id,
        point_name: v.point_name
      })
    })
    editData.value = {
      id: res.data.id,
      project_name: res.data.project_name,
      end_time: handleTime(res.data.end_time),
      least: res.data.least,
      atmost: res.data.atmost,
      more_option_poll_viewpoint_dtos: arr
    }
    add.value.isEdit = true
    add.value.drawer = true
    searchBox.value.tabAction = 1
    tableShow.value = false
  })
}
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
// 选用
function handleModule(row) {
  ElMessageBox.confirm('是否插入组件？', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  })
    .then(() => {
      emit('setModule', row)
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}

// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
</script>
<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: 100%;
  padding: 0;
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.back {
  width: 150px;
  display: flex;
  margin-bottom: 20px;
  cursor: pointer;
  align-items: center;

  img {
    width: 14px;
    height: 15px;
    margin-right: 3px;
  }

  font-size: 14px;
  font-weight: 400;
  color: #0b82fd;
  line-height: 13px;
}
</style>
