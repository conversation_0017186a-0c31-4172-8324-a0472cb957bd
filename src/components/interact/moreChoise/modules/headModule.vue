<template>
  <div class="stateBox">
    <div :class="tabAction === 0 ? 'child act' : 'child'" @click="changeUse(0)">待选用</div>
    <div :class="tabAction === 1 ? 'child act' : 'child'" @click="changeUse(1)">创建</div>
  </div>
</template>
<script setup>
import {
  ref,
  onMounted,
  defineEmits,
  defineExpose
} from 'vue'
// 组件接收事件
const emit = defineEmits(['search', 'add'])

// 时间选择器
const time = ref(null)
const tabAction = ref(0)

function changeUse(val) {
  if (val == 0) {
    tabAction.value = 0
    handSearch()
  } else {
    tabAction.value = 1
    emit('add')
  }
}

// 搜索参数
const searchData = ref({
  used: 0,
  comment_search_type: 'article_title',
  searchword: null // 关键词
})

const handSearch = () => {
  let data = {}

  if (time.value && time.value.length) {
    data.start_time = time.value[0],
    data.end_time = time.value[1]
  } else {
    data.start_time = '',
    data.end_time = ''
  }
  searchData.value.comment_search_type === 'article_title' ? data.project_name = '' : data.article_title = ''
  data.used = searchData.value.used
  data[searchData.value.comment_search_type] = searchData.value.searchword
  emit('search', data)
}

defineExpose({
  tabAction
})

</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
  background-color: #ebebeb;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    .el-button {
      float: left;
      margin-right: 10px;
    }

    .el-select {
      width: 110px;
      float: left;
      margin-right: 10px;
    }
  }

  .right {
    &>.el-select {
      width: 110px;
    }
  }
}
.stateBox {
  width: 132px;
  height: 34px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  display: flex;
  padding: 3px;
  justify-content: space-between;
  margin-bottom: 15px;
  .child {
    width: 60px;
    height: 26px;
    background: #ffffff;
    font-size: 16px;
    color: #333333;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
  }
  .child.act {
    background: #eef5fe;
    color: #0b82fd;
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
