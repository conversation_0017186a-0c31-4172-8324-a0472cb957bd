<template>
  <div v-if="drawer">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" class="demo-ruleForm" :size="formSize" status-icon>
        <div class="label" style="margin-top:20px">
          <span></span> 标题编辑
          <div class="must">*</div>
        </div>
        <el-form-item prop="project_name">
          <el-input
v-model="ruleForm.project_name" :rows="5" maxlength="100" type="textarea" show-word-limit
            placeholder="请输入标题"
/>
        </el-form-item>
        <div class="label" style="margin-top: 30px">
          <span> </span> 选项编辑
          <div class="must">*</div>
        </div>
        <el-form-item prop="choise">
          <div class="choise-box">
            <div v-for="(item, index) in ruleForm.more_option_poll_viewpoint_dtos" :key="index" class="child">
              <div class="step">
                <div v-if="index === 0" class="up no"></div>
                <div v-else class="up" @click="upClick(item, index)"></div>
                <div v-if="index === ruleForm.more_option_poll_viewpoint_dtos.length - 1" class="down no"></div>
                <div v-else class="down" @click="downClick(item, index)"></div>
              </div>
              <el-input
v-model="item.point_name" show-word-limit placeholder="请输入选项内容" maxlength="38"
                style="width: 480px"
/>
              <div v-if="index > 1" class="del" @click="delClick(item)"></div>
            </div>
            <div v-if="ruleForm.more_option_poll_viewpoint_dtos.length < 15" class="add" @click="addItem">
              添加选项
            </div>
          </div>
        </el-form-item>
        <div class="label" style="margin-top: 30px">
          <span></span> 投票规则
          <div class="must">*</div>
        </div>
        <el-form-item prop="number">
          最少选择
          <el-input-number
v-model="ruleForm.least" :min="1" :max="ruleForm.more_option_poll_viewpoint_dtos.length"
            style="width: 150px;margin-right: 10px;margin-left: 10px;"
/>
          最多选择
          <el-input-number
v-model="ruleForm.atmost" :min="1" :max="ruleForm.more_option_poll_viewpoint_dtos.length"
            style="width: 150px;margin-left: 10px;"
/>
        </el-form-item>
        <div class="label" style="margin-top: 30px">
          <span></span> 投票时间
          <div class="must">*</div>
        </div>
        <el-form-item prop="end_time">
          <el-date-picker
v-model="ruleForm.end_time" type="datetime" placeholder="请选择结束时间" :disabled-date="disabledDate"
            value-format="YYYY-MM-DD HH:mm:ss" style="margin-right:10px"
/>
          <el-button plain size="small" @click="setTime(12)">
            12小时后结束
          </el-button>
          <el-button plain size="small" @click="setTime(24)">
            24小时后结束
          </el-button>
        </el-form-item>
      </el-form>
      <div style="position: relative;float: right;margin-top: 30px;">
          <el-button type="primary" @click="sureClick(ruleFormRef)">
            保存
          </el-button>
        </div>
  </div>
</template>
<script setup name="add">
import { ElMessage } from 'element-plus'
import { requests } from '@/api/business/interact'
import { ref, onMounted } from 'vue'
const emit = defineEmits(['init'])
const ruleFormRef = ref(null)

const ruleForm = ref({
  project_name: '',
  end_time: '',
  least: 1,
  atmost: 2,
  ratio_width: 375,
  more_option_poll_viewpoint_dtos: [
    {
      point_name: ''
    },
    {
      point_name: ''
    }
  ]
})
// 自定义选项填写校验规则
const validateChoise = (rule, value, callback) => {
  let num = 0
  ruleForm.value.more_option_poll_viewpoint_dtos.forEach(v => {
    if (!v.point_name) {
      num++
    }
  })
  if (num > 0) {
    callback(new Error('请输入选项内容'))
  } else {
    callback()
  }
}
// 自定义规则填写校验规则
const validateNumber = (rule, value, callback) => {

  if (!ruleForm.value.least || !ruleForm.value.atmost) {
    callback(new Error('请输入投票规则'))
  } else {
    callback()
  }
}
// 验证信息
const rules = ref({
  project_name: [
    {
      required: true,
      message: '请输入标题',
      trigger: 'blur'
    }
  ],
  choise: [
    { validator: validateChoise, trigger: 'blur|change' }
  ],
  number: [
    { validator: validateNumber, trigger: 'blur|change' }
  ],
  end_time: [
    {
      required: true,
      message: '请选择结束时间',
      trigger: 'change'
    }
  ]
})

// 传入的数据
const props = defineProps({
  data: Object
})
const drawer = ref(false)
const isEdit = ref(false)

// 快捷时间设置
function setTime(val) {
  const date = new Date()
  date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 60 * val)
  let y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? ('0' + m) : m
  let d = date.getDate()
  d = d < 10 ? ('0' + d) : d
  let h = date.getHours()
  h = h < 10 ? ('0' + h) : h
  let M = date.getMinutes()
  M = M < 10 ? ('0' + M) : M
  let s = date.getSeconds()
  s = s < 10 ? ('0' + s) : s
  let dateTime = y + '-' + m + '-' + d + ' ' + h + ':' + M + ':' + s
  ruleForm.value.end_time = dateTime
}
// 之前日期不能选
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7
}
function height() {
  // 初始高度
  let height = 54
  ruleForm.value.more_option_poll_viewpoint_dtos.forEach(v => {
    if (Math.ceil(v.point_name.length / 19) > 1) {
      height = height + 55
    } else {
      height = height + 37
    }
  })
  return height
}
// 确定
const sureClick = formEl => {
  // 表单验证
  if (!formEl) return
  formEl.validate((valid, fields) => {
    if (valid) {
      let fun = isEdit.value ? 'editMore' : 'creatMore'
      let data = Object.assign({}, ruleForm.value)
      // data.more_option_poll_viewpoint_dtos = JSON.stringify(data.more_option_poll_viewpoint_dtos)
      data.ratio_height = height()
      requests[fun](data).then(res => {
        ruleForm.value = {
          project_name: '',
          end_time: '',
          least: 1,
          atmost: 2,
          ratio_width: 375,
          more_option_poll_viewpoint_dtos: [
            {
              point_name: ''
            },
            {
              point_name: ''
            }
          ]
        }
        isEdit.value = false
        drawer.value = false
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        emit('init')
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 新增一条
function addItem() {
  if (ruleForm.value.more_option_poll_viewpoint_dtos.length < 15) {
    ruleForm.value.more_option_poll_viewpoint_dtos.push({
      point_name: ''
    })
    ruleForm.value.atmost = ruleForm.value.more_option_poll_viewpoint_dtos.length
  }

}
// 向上一位
function upClick(item, index) {
  let data = ruleForm.value.more_option_poll_viewpoint_dtos.filter(v => { return v != item })
  data.splice(index - 1, 0, item)
  ruleForm.value.more_option_poll_viewpoint_dtos = data
}
// 向下一位
function downClick(item, index) {
  let data = ruleForm.value.more_option_poll_viewpoint_dtos.filter(v => { return v != item })
  data.splice(index + 1, 0, item)
  ruleForm.value.more_option_poll_viewpoint_dtos = data
}
// 删除
function delClick(item) {
  console.log(item)
  if (!item.id) {
    let data = ruleForm.value.more_option_poll_viewpoint_dtos.filter(v => { return v != item })
    ruleForm.value.more_option_poll_viewpoint_dtos = data
    ruleForm.value.atmost = ruleForm.value.more_option_poll_viewpoint_dtos.length
  } else {
    requests.delMoreItem({ point_id: item.id }).then(res => {
      let data = ruleForm.value.more_option_poll_viewpoint_dtos.filter(v => { return v.id != item.id })
      ruleForm.value.more_option_poll_viewpoint_dtos = data
      ruleForm.value.atmost = ruleForm.value.more_option_poll_viewpoint_dtos.length
    })

  }

}

watch(
  () => props.data,
  (newValue, old) => {
    if (isEdit.value) {
      ruleForm.value = {
        ...ruleForm.value,
        ...newValue
      }
    }
  }
)
// 取消弹窗时
const close = formEl => {
  ruleForm.value = {
    project_name: '',
    end_time: '',
    least: 1,
    atmost: 2,
    ratio_width: 375,
    more_option_poll_viewpoint_dtos: [
      {
        point_name: ''
      },
      {
        point_name: ''
      }
    ]
  }
  ruleFormRef.value.resetFields()
  drawer.value = false
  isEdit.value = false
}

defineExpose({
  drawer,
  isEdit,
  ruleForm
})
</script>
<style lang="scss" scoped>
.label {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 14px;
  margin-top: 30px;

  span {
    width: 4px;
    height: 14px;
    background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
    border-radius: 3px;
    margin-right: 6px;
  }

  .must {
    color: var(--el-color-danger);
    margin-left: 4px;
  }
}

.choise-box {
  .child {
    display: flex;
    height: 30px;
    margin-bottom: 15px;
    align-items: center;

    .step {
      height: 100%;
      display: flex;
      align-items: center;
      margin-right: 10px;

      .up {
        width: 14px;
        height: 14px;
        margin-right: 10px;
        background: url(@/assets/images/interact/up.png) no-repeat center center / 100% auto;
        cursor: pointer;
      }

      .up.no {
        background: url(@/assets/images/interact/noUp.png) no-repeat center center / 100% auto;
      }

      .down {
        width: 14px;
        height: 14px;
        background: url(@/assets/images/interact/down.png) no-repeat center center / 100% auto;
        cursor: pointer;
      }

      .down.no {
        background: url(@/assets/images/interact/noDown.png) no-repeat center center / 100% auto;
      }
    }

    .del {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      background: url(@/assets/images/interact/delete.png) no-repeat center center / 100% auto;
      cursor: pointer;
    }
  }

  .add {
    font-weight: 400;
    font-size: 14px;
    color: #0B82FD;
    line-height: 14px;
    cursor: pointer;
    font-weight: 600;
    padding-left: 48px
  }
}
</style>
