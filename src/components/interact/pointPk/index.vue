<template>
<page-main>
    <headModel ref="searchBox" @search="handleSearch" @add="handleAdd" />
    <div v-if="tableShow">
    <div id="tabel-component">
      <el-table
        v-loading="loading"
        :data="data.records"
        height="578"
        highlight-current-row
>
<el-table-column
          prop="project_name"
          label="组件标题"
          min-width="330"
        />
        
        <el-table-column prop="create_time" label="创建时间" width="180" />

        <el-table-column v-slot="scope" align="left" label="操作" width="150" fixed="right">
          <el-button
            v-auth="'dual_option_poll_project:update'"
            :disabled="scope.row.is_used !== 0"
            text
            style="padding-left: 0"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            :disabled="scope.row.is_used !== 0"
            text
            style="margin-left: 0;"
            @click="handleModule(scope.row)"
          >
            选用
          </el-button>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        small
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :page-size="data.size"
        :current-page="data.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
    <Add ref="add" :data="editData" @init="handleSearch" />
  </page-main>
</template>
<script setup>
import Add from './modules/add.vue'
import headModel from './modules/headModule.vue'
import { ref, onMounted } from 'vue'
import { requests } from '@/api/business/interact'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
const route = useRoute(),
      router = useRouter()
const emit = defineEmits(['setModule'])
const searchBox = ref(null)
const tableShow = ref(true)
const add = ref(null)
const loading = ref(false)
const data = ref({
  height: 0,
  records: [],
  total: 0,
  size: 10,
  current: 1,
  used: 0,
  start_time: '',
  end_time: '',
  project_name: '',
  article_title: ''
})
const editData = ref({})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  console.log(document.getElementsByClassName('el-pagination'))
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 20
  data.value.height = height - bottom
  init()
})
// 初始化
function init() {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    start_time: data.value.start_time,
    end_time: data.value.end_time,
    project_name: data.value.project_name,
    article_title: data.value.article_title,
    used: data.value.used
  }
  loading.value = true
  requests
    .getPointList(searchData)
    .then(res => {
      loading.value = false
      data.value.total = res.data.total
      data.value.records = res.data.records
    })
    .catch(() => {
      loading.value = false
    })
}
// 搜索
function handleSearch(val) {
  tableShow.value = true
  searchBox.value.tabAction = 0
  add.value.drawer = false
  data.value = {
    ...data.value,
    size: 10,
    current: 1,
    ...val
  }
  init()
}
// 添加
function handleAdd() {
  const defaultValues = {
    component_type: 'dual_options_poll',
    project_name: '',
    option1_text: '',
    option1_color: '#FF4967',
    option2_text: '',
    option2_color: '#51ABFD'
  }

  add.value.drawer = true
  tableShow.value = false

  for (const key in defaultValues) {
    add.value.ruleForm[key] = defaultValues[key]
  }

}
// 编辑
function handleEdit(val) {
  editData.value = val
  add.value.isEdit = true
  add.value.drawer = true
  searchBox.value.tabAction = 1
  tableShow.value = false
}
// 选用
function handleModule(row) {
  ElMessageBox.confirm('是否插入组件？', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  })
    .then(() => {
      emit('setModule', row)
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}

// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
</script>
<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: 100%;
  padding: 0;
}
#tabel-component {
  height: 100%;
}
.el-table__footer-wrapper::before {
  display: none;
}
.page {
  display: flex;
  justify-content: center;
  height: 45px;
}
.back {
  width: 150px;
  display: flex;
  margin-bottom: 20px;
  cursor: pointer;
  align-items: center;
  img {
    width: 14px;
    height: 15px;
    margin-right: 3px;
  }
  font-size: 14px;
  font-weight: 400;
  color: #0b82fd;
  line-height: 13px;
}
</style>
