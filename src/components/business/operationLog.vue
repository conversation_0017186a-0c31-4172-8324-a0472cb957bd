<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    title="操作日志"
    size="700px"
  >
    <div
      style="margin-bottom: 20px;"
    >
      序号: {{ message.auto_pk }}
    </div>
    <div
      style="margin-bottom: 20px;"
    >
      评论内容:  <span v-html="message.content"></span>
    </div>
    <el-table :data="tableData" style="width: 100%;" height="70vh">
      <el-table-column prop="operation_formatter" label="操作时间" width="180" />
      <el-table-column prop="item_type_desc" label="操作事项" />
      <el-table-column prop="item_desc" label="事项说明" />
      <el-table-column prop="operation_person" label="操作人" width="180" />
    </el-table>
    <!-- <el-input v-model="input" placeholder="输入关键词" /> -->
    <template #footer>
      <div style="flex: auto;">
        <el-button
          type="primary"
          @click="sureClick"
        >
          关闭
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup name="operationLog">
import { ref, onMounted } from 'vue'
import { getHistory } from '@/api/components'
const drawer = ref(false)
const tableData = ref([])
const message = ref({})
function sureClick() {
  drawer.value = false
}
function getList() {
  getHistory({ id: message.value.id }).then(res => {
    tableData.value = res.data.list
    // if (res.data.list.length) {
    //   message.value.content = res.data.list[0].comment_content
    // } else {
    //   message.value.content = ''
    // }
  })
}
defineExpose({
  drawer,
  message,
  getList
})
</script>

