import { computed } from 'vue'
import { useStore } from 'vuex'

export function useAnnouncement() {
  const store = useStore()

  // 计算属性，获取公告的状态和内容
  const announcementVisible = computed(() => store.state.announcement.announcementVisible)
  const htmlContent = computed(() => store.state.announcement.htmlContent)
  const announcementTitle = computed(() => store.state.announcement.announcementTitle)
  const author = computed(() => store.state.announcement.author)
  const time = computed(() => store.state.announcement.time)

  // 方法，用于显示公告（通过 API 获取详细信息）
  const showAnnouncementWithApi = announcementId => {
    store.dispatch('announcement/showAnnouncementWithApi', announcementId)
  }

  // 方法，用于显示公告（直接设置内容）
  const showAnnouncementLocal = ({ title, content, author, time }) => {
    store.dispatch('announcement/showAnnouncement', { title, content, author, time })
  }

  // 方法，用于关闭公告
  const closeAnnouncement = () => {
    store.dispatch('announcement/closeAnnouncement')
  }

  return {
    announcementVisible,
    htmlContent,
    announcementTitle,
    author,
    time,
    showAnnouncementWithApi,
    showAnnouncementLocal,
    closeAnnouncement
  }
}
