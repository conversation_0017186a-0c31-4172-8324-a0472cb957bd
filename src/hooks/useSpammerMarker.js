import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { requests } from '@/api/business/auditManagement'

export function useSpammerMarker() {
  const dialogVisible = ref(false)
  const currentUser = ref('')
  const currentUserData = ref(null)

  const showConfirmDialog = (user) => {
    currentUser.value = user.user_name
    currentUserData.value = user
    dialogVisible.value = true
  }

  const confirmMarkAsSpammer = (callback) => {
    requests
      .markUserAsSpammer(currentUserData.value.user_id,currentUser.value)
      .then(() => {
        ElMessage({
          type: 'success',
          message: `用户 ${currentUser.value} 已被标记为灌水用户`
        })
        dialogVisible.value = false
        if (callback) callback(true) // 调用回调函数，传递成功状态
      })
      .catch(error => {
        console.error('标记用户失败:', error)
      })
  }

  const closeDialog = () => {
    dialogVisible.value = false
  }

  return {
    dialogVisible,
    currentUser,
    showConfirmDialog,
    confirmMarkAsSpammer,
    closeDialog
  }
}
