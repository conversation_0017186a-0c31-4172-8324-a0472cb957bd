import { computed } from 'vue'
import { useStore } from 'vuex'
import { markRaw } from 'vue'

export function useDrawer() {
  const store = useStore()

  // 获取抽屉的可见状态
  const drawerVisible = computed(() => store.state.drawer.visible)

  // 获取当前抽屉组件
  const currentDrawerComponent = computed(() => store.state.drawer.component)

  // 获取当前抽屉大小
  const drawerSize = computed(() => store.state.drawer.size)

  // 打开抽屉并设置内容
  const openDrawer = (component, size, componentProps = {}) => {
    store.commit('drawer/SET_SIZE', size)
    store.commit('drawer/SET_VISIBLE', true)
    store.commit('drawer/SET_COMPONENT', markRaw(component))
    store.commit('drawer/SET_COMPONENT_PROPS', componentProps)
  }

  // 关闭抽屉
  const closeDrawer = () => {
    store.commit('drawer/SET_VISIBLE', false)
    store.commit('drawer/SET_COMPONENT', null)
    store.commit('drawer/SET_COMPONENT_PROPS', {})
  }

  return {
    drawerVisible,
    currentDrawerComponent,
    drawerSize,
    componentProps: computed(() => store.state.drawer.componentProps),
    openDrawer,
    closeDrawer
  }
}
