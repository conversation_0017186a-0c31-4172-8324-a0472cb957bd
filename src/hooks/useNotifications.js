import { computed } from 'vue'
import { useStore } from 'vuex'

export function useNotifications() {
  const store = useStore()

  // 消息总数，后端返回的数字
  const sumCount = computed(() => store.state.notification.sumCount)
  // 审核消息-审核堆积
  const pendingCount = computed(() => store.state.notification.pendingCount)
  // 审核消息-举报审核
  const reportCount = computed(() => store.state.notification.reportCount)
  // 审核消息-申诉审核
  const appealCount = computed(() => store.state.notification.appealCount)
  // 系统公告
  const noticeCount = computed(() => store.state.notification.noticeCount)
  // 跟评消息
  const followMessageCount = computed(() => store.state.notification.followMessageCount)
  // 审核消息总数（大于1需处理
  const isMessageRead = computed(() => store.getters['notification/isMessageRead']('pending'))
  const auditingCount = computed(() => {
    const reportCount = store.state.notification.reportCount > 0 ? 1 : 0
    const appealCount = store.state.notification.appealCount > 0 ? 1 : 0
    const pendingCount = store.state.notification.pendingCount > 0 && !isMessageRead.value ? 1 : 0
    return reportCount + appealCount + pendingCount
  })
  // 需要显示的消息总数(铃铛)
  const handledNotificationCount = computed(() => {
    return auditingCount.value + noticeCount.value + followMessageCount.value
  })
  // 最新一条需要显示的公告
  const latestAnnouncement = computed(() => store.state.notification.latestAnnouncement)

  // 方法，用于获取通知计数
  const fetchNotificationsCount = () => {
    store.dispatch('notification/fetchNotificationsCount')
  }

  // 方法，用于获取最新公告
  const fetchNewestAnnouncement = () => {
    store.dispatch('notification/fetchNewestAnnouncement')
  }

  // 方法，用于获取所有通知
  const fetchAllNotifications = () => {
    store.dispatch('notification/fetchAllNotifications')
  }

  return {
    sumCount,
    pendingCount,
    reportCount,
    appealCount,
    noticeCount,
    followMessageCount,
    latestAnnouncement,
    auditingCount,
    handledNotificationCount,
    fetchNotificationsCount,
    fetchNewestAnnouncement,
    fetchAllNotifications
  }
}
