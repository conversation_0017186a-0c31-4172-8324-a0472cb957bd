<template>
  <div class="user">
    <div class="tools">
      <span
        v-if="$store.state.settings.enableNavSearch"
        class="item"
        @click="$eventBus.emit('global-search-toggle')"
      >
        <svg-icon name="search" />
      </span>
      <span
        v-if="$store.state.settings.mode === 'pc' && isFullscreenEnable && $store.state.settings.enableFullscreen"
        class="item"
        @click="fullscreen"
      >
        <svg-icon :name="isFullscreen ? 'fullscreen-exit' : 'fullscreen'" />
      </span>
      <span
        v-if="$store.state.settings.enablePageReload"
        class="item"
        @click="reload()"
      >
        <svg-icon name="toolbar-reload" />
      </span>
      <span
        v-if="$store.state.settings.enableThemeSetting"
        class="item"
        @click="$eventBus.emit('global-theme-toggle')"
      >
        <svg-icon name="toolbar-theme" />
      </span>
    </div>
    <!-- <el-divider direction="vertical" style="border-color: #000" /> -->
    <el-dropdown
      class="user-container"
      @command="userCommand"
    >
      <div class="user-wrapper">
        <el-avatar size="default" class="avatar">
          {{ getName($store.getters['menu/user'].name) }}
        </el-avatar>
        <el-icon><ArrowDownBold /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="user-dropdown">
          <!-- <el-dropdown-item command="setting">个人设置</el-dropdown-item> -->
          <el-dropdown-item
            command="logout"
          >
            <div class="logout">
              <svg-icon name="icon-logout" style="width: 20px;height: 20px;margin-right: 3px" /> 退出登录
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- <el-icon><ArrowDownBold /></el-icon> -->
  </div>
</template>

<script setup>
import screenfull from 'screenfull'

const reload = inject('reload')
const store = useStore()
const router = useRouter()

const isFullscreenEnable = computed(() => screenfull.isEnabled)
const isFullscreen = ref(false)

onMounted(() => {
  if (isFullscreenEnable.value) {
    screenfull.on('change', fullscreenChange)
  }
})
onBeforeUnmount(() => {
  if (isFullscreenEnable.value) {
    screenfull.off('change', fullscreenChange)
  }
})
// 截取用户名后两位
function getName(val) {
  if (val) {
    return val.slice(-2)
  } else {
    return '技术'
  }
}
function fullscreen() {
  screenfull.toggle()
}
function fullscreenChange() {
  isFullscreen.value = screenfull.isFullscreen
}
function userCommand(command) {
  switch (command) {
    case 'dashboard':
      router.push({
        name: 'dashboard'
      })
      break
    case 'setting':
      router.push({
        name: 'personalSetting'
      })
      break
    case 'logout':
      store.dispatch('user/logout').then(() => {
        router.push({
          name: 'login'
        })
      })
      break
  }
}
</script>

<style lang="scss" scoped>
.logout{
    display: flex;
    align-items: center;
    &:hover{
        background: #F0F2F5FF;
    }
}
.avatar{
    background: #0070F3FF;
    border: 1px solid #FFFFFF;
    line-height: 38px;
    text-align: center;
    font-size: 12px;
    color: #fff;

}
.user {
  display: flex;
  align-items: center;
  padding: 0 20px;
  white-space: nowrap;
}
.tools {
  margin-right: 20px;
  .item {
    margin-left: 5px;
    padding: 6px 8px;
    border-radius: 5px;
    outline: none;
    cursor: pointer;
    vertical-align: middle;
    transition: all 0.3s;
  }
  .item-pro {
    display: inline-block;
    transform-origin: right center;
    animation: pro-text 3s ease-out infinite;
    @keyframes pro-text {
      0%,
      20% {
        transform: scale(1);
      }
      50%,
      70% {
        transform: scale(1.2);
      }
      100% {
        transform: scale(1);
      }
    }
    .title {
      padding-left: 5px;
      font-weight: bold;
      font-size: 14px;
      background-image: linear-gradient(to right, #ffa237, #fc455d);
      /* stylelint-disable-next-line property-no-vendor-prefix */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
:deep(.user-container) {
  display: inline-block;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
  .user-wrapper {
    .el-avatar {
      vertical-align: middle;
      margin-top: -2px;
      margin-right: 4px;
    }
  }

}
</style>
