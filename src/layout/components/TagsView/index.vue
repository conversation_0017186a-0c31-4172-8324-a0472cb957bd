<template>
  <div
    id="tags-view-container"
    ref="domRef"
    class="tags-view-container"
  >
    <scroll-pane
      ref="scrollPane"
      class="tags-view-wrapper"
    >
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag)?'active':''"
        :to="tag.path"
        class="tags-view-item"
        @click.middle="closeSelectedTag(tag)"
        @contextmenu.prevent="openMenu(tag,$event)"
      >
        {{ tag.title }}
        <el-icon
          v-if="!tag.meta.default"
          size="16"
          @click.prevent.stop="closeSelectedTag(tag)"
        >
          <el-icon-close />
        </el-icon>
      </router-link>
    </scroll-pane>
    <ul
      v-show="visible"
      :style="{left:data.left+'px',top:data.top+'px'}"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(data.selectedTag)">刷新</li>
      <li
        v-if="!(data.selectedTag.meta&& data.selectedTag.meta.affix)"
        @click="closeSelectedTag(data.selectedTag)"
      >
        关闭
      </li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(data.selectedTag)">关闭全部</li>
    </ul>
  </div>
</template>

<script setup name="tagView">
import ScrollPane from './ScrollPane.vue'
import { computed, nextTick, ref } from 'vue'
const { proxy } = getCurrentInstance()
const store = useStore()
const route = useRoute(), router = useRouter()

const data = ref({
  top: 0,
  left: 0,
  selectedTag: {},
  affixTags: []
})
const visible = ref(false)

const visitedViews = computed(
  () => {
    return store.state.tagsView.visitedViews
  }
)

const routes = computed(
  () => {

    return router.options.routes
  }
)

watch(route, (newValue, oldValue) => {
  addTags()
  moveToCurrentTag()
})

// watch(visible, (newValue, oldValue) => {
//   console.log(newValue)
//   if (newValue) {
//     document.body.addEventListener('click', closeMenu())
//   } else {
//     document.body.removeEventListener('click', closeMenu())
//   }
// })

onMounted(() => {
  initTags()
  addTags()
})

function isActive(val) {
  return val.path === route.path
}

function filterAffixTags(routes, basePath = '/') {
  let tags = []
  routes.forEach(route => {
    if (route.meta && route.meta.default) {
      const tagPath = route.path
      tags.push({
        fullPath: tagPath,
        path: tagPath,
        name: route.name,
        meta: { ...route.meta }
      })
    }
    if (route.children) {
      const tempTags = filterAffixTags(route.children, route.path)
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags]
      }
    }
  })
  return tags
}
// 初始化
function initTags() {
  const affixTags = data.value.affixTags = filterAffixTags(routes.value)
  for (const tag of affixTags) {
    if (tag.name) {
      store.dispatch('tagsView/addVisitedView', tag)
    }
  }
}
// 添加标签
function addTags() {
  const { name, meta } = route
  if (name && name != 'login' && meta.sidebar !== false) {
    store.dispatch('tagsView/addView', route)
  }
  return false
}

function moveToCurrentTag() {
  const tags = proxy.$refs.tag
  nextTick(() => {
    if (tags.to.path && tags.to.path === route.path) {
      proxy.$refs.scrollPane.moveToTarget(tags)
      // when query is different then update
      if (tags.to.fullPath !== route.fullPath) {
        store.dispatch('tagsView/updateVisitedView', route)
      }
    }

  })
}

function refreshSelectedTag(view) {
  store.dispatch('tagsView/delCachedView', view).then(() => {
    const { fullPath } = view
    nextTick(() => {
      router.replace({
        path: fullPath
      })
    })
  })
}
function closeSelectedTag(view) {
  if (visitedViews.value.length < 2) {
    return
  }
  store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
    if (isActive(view)) {
      toLastView(visitedViews, view)
    }
  })
}

function closeOthersTags() {
  router.push(data.value.selectedTag)
  store.dispatch('tagsView/delOthersViews', data.value.selectedTag).then(() => {
    moveToCurrentTag()
  })
}

function closeAllTags(view) {

  store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
    if (data.value.affixTags.some(tag => tag.path === view.path)) {
      return
    }
    toLastView(visitedViews, view)
  })
}

function toLastView(visitedViews, view) {
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView)
  } else {
    // now the default is to redirect to the home page if there is no tags-view,
    // you can adjust it according to your needs.
    if (view.name === 'dashboard') {
      // to reload home page
      router.replace({ path: '/dashboard' })
    } else {
      router.push('/')
    }
  }
}

function openMenu(tags, e) {
  const menuMinWidth = 134
  const offsetLeft = proxy.$refs.domRef.getBoundingClientRect().left // container margin left
  const offsetWidth = proxy.$refs.domRef.offsetWidth // container width
  const maxLeft = offsetWidth - menuMinWidth // left boundary
  const left = e.clientX - offsetLeft + 20 // 15: margin right
  if (left > maxLeft) {
    data.value.left = maxLeft
  } else {
    data.value.left = left
  }

  data.value.top = e.clientY
  visible.value = true
  data.value.selectedTag = tags
  document.body.addEventListener('click', this.closeMenu)
}

function closeMenu() {
  document.body.removeEventListener('click', this.closeMenu)
  visible.value = false
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  // border-bottom: 1px solid #d8dce5;
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  .tags-view-wrapper {
    .tags-view-item {
      text-decoration: none;
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 38px;
      line-height: 36px;
      // border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px; 
      font-size: 14px;
      margin-left: 1px;
      width: 134px;
      border-radius: 5px;
      //   margin-top: 4px;
      border: 1px solid #ccc;
      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        background-color: #f7f7f7;
        color: #333;
        // &::before {
        //   content: '';
        //   background: #fff;
        //   display: inline-block;
        //   width: 8px;
        //   height: 8px;
        //   border-radius: 50%;
        //   position: relative;
        //   margin-right: 2px;
        // }
      }
      .tags-view-item :hover {
        background-color: #f7f7f7;
        color: #333;
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon {
      float: right;
      margin-top: 10px;
      //   width: 16px;
      //   height: 16px;
      //   float: right;
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
