<template>
  <div
    class="topbar-container"
    :class="{
      fixed: $store.state.settings.topbarFixed,
      shadow: scrollTop
    }"
    data-fixed-calc-width
  >
    <div class="left-box">
      <div
        v-if="
          $store.state.settings.mode === 'mobile' ||
          (['side', 'head', 'single'].includes($store.state.settings.menuMode) &&
            $store.state.settings.enableSidebarCollapse)
        "
        class="sidebar-collapse"
        :class="{ 'is-collapse': $store.state.settings.sidebarCollapse }"
        @click="$store.commit('settings/toggleSidebarCollapse')"
      >
        <svg-icon name="toolbar-collapse" class="toolbar-collapse" />
      </div>
      <el-breadcrumb
        v-if="$store.state.settings.enableBreadcrumb && $store.state.settings.mode === 'pc'"
        separator-class="el-icon-arrow-right"
      >
        <transition-group name="breadcrumb">
          <template v-for="(item, index) in breadcrumbList">
            <el-breadcrumb-item
              v-if="index < breadcrumbList.length - 1"
              :key="item.path"
              :to="pathCompile(item.path)"
            >
              {{ item.title }}
            </el-breadcrumb-item>
            <el-breadcrumb-item v-else :key="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </template>
        </transition-group>
      </el-breadcrumb>
    </div>
    <div class="right-container">
      <NotificationMenuButton />
      <UserMenu />
    </div>

    <!-- <div class="tag-view">
      <TagView></TagView>
    </div> -->
  </div>
</template>

<script setup>
import { compile } from 'path-to-regexp'
import { deepClone } from '@/util'
import UserMenu from '../UserMenu/index.vue'
import TagView from '../TagsView/index.vue'
import NotificationMenuButton from '@/layout/components/Topbar/components/NotificationMenuButton.vue'
const store = useStore()
const route = useRoute()

const breadcrumbList = computed(() => {
  let breadcrumbList = []
  if (store.state.settings.enableDashboard) {
    breadcrumbList.push({
      path: '/dashboard',
      title: store.state.settings.dashboardTitle
    })
  }
  console.log(route.meta.breadcrumbNeste)
  if (route.meta.breadcrumbNeste) {
    breadcrumbList.push(...deepClone(route.meta.breadcrumbNeste))
  }
  return breadcrumbList
})

const scrollTop = ref(0)
onMounted(() => {
  window.addEventListener('scroll', onScroll)
})
onUnmounted(() => {
  window.removeEventListener('scroll', onScroll)
})
function onScroll() {
  scrollTop.value = document.documentElement.scrollTop || document.body.scrollTop
}

function pathCompile(path) {
  let toPath = compile(path)
  return toPath(route.params)
}
</script>

<style lang="scss" scoped>
:deep(.el-badge__content--danger) {
  background-color: #ff0000;
}
:deep(.el-badge__content.is-fixed) {
  transform: translateY(-50%) translateX(90%);
}
.topbar-container {
  position: absolute;
  z-index: 999;
  top: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  height: $g-topbar-height;

  .right-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  &.fixed {
    position: fixed;
    &.shadow {
      box-shadow: 0 10px 10px -10px #ccc;
    }
  }
  .tag-view {
    width: 100%;
  }
  .left-box {
    display: flex;
    align-items: center;
    padding-right: 50px;
    overflow: hidden;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-mask-image: linear-gradient(90deg, #000 0%, #000 calc(100% - 50px), transparent);
    .sidebar-collapse {
      display: flex;
      align-items: center;

      height: 50px;
      cursor: pointer;

      .svg-icon {
        width: 20px;
        height: 20px;
      }
      &:hover .svg-icon {
        color: #5482ee;
      }
      &.is-collapse .svg-icon {
        transform: rotateZ(-180deg);
      }
      & + .el-breadcrumb {
        margin-left: 0;
      }
    }
    :deep(.el-breadcrumb) {
      margin-left: 20px;
      white-space: nowrap;
      .el-breadcrumb__item {
        display: inline-block;
        float: none;
        span {
          font-weight: normal;
        }
        &:last-child span {
          color: #1890ff;
        }
      }
    }
  }
}
// 面包屑动画
.breadcrumb-enter-active {
  transition: all 0.25s;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(30px) skewX(-50deg);
}
</style>
