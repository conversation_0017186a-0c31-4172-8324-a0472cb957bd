<script setup>
import YiDu from '@/assets/images/yidu.png'
import { computed, onMounted, ref } from 'vue'
import { requests } from '@/api/system/notification'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useDrawer } from '@/hooks/useDrawer'
import { ElMessage } from 'element-plus'
import { useNotifications } from '@/hooks/useNotifications'

const store = useStore()
const list = ref([])
const listData = ref({})
const currentPage = computed(() => listData.value.page.current)

const { fetchNotificationsCount } = useNotifications()

onMounted(() => {
  requests.getUserFollowListApi().then(res => {
    listData.value = res.data
    list.value = res.data.page.records
  })
})

const onRealAllClick = async () => {
  await requests.readAllFollowApi()
  requests.getUserFollowListApi().then(res => {
    listData.value = res.data
    list.value = res.data.page.records
  })
  // 刷新首页数字
  fetchNotificationsCount()
}

const router = useRouter()
const { closeDrawer } = useDrawer()
const onItemClick = async (event, item) => {
  // 点击评论内的链接，不触发整个条目的点击动作
  if (event.target.tagName.toLowerCase() === 'a') {
    console.log('点击事件来自 a 标签', item)
    return
  } else {
    console.log('点击事件不来自 a 标签', item)
  }

  await requests.readSingleFollowApi(item.id)
  requests.getUserFollowListApi().then(res => {
    listData.value = res.data
    list.value = res.data.page.records
  })
  // 刷新首页数字
  fetchNotificationsCount()
  // 圈子权限
  const resPermissionCircle = await requests.checkUserMenuPermissionApi('circleMgr')
  // 频道权限
  const resPermissionChannel = await requests.checkUserMenuPermissionApi('channelMgr')
  const circlePermission = resPermissionCircle?.data?.data
  const channelPermission = resPermissionChannel?.data?.data
  if (!circlePermission && !channelPermission) {
    ElMessage.error('您没有权限查看该页面')
    return
  }
  // 页面跳转
  if (channelPermission && circlePermission) {
    router.push(`/operationManagement/operationManagement/channelMgr?articleId=${item.article_id}`)
  } else if (circlePermission) {
    router.push(`/operationManagement/circleMgr?articleId=${item.article_id}`)
  } else if (channelPermission) {
    router.push(`/operationManagement/operationManagement/channelMgr?articleId=${item.article_id}`)
  }
  setTimeout(() => {
    closeDrawer()
  }, 500)
}

const load = async () => {
  const total = listData.value.page.total
  if (list.value.length >= total) {
    return
  }
  const res = await requests.getUserFollowListApi(currentPage.value + 1)
  listData.value = res.data
  list.value = list.value.concat(res.data.page.records)
}
</script>

<template>
  <div class="page-container">
    <div class="top-container">
      <span class="top-container-title">跟评消息</span>
      <div v-if="list.length > 0" class="top-right" @click="onRealAllClick">
        <img :src="YiDu" class="image-yidu" />
        <span class="top-container-read">全部已读</span>
      </div>
    </div>
    <div v-if="list.length > 0" v-infinite-scroll="load" class="content-container">
      <div
        v-for="(item, index) in list"
        :key="item.id"
        class="item-container"
        @click="onItemClick($event, item)"
      >
        <div class="item-top">
          <div v-if="item.is_read === 0" class="red-dot"></div>
          <span :class="['item-title', item.is_read ? 'item-title-read' : '']">有新的跟评</span>
        </div>
        <div class="reply-content" v-html="item.reply_content"></div>
        <div class="parent-comment-content-container">
          <div :class="['tag-reply', item.is_read ? 'tag-reply-read' : '']">回复</div>
          <div :class="['parent-comment-user', item.is_read ? 'parent-comment-user-read' : '']">
            {{ item.reply_user_name }}：
          </div>
          <div
            :class="['parent-comment-content', item.is_read ? 'parent-comment-content-read' : '']"
            v-html="item.parent_comment_content"
          ></div>
        </div>
        <div class="item-bottom">
          <div class="item-desc">稿件 《{{ item.article_title }}》</div>
          <div class="item-time">{{ item.created_at_string }}</div>
        </div>
        <div class="item-divider"></div>
      </div>
    </div>
    <div v-else class="empty-container">
      <el-empty description="暂无消息"> </el-empty>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-container {
  width: 100%;
  height: 90vh;
  display: flex;
  flex-direction: column;
}
.top-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-left: 14px;
  margin-top: 14px;
  margin-right: 14px;
}
.top-container-title {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
}

.top-container-read {
  font-size: 12px;
  color: #333333;
}

.image-yidu {
  width: 14px;
  height: 14px;
}

.top-right {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}
.content-container {
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  cursor: pointer;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}

.item-container {
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 14px;
}
.item-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.red-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: red;
  margin-right: 8px;
  position: absolute;
  left: -10px;
}
.item-title {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
}
.item-title-read {
  color: #999999;
}
.item-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  white-space: nowrap;
}
.item-desc {
  font-size: 14px;
  color: #acacac;
  width: 400px;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.item-time {
  font-size: 14px;
  color: #acacac;
}
.item-divider {
  width: 100%;
  height: 1px;
  background-color: #efefef;
  margin-top: 14px;
}
.reply-content {
  margin-top: 14px;
  width: 100%;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #333333;
  border-radius: 3px;
  padding: 8px 6px;
}
.parent-comment-content-container {
  margin-top: 10px;
  width: 540px;
  line-height: 1.5;
  display: flex;
  align-items: center;
}
.tag-reply {
  font-size: 14px;
  color: #0b82fd;
  background: #e6f2fe;
  border-radius: 2px;
  padding: 4px 6px;
  margin-right: 6px;
  white-space: nowrap;
}
.tag-reply-read {
  color: #333333;
  background: #f5f5f5;
}
.parent-comment-user {
  font-size: 14px;
  color: #0b82fd;
  white-space: nowrap;
}
.parent-comment-content {
  flex: 1;
  font-size: 14px;
  color: #0b82fd;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.parent-comment-content-read {
  color: #acacac;
}
.parent-comment-user-read {
  color: #acacac;
}
</style>
