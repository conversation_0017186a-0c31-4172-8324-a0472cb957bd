<script setup>
import YiDu from '@/assets/images/yidu.png'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { useDrawer } from '@/hooks/useDrawer'
import { useNotifications } from '@/hooks/useNotifications'

const store = useStore()
const { pendingCount, reportCount, appealCount } = useNotifications()
const isMessageRead = computed(() => (messageId) => store.getters['notification/isMessageRead'](messageId))

const markAsRead = (messageId) => {
  store.dispatch('notification/markMessageAsRead', { messageId })
}
// 显示时间 YYYY-MM-DD HH:mm:ss
const formattedDate = dayjs().format('YYYY-MM-DD HH:mm:ss')

const router = useRouter()
const { closeDrawer } = useDrawer()
const onItemPendingClick = () => {
  // 跳转到审核管理
  router.push('/auditManagement/regularAudit')
  setTimeout(() => {
    closeDrawer()
  }, 500)
}

const onItemReportClick = () => {
  // 跳转到举报管理
  router.push('/auditManagement/reportReview')
  setTimeout(() => {
    closeDrawer()
  }, 500)
}

const onItemAppealClick = () => {
  // 跳转到申诉管理
  router.push('/auditManagement/appeal')
  setTimeout(() => {
    closeDrawer()
  }, 500)
}
</script>

<template>
  <div class="page-container">
    <div class="top-container">
      <span class="top-container-title">审核消息</span>
      <div v-if="false" class="top-right">
        <img :src="YiDu" class="image-yidu" />
        <span class="top-container-read">全部已读</span>
      </div>
    </div>
    <div class="content-container">
      <div
        class="item-container"
        :class="{
          'item-read': isMessageRead('pending'),
          'item-unread': !isMessageRead('pending')
        }"
        @click="markAsRead('pending'); onItemPendingClick()"
        >
        <div class="item-top">
          <div v-if="pendingCount > 0 && !isMessageRead('pending')" class="red-dot"></div>
          <span :class="['item-title', pendingCount === 0 ? 'item-title-no' : '']">审核堆积</span>
        </div>
        <div class="item-bottom">
          <div :class="['item-desc', pendingCount === 0 ? 'item-desc-no' : '']">
            {{ pendingCount > 0 ? `有${pendingCount}条评论待审核，赶紧去审核吧！` : '暂无审核' }}
          </div>
          <div class="item-time">{{ formattedDate }}</div>
        </div>
        <div class="item-divider"></div>
      </div>
      <div class="item-container" @click="onItemReportClick">
        <div class="item-top">
          <div v-if="reportCount > 0" class="red-dot"></div>
          <span :class="['item-title', reportCount === 0 ? 'item-title-no' : '']">举报审核</span>
        </div>
        <div class="item-bottom">
          <div :class="['item-desc', reportCount === 0 ? 'item-desc-no' : '']">
            {{ reportCount > 0 ? `有${reportCount}条评论举报，赶紧去审核吧！` : '暂无审核' }}
          </div>
          <div class="item-time">{{ formattedDate }}</div>
        </div>
        <div class="item-divider"></div>
      </div>
      <div class="item-container" @click="onItemAppealClick">
        <div class="item-top">
          <div v-if="appealCount > 0" class="red-dot"></div>
          <span :class="['item-title', appealCount === 0 ? 'item-title-no' : '']">申诉审核</span>
        </div>
        <div class="item-bottom">
          <div :class="['item-desc', appealCount === 0 ? 'item-desc-no' : '']">
            {{ appealCount > 0 ? `有${appealCount}条评论申诉，赶紧去审核吧！` : '暂无审核' }}
          </div>
          <div class="item-time">{{ formattedDate }}</div>
        </div>
        <div class="item-divider"></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item-read {
  opacity: 0.6;
}

.item-unread {
  font-weight: bold;
}
.page-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 14px;
}
.top-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.top-container-title {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
}

.top-container-read {
  font-size: 12px;
  color: #333333;
}

.image-yidu {
  width: 14px;
  height: 14px;
}

.top-right {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.item-container {
  display: flex;
  flex-direction: column;
  margin-top: 14px;
  position: relative;
  cursor: pointer;
}
.item-top {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.red-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: red;
  margin-right: 8px;
  position: absolute;
  left: -10px;
}
.item-title {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
}
.item-title-no {
  color: #acacac;
}
.item-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.item-desc {
  font-size: 14px;
  color: #0b82fd;
}
.item-time {
  font-size: 14px;
  color: #acacac;
}
.item-divider {
  width: 100%;
  height: 1px;
  background-color: #efefef;
  margin-top: 14px;
}
.item-desc-no {
  color: #acacac;
}
</style>
