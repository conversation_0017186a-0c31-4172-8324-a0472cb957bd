<script setup>
import YiDu from '@/assets/images/yidu.png'
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { requests } from '@/api/system/notification'
import { useNotifications } from '@/hooks/useNotifications'

const store = useStore()
const list = ref([])
const data = ref({})
const { fetchAllNotifications } = useNotifications()

onMounted(() => {
  requests.getUserAnnouncementListApi().then(res => {
    list.value = res.data.page.records
    data.value = res.data.page
  })
})

const load = async () => {
  const total = data.value.total
  if (list.value.length >= total) {
    return
  }
  const current = data.value.current
  const res = await requests.getUserAnnouncementListApi(current + 1)
  list.value = list.value.concat(res.data.page.records)
  data.value = res.data.page
}

const onReadAllClick = async () => {
  await requests.readAllAnnouncementApi()
  requests.getUserAnnouncementListApi().then(res => {
    list.value = res.data.page.records
    data.value = res.data.page
  })
  // 刷新首页数字
  fetchAllNotifications()
}

const onItemClick = async item => {
  console.log(item)
  if (!item.read_or_no) {
    await requests.readSingleAnnouncementApi(item.id)
    setTimeout(() => {
      requests.getUserAnnouncementListApi().then(res => {
        list.value = res.data.page.records
        data.value = res.data.page
      })
      // 刷新首页数字
      fetchAllNotifications()
    }, 500)
  }
  store.dispatch('announcement/showAnnouncementWithApi', item.id)
}
</script>

<template>
  <div class="page-container">
    <div class="top-container" @click="onReadAllClick">
      <span class="top-container-title">系统公告</span>
      <div v-if="list.length > 0" class="top-right">
        <img :src="YiDu" class="image-yidu" />
        <span class="top-container-read">全部已读</span>
      </div>
    </div>
    <div v-if="list.length > 0" v-infinite-scroll="load" class="content-container">
      <div v-for="(item, index) in list" class="item-container" @click="onItemClick(item)">
        <div class="item-top">
          <div v-if="!item.read_or_no" class="red-dot"></div>
          <span :class="['item-title', item.read_or_no ? 'item-title-read' : '']">{{
            item.notice_title
          }}</span>
        </div>
        <div class="item-bottom">
          <div class="item-desc">发布人：{{ item.subscribe }}</div>
          <div class="item-time">{{ item.update_at_string }}</div>
        </div>
        <div class="item-divider"></div>
      </div>
    </div>
    <div v-else class="empty-container">
      <el-empty description="暂无公告"> </el-empty>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-container {
  width: 100%;
  height: 90vh;
  display: flex;
  flex-direction: column;
  padding-top: 14px;
  padding-bottom: 14px;
}
.top-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 14px;
  padding-right: 14px;
}
.top-container-title {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
}

.top-container-read {
  font-size: 12px;
  color: #333333;
}

.image-yidu {
  width: 14px;
  height: 14px;
}

.top-right {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.content-container {
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}

.item-container {
  display: flex;
  flex-direction: column;
  margin-top: 14px;
  position: relative;
  cursor: pointer;
  padding-left: 14px;
  padding-right: 14px;
}
.item-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.red-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: red;
  margin-right: 8px;
  position: absolute;
  left: -8px;
}
.item-title {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
}
.item-title-read {
  color: #acacac;
}
.item-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.item-desc {
  font-size: 14px;
  color: #acacac;
}
.item-desc-read {
  color: #acacac;
}
.item-time {
  font-size: 14px;
  color: #acacac;
}
.item-divider {
  width: 100%;
  height: 1px;
  background-color: #efefef;
  margin-top: 14px;
}
</style>
