<script setup name="NotificationMenuButton">
import { useStore } from 'vuex'
import NotificationMenuContent from '@/layout/components/Topbar/components/NotificationMenuContent.vue'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import NotificationIcon from '@/assets/images/notification_icon.png'
import { useDrawer } from '@/hooks/useDrawer'
import NotificationIconLeft from '@/assets/images/notification_trumpet.png'
import { requests } from '@/api/system/notification'
import { useNotifications } from '@/hooks/useNotifications'
import { useAnnouncement } from '@/hooks/useAnnouncement'

const store = useStore()
const { openDrawer } = useDrawer()
const { showAnnouncementWithApi } = useAnnouncement()
const {
  latestAnnouncement,
  followMessageCount,
  handledNotificationCount,
  fetchAllNotifications,
  fetchNotificationsCount
} = useNotifications()
const onNotificationClick = () => {
  openDrawer(NotificationMenuContent, '830px')
}
const isLatestAnnouncementRead = computed(() => latestAnnouncement.value.read_or_no)
const announcementId = computed(() => latestAnnouncement.value.id)
const title = computed(() => latestAnnouncement.value.notice_title)
const content = computed(() => latestAnnouncement.value.notice_content)
const time = computed(() => latestAnnouncement.value.update_at_string)

onMounted(() => {
  fetchAllNotifications()
  const intervalId = setInterval(fetchNotificationsCount, 60 * 1000)

  onBeforeUnmount(() => {
    clearInterval(intervalId)
  })
})

const onAnnouncementClick = async () => {
  await requests.readSingleAnnouncementApi(announcementId.value)

  setTimeout(() => {
    fetchAllNotifications()
  }, 500)
  showAnnouncementWithApi(announcementId.value)
}
</script>

<template>
  <div class="container">
    <div v-if="!isLatestAnnouncementRead" class="left-container">
      <img :src="NotificationIconLeft" class="img-left" @click="onAnnouncementClick" />
      <span class="text-left" @click="onAnnouncementClick">{{ `${title}` }}</span>
      <div class="divider-left"></div>
    </div>
    <el-badge
      class="item"
      :value="handledNotificationCount"
      :hidden="handledNotificationCount === 0"
      :max="99"
    >
      <div class="notification-container" @click="onNotificationClick">
        <img :src="NotificationIcon" class="img-logo" />
      </div>
    </el-badge>
  </div>
</template>

<style scoped lang="scss">
.container {
  padding-top: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.notification-container {
  cursor: pointer;
}
.img-logo {
  width: 26px;
  height: 26px;
}
.img-left {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.text-left {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  margin-left: 6px;
  cursor: pointer;
}
.left-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.divider-left {
  width: 1px;
  height: 14px;
  background-color: #333333;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 14px;
}
</style>
