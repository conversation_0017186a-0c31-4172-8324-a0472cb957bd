<script setup name="NotificationMenuContent">
import { ref, onMounted, computed, markRaw } from 'vue'
import CloseIcon from '@/assets/images/circle/closeCircle.png'
import { useDrawer } from '@/hooks/useDrawer'
import AuditMessages from '@/layout/components/Topbar/components/AuditMessages.vue'
import FollowMessages from '@/layout/components/Topbar/components/FollowMessages.vue'
import SystemAnnouncements from '@/layout/components/Topbar/components/SystemAnnouncements.vue'
import { useStore } from 'vuex'
import { useNotifications } from '@/hooks/useNotifications'

const router = useRouter()
const store = useStore()
const { closeDrawer } = useDrawer()
const {
  auditingCount,
  followMessageCount,
  noticeCount,
  fetchAllNotifications,
  fetchNotificationsCount
} = useNotifications()

const tabs = ref([
  { name: 'auditMessages', label: '审核消息', component: markRaw(AuditMessages), count: 0 },
  { name: 'followMessages', label: '跟评消息', component: markRaw(FollowMessages), count: 0 },
  {
    name: 'systemAnnouncements',
    label: '系统公告',
    component: markRaw(SystemAnnouncements),
    count: 0
  }
])

const tabCounts = computed(() => {
  return {
    auditMessages: auditingCount.value,
    followMessages: followMessageCount.value,
    systemAnnouncements: noticeCount.value
  }
})

onMounted(() => {
  fetchNotificationsCount()
})

const selectTab = name => {
  activeTabName.value = name
}

// 激活的Tab名称
const activeTabName = ref('auditMessages')

const currentTab = computed(() => tabs.value.find(tab => tab.name === activeTabName.value))

const onCloseDrawerClick = () => {
  closeDrawer()
}
</script>

<template>
  <div class="drawer-container">
    <div class="header-container">
      <div class="msg-list">消息中心</div>
      <img :src="CloseIcon" class="close-icon" @click="onCloseDrawerClick" />
    </div>
    <div class="list-container">
      <div class="left-container">
        <div class="tabs-header">
          <div
            v-for="tab in tabs"
            :key="tab.name"
            :class="['tab-header-item', { 'is-active': activeTabName === tab.name }]"
            @click="selectTab(tab.name)"
          >
            <span :class="['tab-item-text', { 'is-active': activeTabName === tab.name }]">{{
              tab.label
            }}</span>
            <el-badge
              :value="tabCounts[tab.name]"
              :hidden="tabCounts[tab.name] === 0"
              :max="99"
            ></el-badge>
          </div>
        </div>
      </div>
      <div class="right-container">
        <component :is="currentTab.component" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-badge__content--danger) {
  background-color: #ff0000;
}
:deep(.el-badge__content) {
  margin-top: 6px;
}
.drawer-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #f3f5f6;
  display: flex;
  flex-direction: column;
}

.msg-list {
  font-size: 20px;
  color: #333333;
}

.list-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  align-items: start;
}

.left-container {
  display: flex;
  flex-direction: column;
}

.tabs-header {
  width: 180px;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 6px;
}

.tab-header-item {
  margin: 6px 6px;
  border-radius: 4px;
  height: 32px;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.tab-item-text {
  font-size: 14px;
  color: #333333;
  font-weight: normal;
}

.tab-item-text.is-active {
  font-weight: bold;
}

.tab-header-item.is-active {
  background-color: #f5f5f5;
}

.tabs-content {
  flex-grow: 1;
  padding: 20px;
  background-color: #f3f5f6;
}

.right-container {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 6px;
  margin-left: 10px;
  padding-left: 14px;
}
.close-icon {
  width: 14px;
  height: 14px;
  cursor: pointer;
  margin-right: 6px;
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
}
</style>
