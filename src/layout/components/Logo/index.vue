<template>
  <router-link v-if="!showLogo"
               :to="to"
               class="title"
               :class="{'is-link': $store.state.settings.enableDashboard}"
               :title="title"
               style="font-size: 14px;">
    <img :src="logo"
         class="logo">
    <!-- <span v-if="showTitle">{{ title }}</span> -->
  </router-link>
  <router-link v-else
               :to="to"
               class="title2"
               :class="{'is-link': $store.state.settings.enableDashboard}"
               :title="title"
               style="font-size: 14px;">
    <img :src="imgLogo2"
         class="logo">
    <!-- <span v-if="showTitle">{{ title }}</span> -->
  </router-link>
</template>

<script setup>
import imgLogo from '@/assets/images/logo-icon.png'
import imgLogo2 from '@/assets/images/logo-small.png'
const store = useStore()

defineProps({
  showLogo: {
    type: Boolean,
    default: true
  },
  showTitle: {
    type: Boolean,
    default: true
  }
})

const title = ref('智慧化互动管理平台')
const logo = ref(imgLogo)

const to = computed(() => {
  let rtn = {}
  if (store.state.settings.enableDashboard) {
    rtn.name = 'dashboard'
  }
  return rtn
})
</script>

<style lang="scss" scoped>
.title {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  height: $g-sidebar-logo-height;
  text-align: center;
  overflow: hidden;
  text-decoration: none;
  &.is-link {
    cursor: pointer;
  }
  .logo {
    width: 200px;
    height: 100%;
  }
  span {
    display: block;
    font-size: 14px;
    font-weight: normal;
    color: #ffffff;
  }
}
.title2 {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  height: $g-sub-sidebar-close-logo-height;
  text-align: center;
  overflow: hidden;
  text-decoration: none;
  &.is-link {
    cursor: pointer;
  }
  .logo {
    width: 60px;
    height: 100%;
  }
  span {
    display: block;
    font-size: 14px;
    font-weight: normal;
    color: #ffffff;
  }
}
</style>
