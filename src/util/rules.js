// /
//  * @param {string} path  判断字符串是否是https?:|mailto:|tal: 开头的
//  * @returns {Boolean}
//  */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

// /**
//  * @param {string} str  //检验字符串是否是 admin  editor
//  * @returns {Boolean}
//  */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

// /**
//  * @param {string} str  判断都是小写字母
//  * @returns {Boolean}
//  */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

// /**
//  * @param {string} str  判断都是大写字母
//  * @returns {Boolean}
//  */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

// /**
//  * @param {string} str  判断是否是字母
//  * @returns {Boolean}
//  */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

// /**
//  * @param {string} email  校验邮箱
//  * @returns {Boolean}
//  */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

// /**
//  * @param {string} str 判断是否字符串
//  * @returns {Boolean}
//  */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

// /**
//  * @param {Array} arg  判断是否是数组
//  * @returns {Boolean}
//  */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

// 校验是否是网址
export function  CheckUrl(url) {
  var reg = /^(?:(http|https|ftp):\/\/)?((?:[\w-]+\.)+[a-z0-9]+)((?:\/[^/?#]*)+)?(\?[^#]+)?(#.+)?$/gim
  if (reg.test(url)) {
    return true
  } else {
    return false
  }
}
 