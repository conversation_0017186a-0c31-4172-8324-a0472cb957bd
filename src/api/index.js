import axios from 'axios'
import qs from 'qs'
import router from '@/router/index'
import store from '@/store/index'
import Cookies from 'js-cookie'
import { v4 as uuidv4 } from 'uuid'
import { sha256 } from 'js-sha256'
const timestamp = new Date().getTime()
const request_id = uuidv4()
const signature = sha256(`${request_id}&&${timestamp}&&cK1!dJ0&aA0`)
import { ElMessage, ElMessageBox } from 'element-plus'

const toLogin = () => {
  router.push({
    name: 'login',
    query: {
      redirect:
        router.currentRoute.value.path !== '/login' ? router.currentRoute.value.fullPath : undefined
    }
  })
}

const api = axios.create({
  baseURL:
    import.meta.env.VITE_OPEN_PROXY === 'true' ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL,
  timeout: 60000,
  responseType: 'json'
})
api.interceptors.request.use(request => {
  /**
   * 全局拦截请求发送前提交的参数
   * 以下代码为示例，在请求头里带上 token 信息
   */
  request.headers['X-REQUEST-ID'] = request_id
  request.headers['X-TIMESTAMP'] = timestamp
  request.headers['X-SIGNATURE'] = signature

  // 是否将 POST 请求参数进行字符串化处理
  if (request.method === 'post') {
    if (request.file) {
      // Check if the request has a file to upload
      request.headers['Content-Type'] = 'multipart/form-data'
    } else {
      request.data = qs.stringify(request.data, { arrayFormat: 'brackets' })
    }
  }
  if (request.method === 'get') {
    request.data = qs.stringify(request.data, {
      arrayFormat: 'brackets'
    })
  }
  return request
})

api.interceptors.response.use(
  response => {
    /**
     * 全局拦截请求发送后返回的数据，如果数据有报错则在这做全局的错误提示
     * 假设返回数据格式为：{ status: 1, error: '', data: '' }
     * 规则是当 status 为 1 时表示请求成功，为 0 时表示接口需要登录或者登录状态失效，需要重新登录
     * 请求出错时 error 会返回错误信息
     */
    if (response.data.code === 200 || response.data.code === 0) {
      // 请求成功并且没有报错
      return Promise.resolve(response.data)
    } else {
      // 这里做错误提示
      if (response.data.code === 30200) {
        Cookies.remove('login')
        Cookies.remove('ZJOL-JSESSIONID')
      }
      ElMessage.error(response.data.message)
      return Promise.reject(response.data)
    }
  },
  error => {
    console.log(error)
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }
    let message = error.message
    if (error.data && error.data.code === 30200) {
      Cookies.remove('login')
      Cookies.remove('ZJOL-JSESSIONID')
    }
    if (message == 'Network Error') {
      message = '后端网络故障'
    } else if (message.includes('timeout')) {
      message = '接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '接口' + message.substr(message.length - 3) + '异常'
    }
    const disableErrorToast = !!error?.config?.disableErrorToast
    if (disableErrorToast) {
      // 不允许接口错误Toast提示
    } else {
      ElMessage({
        message,
        type: 'error'
      })
    }
    return Promise.reject(error)
  }
)

export default api
