import api from './index.js'

// 获取列表
export function getTabel(path, data) {
  return api({
    url: path,
    method: 'get',
    params: data
  })
}
export function getHistory(data) {
  return api({
    url: 'operator_log/query_logs',
    method: 'get',
    params: data
  })
}

export function getColumnSettingsApi(menuType, mode) {
  return api({
    url: 'column/query',
    method: 'post',
    data: { type: menuType, mode }
  })
}

export function saveColumnSettingsApi(data) {
  return api({
    url: 'column/save',
    method: 'post',
    data
  })
}
