import api from '../index.js'
const requests = {
  // 上下线
  updateStatus(data) {
    return api({
      url: 'admin_user/update_status',
      method: 'post',
      data
    })
  },
  // 角色关联列表
  roleListAll(data) {
    return api({
      url: 'admin_role/role_list_all',
      method: 'get',
      params: data
    })
  },
  // 用户编辑
  updateUserRole(data) {
    return api({
      url: 'admin_user/user_update',
      method: 'post',
      data
    })
  }
}
export {
  requests
}
