import api from '../index.js'
const requests = {
  // 权限列表
  permissionList(data) {
    return api({
      url: 'admin_permission/permission_list',
      method: 'get',
      data
    })
  },
  //   权限创建
  createPermission(data) {
    return api({
      url: 'admin_permission/create_permission',
      method: 'post',
      data
    })
  },
  //   权限删除
  deletePermission(data) {
    return api({
      url: 'admin_permission/delete_permission',
      method: 'post',
      data
    })
  },
  //   权限编辑
  updatePermission(data) {
    return api({
      url: 'admin_permission/update_permission',
      method: 'post',
      data
    })
  }
}
export {
  requests
}
