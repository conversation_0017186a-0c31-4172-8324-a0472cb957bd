import api from '../index.js'
const requests = {
  // 消息数量
  getNotificationsCountApi() {
    return api({
      url: 'follow_message/message_bell_number',
      method: 'get',
      params: {},
      disableErrorToast: true
    })
  },
  // 最新消息公告
  getLatestAnnouncementApi() {
    return api({
      url: 'notice_system/new_notice',
      method: 'get',
      params: {}
    })
  },
  // 个人跟评列表
  getUserFollowListApi(current = 1) {
    return api({
      url: 'follow_message/list',
      method: 'get',
      params: {
        size: 20,
        current
      }
    })
  },
  // 用户公告列表
  getUserAnnouncementListApi(current = 1) {
    return api({
      url: 'notice_system/list',
      method: 'get',
      params: {
        size: 20,
        current
      }
    })
  },
  // 系统所有公告列表
  getSystemAnnouncementListApi(current = 1, size = 10) {
    return api({
      url: 'notice_system/all_list',
      method: 'get',
      params: {
        size,
        current
      }
    })
  },
  readAllFollowApi() {
    return api({
      url: 'follow_message/read_all',
      method: 'post',
      data: {}
    })
  },
  readSingleFollowApi(id) {
    return api({
      url: 'follow_message/read',
      method: 'post',
      data: {
        id
      }
    })
  },
  readAllAnnouncementApi() {
    return api({
      url: 'notice_system/read_all',
      method: 'post',
      data: {}
    })
  },
  readSingleAnnouncementApi(announcementId) {
    return api({
      url: 'notice_system/read',
      method: 'post',
      data: {
        id: announcementId
      }
    })
  },
  getSingleAnnouncementDetailApi(id) {
    return api({
      url: 'notice_system/details',
      method: 'get',
      params: {
        id
      }
    })
  },
  // state 0 创建 1 发布
  createAnnouncementApi(title = '', content = '', author = '') {
    return api({
      url: 'notice_system/create',
      method: 'post',
      data: {
        notice_title: title,
        notice_content: content,
        subscribe: author,
        state: 0
      }
    })
  },
  createAndPublishAnnouncementApi(title = '', content = '', author = '') {
    return api({
      url: 'notice_system/create',
      method: 'post',
      data: {
        notice_title: title,
        notice_content: content,
        subscribe: author,
        state: 1
      }
    })
  },
  updateAnnouncementApi(id, title = '', content = '', author = '') {
    return api({
      url: 'notice_system/update',
      method: 'post',
      data: {
        id,
        notice_title: title,
        notice_content: content,
        subscribe: author,
        state: 0
      }
    })
  },
  updateAndPublishAnnouncementApi(id, title = '', content = '', author = '') {
    return api({
      url: 'notice_system/update',
      method: 'post',
      data: {
        id,
        notice_title: title,
        notice_content: content,
        subscribe: author,
        state: 1
      }
    })
  },
  // type 0 撤回 1 发布
  publishOrRevokeAnnouncementApi(id, state) {
    return api({
      url: 'notice_system/announce_or_recall',
      method: 'post',
      data: {
        id,
        state
      }
    })
  },
  deleteAnnouncementApi(id) {
    return api({
      url: 'notice_system/delete',
      method: 'post',
      data: {
        id
      }
    })
  },
  // 图片上传接口
  uploadRichTextImage(data) {
    return api({
      url: 'oss/upload_notice',
      method: 'post',
      file: true,
      data
    })
  },
  // 检查用户是否有这个菜单权限
  checkUserMenuPermissionApi(code) {
    return api({
      url: 'admin_permission/is_have_menu',
      method: 'get',
      params: {
        code
      }
    })
  }
}
export { requests }
