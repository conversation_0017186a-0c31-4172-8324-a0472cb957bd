import api from '../index.js'
const requests = {
  // 创建角色
  createRole (data) {
    return api({
      url: 'admin_role/create_role',
      method: 'post',
      data
    })
  },
  // 角色编辑
  updateRole (data) {
    return api({
      url: 'admin_role/update_role',
      method: 'post',
      data
    })
  },
  //   角色删除
  deleteRole (data) {
    return api({
      url: 'admin_role/delete_role',
      method: 'post',
      data
    })
  },

  // 角色权限列表
  rolePermissionList (data) {
    return api({
      url: 'admin_role/role_permission_list',
      method: 'get',
      params: data
    })
  },
  // 角色权限更新
  rolePermissionSelect (data) {
    return api({
      url: 'admin_role/role_permission_select',
      method: 'post',
      data
    })
  },
  // 频道权限列表
  roleListCategoryTree (data) {
    return api({
      url: 'category/role_list_category_tree',
      method: 'get',
      params: data
    })
  },
  // 频道权限更新
  updateRoleCategory (data) {
    return api({
      url: 'admin_role/update_role_category',
      method: 'post',
      data
    })
  },
  // 获取圈子权限列表
  getCircleList (data) {
    return api({
      url: 'circle/permission_list',
      method: 'get',
      params: data
    })
  },
  // 圈子权限更新
  updateCircle (data) {
    return api({
      url: 'circle/permission_update',
      method: 'post',
      data
    })
  },
  // 获取角色下的用户
  getUserList (data) {
    return api({
      url: 'admin_role/role_user_list',
      method: 'get',
      params: data
    })
  }
}
export { requests }
