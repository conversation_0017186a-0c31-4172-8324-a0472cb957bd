import api from '../index.js'
const requests = {
  // 马甲号同步
  vest_sync(data) {
    return api({
      url: 'vest/vest_sync',
      method: 'get',
      params: data
    })
  },
  // 热门配置详情
  detail(data) {
    return api({
      url: 'comment_config/detail',
      method: 'get',
      params: data
    })
  },
  // 热门评论配置
  update(data) {
    return api({
      url: 'comment_config/update',
      method: 'post',
      data
    })
  },
}
export {
  requests
}
