import api from '../index.js'

// 获取权限
export function getJurisdiction() {
  return api({
    url: 'get_menus',
    method: 'get',
    params: {}
  })
}
// 获取列表权限
export function getTableJurisdiction() {
  return api({
    url: 'column/find',
    method: 'get',
    params: {}
  })
}
// 登录
export function ssoLogin(data) {
  return api({
    url: 'sso/login',
    method: 'post',
    data: data
  })
}
// 本地登录
export function debugLogin(data) {
  return api({
    url: 'debug_login',
    method: 'post',
    data: data
  })
}
// 登出
export function logout() {
  return api({
    url: 'logout',
    method: 'get'
  })
}
