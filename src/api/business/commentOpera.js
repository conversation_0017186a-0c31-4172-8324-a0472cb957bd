import api from '../index.js'

const requests = {
  // 创建或修改自定义时间
  create_or_update(data) {
    return api({
      url: 'user_defined_time/create_or_update',
      method: 'post',
      data
    })
  },
  // 获取个人自定义时间
  get_time_array(data) {
    return api({
      url: 'user_defined_time/get_time_array',
      method: 'get',
      params: data
    })
  },
  // 更改每日每人举报数量
  detail_report_edit(data) {
    return api({
      url: 'comment_config/detail_report_edit',
      method: 'get',
      params: data
    })
  },
  // 获取举报配置详情
  detail_report(data) {
    return api({
      url: 'comment_config/detail_report',
      method: 'get',
      params: data
    })
  },
  // 举报类型删除
  reportsDel(data) {
    return api({
      url: 'comment_reports_type/delete',
      method: 'get',
      params: data
    })
  },
  // 举报类型列表
  reportsList(data) {
    return api({
      url: 'comment_reports_type/list',
      method: 'get',
      params: data
    })
  },
  // 举报类型创建
  create(data) {
    return api({
      url: 'comment_reports_type/create',
      method: 'get',
      params: data
    })
  },
  // 稿件评论数
  count(data) {
    return api({
      url: 'comment_operation/comments/count_x',
      method: 'get',
      params: data
    })
  },
  // 批量创建
  batch_commit(data) {
    return api({
      url: 'comment_operation/task/batch_commit',
      method: 'post',
      data
    })
  },
  // 收藏马甲号
  vest_collect_add(data) {
    return api({
      url: 'vest_collect/vest_collect_add',
      method: 'get',
      params: data
    })
  },
  // 取消置顶、收藏
  vest_collect_delete(data) {
    return api({
      url: 'vest_collect/vest_collect_delete',
      method: 'get',
      params: data
    })
  },
  // 获取置顶和收藏列表
  vest_collect_list(data) {
    return api({
      url: 'vest_collect/vest_collect_list',
      method: 'get',
      params: data
    })
  },
  // 置顶马甲号
  vest_collect_top(data) {
    return api({
      url: 'vest_collect/vest_collect_top',
      method: 'get',
      params: data
    })
  },
  // 获取可关联组件列表
  getModelsList(data) {
    return api({
      url: 'activity/projects/query_by_component_type',
      method: 'get',
      params: data
    })
  },
  //   专题稿件列表
  subjectList(data) {
    return api({
      url: 'comment_operation/subject_detail',
      method: 'get',
      params: data
    })
  },
  //  批量通过
  batchAction(data) {
    return api({
      url: 'comment/batch_action',
      method: 'post',
      data
    })
  },
  // 取消、设置热评
  set_top(data) {
    return api({
      url: 'comment_top/set_top',
      method: 'post',
      data
    })
  },
  // 热评列表
  hot_comments(data) {
    return api({
      url: 'comment_operation/comments/floor_hot_comments',
      method: 'get',
      params: data
    })
  },
  // 马甲号列表
  vest_list(data) {
    return api({
      url: 'vest/vest_list',
      method: 'get',
      params: data
    })
  },
  // 马甲号是否已经用点过赞
  is_exists(data) {
    return api({
      url: 'vest_like/is_exists',
      method: 'get',
      params: data
    })
  },
  // 马甲号加权点赞
  like_save(data) {
    return api({
      url: 'vest_like/like_save',
      method: 'post',
      data
    })
  },
  // 后台评论回复
  vest_comment_reply(data) {
    return api({
      url: 'comment_operation/comments/vest_comment_reply',
      method: 'post',
      data
    })
  },
  // 盖楼展开接口
  get_more(data) {
    return api({
      url: 'comment_operation/comments/get_more',
      method: 'get',
      params: data
    })
  },
  // 评论列表接口
  floor_list(data) {
    return api({
      url: 'comment_operation/comments/floor_list',
      method: 'get',
      params: data
    })
  },
  // 更新评论任务
  taskUpdate(data) {
    return api({
      url: 'comment_operation/task/update',
      method: 'post',
      data
    })
  },
  // 保存新增评论任务
  taskSave(data) {
    return api({
      url: 'comment_operation/task/save',
      method: 'post',
      data
    })
  },
  //  马甲号
  shadow_list(data) {
    return api({
      url: 'comment_operation/task/shadow_list',
      method: 'get',
      params: data
    })
  },
  //  敏感词效验
  checkSensitiveWord(data) {
    return api({
      url: 'risk/comment/check_sensitive_word',
      method: 'get',
      params: data
    })
  },
  // 评论运营 评论明细接口
  get_detail(data) {
    return api({
      url: 'comment_operation/task/get_detail',
      method: 'get',
      params: data
    })
  },
  // 获取稿件列表
  getList(data) {
    return api({
      url: 'comment_operation/list',
      method: 'get',
      params: data
    })
  },
  //   获取状态数量
  getStateNum(data) {
    return api({
      url: 'comment_operation/task/get_state_number',
      method: 'get',
      params: data
    })
  },
  //   获取任务列表
  getTaskList(data) {
    return api({
      url: 'comment_operation/task/list',
      method: 'get',
      params: data
    })
  },
  // 停止/执行
  enableTask(data) {
    return api({
      url: 'comment_operation/task/enable',
      method: 'post',
      data: data
    })
  },
  // 操作记录
  getHistory(data) {
    return api({
      url: 'task_operator_log/list',
      method: 'get',
      params: data
    })
  },
  // 操作记录
  getOperateHistory(data) {
    return api({
      url: 'article_operator_log/list',
      method: 'get',
      params: data
    })
  },
  //  小编回复
  editorReplay(data) {
    return api({
      url: 'comment/editor_replay',
      method: 'post',
      data
    })
  }
}
export { requests }
