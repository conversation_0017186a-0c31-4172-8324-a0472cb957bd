import api from '../index.js'
import SSEClient from '../sseService'

const requests = {
  // 是否设置引导规则
  getStatus(data) {
    return api({
      url: 'comment_style_guide/settings/is_config',
      method: 'get',
      params: data
    })
  },
  // 查询用户引导规则
  getRule(data) {
    return api({
      url: 'comment_style_guide/settings/query',
      method: 'get',
      params: data
    })
  },
  // 保存用户引导规则
  saveRule(data) {
    return api({
      url: 'comment_style_guide/settings/save',
      method: 'post',
      data
    })
  },
  getStyleSSE(data, callback, errorCallback) {
    return new SSEClient('comment_operation/ai_generate_stream', data, callback, errorCallback)
  },
  getMyStyleSSE(data, callback, errorCallback) {
    return new SSEClient('comment_operation/ai_generate_with_style_stream', data, callback, errorCallback)
  },
  // 历史评论
  getHistory(data) {
    return api({
      url: 'comment_history/query',
      method: 'get',
      params: data
    })
  },
  // 清除历史评论
  clearHistory(data) {
    return api({
      url: 'comment_history/clear',
      method: 'post',
      data
    })
  }

}
export {
  requests
}
