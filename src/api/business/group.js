import api from '../index.js'
const requests = {
  // 频道分组列表
  getGroupListApi(data = {}) {
    return api({
      url: 'category_group/list',
      method: 'get',
      params: {
        ...data,
        desc_or_asc: 'asc'
      }
    })
  },
  // 获取用户的所有频道
  getChannelByUser(data) {
    return api({
      url: 'category/user_category_tree_by_user_id',
      method: 'get',
      params: data
    })
  },
  // 根据频道分组获取频道树
  getChannelByGroup(data) {
    return api({
      url: 'category/user_category_tree_by_group_id',
      method: 'get',
      params: data
    })
  },
  // 创建频道分组
  createGroup(data) {
    return api({
      url: 'category_group/create',
      method: 'post',
      data
    })
  },
  // 编辑频道分组
  editGroup(data) {
    return api({
      url: 'category_group/update',
      method: 'post',
      data
    })
  },
  // 分组修改绑定的频道
  updateGroupCategory(data) {
    return api({
      url: 'category_group/update',
      method: 'post',
      data
    })
  },
  // 删除频道分组
  deleteGroup(data) {
    return api({
      url: 'category_group/delete',
      method: 'post',
      data
    })
  }
  // 批量审核
  // batchActionAudit(data) {
  //   return api({
  //     url: 'comment/batch_action_audit',
  //     method: 'post',
  //     data
  //   })
  // }
}
export { requests }
