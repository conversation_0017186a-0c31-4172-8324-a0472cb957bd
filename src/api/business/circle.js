import api from '../index.js'
const requests = {
  // 获取圈子列表
  circleList(data) {
    return api({
      url: 'circle/list',
      method: 'get',
      params: data
    })
  },
  // 获取稿件列表
  getList(data) {
    return api({
      url: 'circle_management/all_article_list',
      method: 'get',
      params: data
    })
  },
  // 获取稿件列表
  getList2(data) {
    return api({
      url: 'circle_management/auditing_article_list',
      method: 'get',
      params: data
    })
  },
  // 获取稿件详情
  getDetails(data) {
    return api({
      url: 'article/detail',
      method: 'get',
      params: data
    })
  },
  // 获取待审核数量
  getAdoptNum(data) {
    return api({
      url: 'circle_management/get_count',
      method: 'get',
      params: data
    })
  },
  // 获取评论
  getComment(data) {
    return api({
      url: 'circle_management/comment/floor_list',
      method: 'get',
      params: data
    })
  },
  // 获取更多评论
  getMoreComment(data) {
    return api({
      url: 'circle_management/comment/get_more',
      method: 'get',
      params: data
    })
  },
  // 获取热评
  getHotComment(data) {
    return api({
      url: 'circle_management/comment/floor_hot_comments',
      method: 'get',
      params: data
    })
  },
  // 设为热评
  setHot(data) {
    return api({
      url: 'circle_management/comment/on_off_hot',
      method: 'post',
      data
    })
  },
  // 更改状态
  setStete(data) {
    return api({
      url: 'circle_management/comment/auditing',
      method: 'post',
      data
    })
  },
  // 马甲号是否已经用点过赞
  is_exists(data) {
    return api({
      url: 'vest_like/is_exists',
      method: 'get',
      params: data
    })
  },
  // 马甲号加权点赞
  like_save(data) {
    return api({
      url: 'vest_like/like_save',
      method: 'post',
      data
    })
  },
  // 获取数量
  getNumber(data) {
    return api({
      url: 'article_comment/get_count',
      method: 'get',
      params: data
    })
  },
  // 获取审核评论
  getAdopt(data) {
    return api({
      url: 'circle_management/comment/pending_delete_list',
      method: 'get',
      params: data
    })
  },
  // 判断上级评论是否已删除接口
  getParentCommentState(data) {
    return api({
      url: 'comment/get_parent_comment_state',
      method: 'get',
      params: data
    })
  },
  // 判断上级评论是否已通过接口
  getParentCommentState2(data) {
    return api({
      url: 'comment/pending_parent_comment',
      method: 'get',
      params: data
    })
  },
  // 判断是否能被撤销至待审核
  getDelType(data) {
    return api({
      url: 'reports/reports_exist',
      method: 'get',
      params: data
    })
  },
}
export { requests }
