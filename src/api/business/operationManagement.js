import api from '../index.js'

const requests = {
  //  敏感词效验
  checkSensitiveWord(data) {
    return api({
      url: 'risk/comment/check_sensitive_word',
      method: 'get',
      params: data
    })
  },
  //  热评管理列表
  list(data) {
    return api({
      url: 'comment_top/list',
      method: 'get',
      params: data
    })
  },

  //  取消热评接口
  setTop(data) {
    return api({
      url: 'comment_top/set_top',
      method: 'post',
      data
    })
  },
  // 搜索热评稿件列表
  searchArticle(data) {
    return api({
      url: 'comment/search_article',
      method: 'get',
      params: data
    })
  },
  // 查询热评
  queryTopList(data) {
    return api({
      url: 'comment_top/query_top_list',
      method: 'get',
      params: data
    })
  },
  //  热评排序接口
  updateSort(data) {
    return api({
      url: 'comment_top/update_sort',
      method: 'post',
      data
    })
  },
  //  黑名单列表接口
  blackList(data) {
    return api({
      url: 'blacklist/list',
      method: 'get',
      params: data
    })
  },
  //  添加黑名单接口
  add(data) {
    return api({
      url: 'blacklist/add',
      method: 'post',
      data
    })
  },
  //  黑名单恢复接口
  delete(data) {
    return api({
      url: 'blacklist/delete',
      method: 'post',
      data
    })
  },
  // 灌水用户列表接口
  waterUserList(data) {
    return api({
      url: 'spammer/user_list',
      method: 'get',
      params: data
    })
  },
  //  灌水用户恢复接口
  waterUserDelete(data) {
    return api({
      url: 'spammer/handle',
      method: 'post',
      data
    })
  },
  getHistory(data) {
    return api({
      url: 'spammer/opt_log',
      method: 'get',
      params: data
    })
  }
}
export { requests }
