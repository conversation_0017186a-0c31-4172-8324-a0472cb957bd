import api from '../index.js'
const requests = {
  // 排序列表
  sortList(data) {
    return api({
      url: 'comment_operation/comment_sort/list',
      method: 'get',
      params: data
    })
  },
  // 热评排序列表
  hotList(data) {
    return api({
      url: 'comment/search_article_top_comments',
      method: 'get',
      params: data
    })
  },
  // 热评上下调序
  hotSort(data) {
    return api({
      url: 'comment_top/update_sort',
      method: 'post',
      data
    })
  },
  // 上下调序
  sort(data) {
    return api({
      url: 'comment_operation/comment_sort/up_down_sort',
      method: 'post',
      data
    })
  },
  //   固定
  regular(data) {
    return api({
      url: 'comment_operation/comment_sort/move_position',
      method: 'post',
      data
    })
  },
  // 取消固定
  cancelRegular(data) {
    return api({
      url: 'comment_operation/comment_sort/cancel_fixed',
      method: 'post',
      data
    })
  },
  //   热评固定
  regular2(data) {
    return api({
      url: 'comment_operation/comment_sort/move_position_hot',
      method: 'post',
      data
    })
  },
  // 取消热评固定
  cancelRegular2(data) {
    return api({
      url: 'comment_operation/comment_sort/cancel_fixed_hot',
      method: 'post',
      data
    })
  },
  //   设置热评
  setHotComment(data) {
    return api({
      url: 'comment_top/set_top',
      method: 'post',
      data
    })
  },
  //   插入位置
  position(data) {
    return api({
      url: 'comment_operation/comment_sort/change_position',
      method: 'post',
      data
    })
  }
}
export { requests }
