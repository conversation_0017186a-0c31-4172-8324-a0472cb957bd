import api from '../index.js'
import api2 from '../jsonApi.js'

const requests = {
  // 获取项目列表信息
  projectList(data) {
    return api({
      url: 'activity/component/list_with_analytics',
      method: 'get',
      params: data
    })
  },
  //   观点组件列表
  getPointList(data) {
    return api({
      url: 'activity/project/dual_option_poll/query',
      method: 'get',
      params: data
    })
  },
  //   创建观点组件
  creatPoint(data) {
    return api({
      url: 'activity/project/dual_option_poll/create',
      method: 'post',
      data
    })
  },
  //   编辑观点组件
  editPoint(data) {
    return api({
      url: 'activity/project/dual_option_poll/update',
      method: 'post',
      data
    })
  },
  //   删除观点组件
  delPoint(data) {
    return api({
      url: 'activity/project/dual_option_poll/delete',
      method: 'post',
      data
    })
  },
  // 多选投票组件
  // 获取列表
  getMoreList(data) {
    return api({
      url: 'activity/project/more_option_poll/query',
      method: 'get',
      params: data
    })
  },
  getMoreDetail(data) {
    return api({
      url: 'activity/project/more_option_poll/get_details',
      method: 'get',
      params: data
    })
  },
  // 删除
  delMore(data) {
    return api({
      url: 'activity/project/more_option_poll/delete',
      method: 'post',
      data
    })
  },
  // 删除选项
  delMoreItem(data) {
    return api({
      url: 'activity/project/more_option_poll/delete_viewpoint',
      method: 'post',
      data
    })
  },
  // 添加
  creatMore(data) {
    return api2({
      url: 'activity/project/more_option_poll/create',
      method: 'post',
      data
    })
  },
  // 编辑
  editMore(data) {
    return api2({
      url: 'activity/project/more_option_poll/update',
      method: 'post',
      data
    })
  },
  // 解绑
  onUnbind(data) {
    return api2({
      url: 'activity/project/dual_option_poll/unbind',
      method: 'get',
      params: data
    })
  },
}

export { requests }
