import api from '../index.js'
const requests = {
  // 表情包列表
  list(data) {
    return api({
      url: 'expression_package/list',
      method: 'get',
      params: data
    })
  },
  // 图片上传接口
  upload_expression(data) {
    return api({
      url: 'oss/upload_expression',
      method: 'post',
      file: true,
      data
    })
  },
  // 图片审核接口
  audit_sync(data) {
    return api({
      url: 'image_audit/audit_sync',
      method: 'post',
      data
    })
  },
  // 新增表情包接口
  create(data) {
    return api({
      url: 'expression_package/create',
      method: 'post',
      data
    })
  },
  // 表情包管理启/停用接口
  enable(data) {
    return api({
      url: 'expression_package/enable',
      method: 'post',
      data
    })
  },
  // 表情包排序接口
  sort(data) {
    return api({
      url: 'expression_package/sort',
      method: 'post',
      data
    })
  },
  // 表情包管理删除接口
  delete(data) {
    return api({
      url: 'expression_package/delete',
      method: 'post',
      data
    })
  },
  // 修改表情接口
  update(data) {
    return api({
      url: 'expression_package/update',
      method: 'post',
      data
    })
  },
  // 通过表情包查找表情接口
  query_by_package(data) {
    return api({
      url: 'expression/query_by_package',
      method: 'get',
      params: data
    })
  },
  // 表情包日志列表接口
  operator_log(data) {
    return api({
      url: 'expression_operator_log/list',
      method: 'get',
      params: data
    })
  },
  // 表情后台保存接口
  save(data) {
    return api({
      url: 'expression/save',
      method: 'post',
      data
    })
  },
  // 收藏图片列表
  collectList(data) {
    return api({
      url: 'image_collection/list',
      method: 'get',
      params: data
    })
  },
  // 收藏图片删除
  collectDel(data) {
    return api({
      url: 'image_collection/batch_delete',
      method: 'post',
      data
    })
  },
  // 收藏图片保存
  collectSave(data) {
    return api({
      url: 'image_collection/add',
      method: 'post',
      data
    })
  },
}

export { requests }
