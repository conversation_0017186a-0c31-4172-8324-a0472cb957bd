import api from '../index.js'
import api2 from '../index2.js'

const requests = {
  // 频道列表
  listCategoryTree(data) {
    return api({
      url: 'category/list_category_tree',
      method: 'get',
      params: data
    })
  },
  // 频道数据
  channel_info(data) {
    return api({
      url: 'statistics_data/channel_info',
      method: 'get',
      params: data
    })
  },
  // 用户行为
  user_action_info(data) {
    return api({
      url: 'statistics_data/user_action_info',
      method: 'get',
      params: data
    })
  },
  // 访问数据
  copilot_info(data) {
    return api2({
      url: 'statistics_data/copilot_info',
      method: 'get',
      params: data
    })
  },
  // 导出数据
  export(data) {
    return api2({
      url: 'statistics/export_excel',
      method: 'get',
      params: data
    })
  },
  // 数据看板榜单接口
  rank_list(data) {
    return api({
      url: 'statistics/rank_list',
      method: 'get',
      params: data
    })
  },
  // 看板评论总数接口
  comment_number(data) {
    return api({
      url: 'statistics/get_comment_number',
      method: 'get',
      params: data
    })
  },
  // 待审核评论条数接口
  count(data) {
    return api({
      url: 'comment/count_pedding_comment',
      method: 'get',
      params: data
    })
  },
  // 时间筛选接口
  date(data) {
    return api({
      url: 'statistics/rank_list_time',
      method: 'get',
      params: data
    })
  }
}
export { requests }
