import { v4 as uuidv4 } from 'uuid'
import { sha256 } from 'js-sha256'

class SSEClient {
  constructor(endpoint, data, callback, errorCallback, interval = 100) {
    this.endpoint = endpoint
    this.data = data
    this.callback = callback
    this.errorCallback = errorCallback
    this.interval = interval
    this.eventQueue = []
    this.timer = null

    this.requestId = uuidv4()
    this.timestamp = new Date().getTime()
    this.signature = sha256(`${this.requestId}&&${this.timestamp}&&cK1!dJ0&aA0`)
    this.baseUrl = import.meta.env.VITE_OPEN_PROXY === 'true' ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL
    this.url = `${this.baseUrl}${this.endpoint}?${new URLSearchParams(this.data).toString()}`
    this.controller = new AbortController()

    this.initConnection()
    this.startTimer()
  }

  initConnection() {
    const headers = new Headers({
      'Content-Type': 'application/json',
      'X-REQUEST-ID': this.requestId,
      'X-TIMESTAMP': this.timestamp.toString(),
      'X-SIGNATURE': this.signature,
      'Accept': 'text/event-stream'
    })

    const requestOptions = {
      method: 'GET',
      headers: headers,
      signal: this.controller.signal,
      cache: 'no-cache'
    }

    fetch(this.url, requestOptions).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      this.handleStream(response.body.getReader())
    }).catch(err => {
      if (this.userCancelled) return
      if (this.errorCallback) {
        this.errorCallback(err)
      }
      console.error('Failed to fetch:', err)
    })
  }

  handleStream(reader) {
    const decoder = new TextDecoder()
    let accumulatedText = ''

    const processText = text => {
      accumulatedText += text
      let lastNewlineIndex
      while ((lastNewlineIndex = accumulatedText.indexOf('\n\n')) !== -1) {
        let processedText = accumulatedText.substring(0, lastNewlineIndex)
        accumulatedText = accumulatedText.substring(lastNewlineIndex + 2)
        this.processEvent(processedText)
      }
    }

    const read = () => {
      reader.read().then(({ done, value }) => {
        if (done) {
          if (accumulatedText.length > 0) processText('')
          console.log('SSE stream completed')
          return
        }
        processText(decoder.decode(value, { stream: true }))
        read()
      }).catch(err => {
        console.error('Error reading the stream', err)
      })
    }
    read()
  }

  processEvent(eventText) {
    const { event, data } = this.parseText(eventText)
    if (event && data) {
      this.eventQueue.push({ event, data })
    }
  }

  parseText(text) {
    const eventRegex = /^event:\s*(.*)$/m
    const dataRegex = /^data:\s*(.*)$/m

    let eventMatch = text.match(eventRegex)
    let dataMatch = text.match(dataRegex)

    return {
      event: eventMatch ? eventMatch[1].trim() : null,
      data: dataMatch ? dataMatch[1].trim() : null
    }
  }

  startTimer() {
    this.timer = setInterval(() => {
      if (this.eventQueue.length > 0) {
        const { event, data } = this.eventQueue.shift()
        this.callback(event, data)
      }
    }, this.interval)
  }

  close() {
    this.userCancelled = true // 用户主动关闭
    this.controller.abort()
    clearInterval(this.timer)
    console.log('SSE connection closed')
  }
}

export default SSEClient
