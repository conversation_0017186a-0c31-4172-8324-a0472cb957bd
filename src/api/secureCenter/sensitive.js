import api from '../index.js'
import jsonApi from '../jsonApi.js'
const requests = {
  // 敏感词列表
  sensitiveList(data) {
    return api({
      url: 'risk_sensitive_word/get_page',
      method: 'get',
      params: data
    })
  },
  //   新增
  add(data) {
    return jsonApi({
      url: 'risk_sensitive_word/add',
      method: 'post',
      data
    })
  },
  //   编辑
  edit(data) {
    return api({
      url: 'risk_sensitive_word/modify',
      method: 'post',
      data
    })
  },
  //   删除
  del(data) {
    return api({
      url: 'risk_sensitive_word/delete',
      method: 'post',
      data
    })
  },
  //   删除
  allDel(data) {
    return api({
      url: 'risk_sensitive_word/batch_delete',
      method: 'post',
      data
    })
  },
  // 获取词库列表
  getLibList: (params) => {
    return api({
      url: 'risk_sensitive_word_lib/get_page',
      method: 'get',
      params
    })
  },

  // 添加词库
  addLib: (data) => {
    return jsonApi({
      url: 'risk_sensitive_word_lib/add',
      method: 'post',
      data
    })
  },

  // 删除词库
  deleteLib: (data) => {
    return api({
      url: 'risk_sensitive_word_lib/delete',
      method: 'post',
      data
    })
  },
  // 编辑词库
  editLib: (data) => {
    return api({
      url: 'risk_sensitive_word_lib/modify',
      method: 'post',
      data
    })
  },

  // 获取词库详情
  getLibDetail: (params) => {
    return api({
      url: 'risk_sensitive_word_lib/get_detail',
      method: 'get',
      params
    })
  }
}
export { requests }

