import api from '../index.js'
import jsonApi from '../jsonApi.js'
const requests = {
  // 白名单列表
  list(data) {
    return api({
      url: 'white_list/user_list',
      method: 'get',
      params: data
    })
  },
  //   新增
  add(data) {
    return api({
      url: 'white_list/add',
      method: 'post',
      data
    })
  },
  // 移除
  del(data) {
    return api({
      url: 'white_list/remove',
      method: 'post',
      data
    })
  },
  // 日志
  history(data) {
    return api({
      url: 'white_list/opt_log_list',
      method: 'get',
      params: data
    })
  },
}
export {
  requests
}
