import api from '../index.js'
const requests = {
  // 规则列表
  ruleListApi(data) {
    return api({
      url: 'govern_rule/get_rule_list',
      method: 'get',
      params: data
    })
  },
  //   创建规则
  createRuleApi(data) {
    return api({
      url: 'govern_rule/create',
      method: 'post',
      data
    })
  },
  //   编辑规则
  updateRuleApi(data) {
    return api({
      url: 'govern_rule/update',
      method: 'post',
      data
    })
  },
  //   删除规则
  deleteRuleApi(ruleId) {
    return api({
      url: 'govern_rule/delete',
      method: 'post',
      data: {
        rule_id: ruleId
      }
    })
  },
  //   启用规则
  enableRuleApi(ruleId, state = 0) {
    return api({
      url: 'govern_rule/used',
      method: 'post',
      data: {
        rule_id: ruleId,
        state
      }
    })
  },
  // 频道列表
  ruleChannelListApi(ruleId = '') {
    return api({
      url: 'govern_rule/get_channel_list',
      method: 'get',
      params: {
        rule_id: ruleId
      }
    })
  },
  // 操作日志
  operationLogApi(ruleId, page = 1, pageSize = 10) {
    return api({
      url: 'govern_rule/log',
      method: 'post',
      data: {
        // govern_rule_id: ruleId,
        current: page,
        size: pageSize
      }
    })
  }
}
export { requests }
