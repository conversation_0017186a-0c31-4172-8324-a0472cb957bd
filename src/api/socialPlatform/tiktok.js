import api from '../index.js'
import api2 from '../jsonApi.js'
import SSEClient from '../sseService'
const requests = {
  // 抖音

  politician_info_generate(data, callback, errorCallback) {
    return new SSEClient('social/politician_info_generate', data, callback, errorCallback)
  },
  get_count(data) {
    return api({
      url: 'douyin/alarm/get_count',
      method: 'get',
      params: data
    })
  },
  handle(data) {
    return api2({
      url: 'douyin/alarm/handle',
      method: 'post',
      data
    })
  },
  get_videos(data) {
    return api({
      url: 'douyin/alarm/get_videos',
      method: 'get',
      params: data
    })
  },
  get_comments(data) {
    return api({
      url: 'douyin/alarm/get_comments',
      method: 'get',
      params: data
    })
  }
}

export { requests }
