import api from '../index.js'
import api2 from '../jsonApi.js'
const requestsWeiXin = {
  get_count(data) {
    return api({
      url: 'wechat/alarm/get_count',
      method: 'get',
      params: data
    })
  },
  handle(data) {
    return api2({
      url: 'wechat/alarm/handle',
      method: 'post',
      data
    })
  },
  get_videos(data) {
    return api({
      url: 'wechat/alarm/get_videos',
      method: 'get',
      params: data
    })
  },
  get_comments(data) {
    return api({
      url: 'wechat/alarm/get_comments',
      method: 'get',
      params: data
    })
  }
}

export { requestsWeiXin }
