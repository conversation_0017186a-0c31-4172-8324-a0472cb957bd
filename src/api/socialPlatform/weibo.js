import api from '../index.js'
import api2 from '../jsonApi.js'
const requestsWeibo = {
  get_count(data) {
    return api({
      url: 'weibo/alarm/get_count',
      method: 'get',
      params: data
    })
  },
  handle(data) {
    return api2({
      url: 'weibo/alarm/handle',
      method: 'post',
      data
    })
  },
  get_videos(data) {
    return api({
      url: 'weibo/alarm/get_statuses',
      method: 'get',
      params: data
    })
  },
  get_comments(data) {
    return api({
      url: 'weibo/alarm/get_comments',
      method: 'get',
      params: data
    })
  }
}

export { requestsWeibo }
