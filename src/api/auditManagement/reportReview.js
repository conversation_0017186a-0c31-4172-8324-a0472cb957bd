import api from '../index.js'
const requests = {
  // 获取举报列表
  getList(data) {
    return api({
      url: 'reports/reports_list',
      method: 'get',
      params: data
    })
  },
  // 获取子列表
  getChildList(data) {
    return api({
      url: 'reports/reports_list_by_comment_id',
      method: 'get',
      params: data
    })
  },
  // 获取举报类型列表
  getTypeList(data) {
    return api({
      url: 'comment_reports_type/list',
      method: 'get',
      params: data
    })
  },
  // 频道列表
  listCategoryTree(data) {
    return api({
      url: 'category/list_category_tree',
      method: 'get',
      params: data
    })
  },
  // 修改状态
  changeState(data) {
    return api({
      url: 'reports/edit',
      method: 'get',
      params: data
    })
  },
}

export {
  requests
}
