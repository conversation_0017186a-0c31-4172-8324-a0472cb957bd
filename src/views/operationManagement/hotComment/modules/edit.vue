<template>
  <el-drawer
    id="drawer" v-model="drawer" title="稿件内热评排序" :with-header="true" show-close="true" size="700px"
    @open="open" @close="close"
  >
    <div class="elem">
      <el-input
        v-model.trim="searchData.searchword"
        :placeholder="searchData.comment_search_type=='TM_ID'?'请输入稿件ID':'请输入稿件标题'" class="input-with-select"
        clearable
      >
        <template #prepend>
          <el-select v-model="searchData.comment_search_type" style="width: 115px;" @change="change">
            <el-option v-for="item in type" :key="item" :label="item.label" :value="item.key" />
          </el-select>
        </template>
        <template #append>
          <el-button :icon="Search" :disabled="!searchData.searchword" @click="editSearch" />
        </template>
      </el-input>
    </div>
    <ul class="elem1">
      <li v-for="(item,index) of data.list" v-show="item.article_title" :key="index" :class="{che:item.che}">
        <span @click="check(index)">【{{ item.article_id }}】{{ item.article_title }}</span>
        <el-button type="primary" size="small" @click="deleteClick(index)">删除</el-button>
      </li>
    </ul>
    <el-table id="tabel-component" :data="data.records.comments" :height="data.height">
      <el-table-column v-slot="scope" label="排序" width="100">
        <el-button
          v-auth="'comment_top:update_sort'" :disabled="scope.row.num==0" type="primary"
          :icon="ArrowUpBold" size="small" circle @click="sortClick(scope.row,scope.row.id,'UP')"
        >
        </el-button>
        <el-button
          v-auth="'comment_top:update_sort'" :disabled="scope.row.num==(count-1)" type="primary"
          :icon="ArrowDownBold" size="small" circle @click="sortClick(scope.row,scope.row.id,'DOWN')"
        >
        </el-button>
      </el-table-column>
      <el-table-column prop="content" label="热门评论" />
    </el-table>
  </el-drawer>
</template>
<script setup>
import {
  requests
} from '@/api/business/operationManagement'
import {
  ArrowUpBold,
  ArrowDownBold,
  Search
} from '@element-plus/icons'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  ref, defineExpose
} from 'vue'
const count = ref(null)
// 评论类型
const type = ref([
  {
    key: 'TM_ID',
    label: '稿件ID'
  },
  {
    key: 'ARTICLE_TITLE',
    label: '文章标题'
  }
])
const emit = defineEmits(['openEdit'])
const drawer = ref(false) // 界面显示
// 下拉选择的值
const data = ref({
  height: null,
  num: 0, // 当前选择的稿子序号
  // 所有数据
  list: null,
  //   选中稿子的数据
  records: {
    comments: null
  }
})
const searchData = ref({
  comment_search_type: 'TM_ID', // TM_ID 天目号  ARTICLE_TITLE 文章标题
  searchword: '',
  size: '',
  current: ''
})
onMounted(() => {

})
const change = () => {
  searchData.value.searchword = ''
  data.value.list = null
  data.value.records.comments = null
  data.value.num = 0
  count.value = 0

}
// 打开弹窗时
const open = () => {
  data.value.height = document.documentElement.clientHeight - 310
}
const close = () => {
  searchData.value.searchword = ''
  data.value.list = null
  data.value.records.comments = null
  data.value.num = 0
  count.value = 0

}

// 点击搜索
const editSearch = () => {
  requests.searchArticle(searchData.value)
    .then(res => {
      data.value.list = res.data.list
      data.value.list[data.value.num].che = true
      data.value.records = data.value.list[data.value.num]
      count.value = data.value.records.comments.length
      for (let i = 0; i < data.value.records.comments.length; i++) {
        data.value.records.comments[i].num = i
      }

    })
    .catch(error => {
      console.log(error)
    })
}
// 选中稿子的热评
const check = val => {
  console.log('点击的序号', val, data.value.num)
  data.value.num = val

  data.value.list.forEach(elem => {
    elem.che = false
  })
  setTimeout(() => {
    data.value.list[val].che = true

  })
  data.value.records = data.value.list[val]
  count.value = data.value.records.comments.length

  for (let i = 0; i < data.value.records.comments.length; i++) {
    data.value.records.comments[i].num = i
  }
}
// 点击删除
const deleteClick = index => {
  //   if (data.value.list.length == 1) {
  //       data.value.list = null
  //       data.value.records.comments = null

  //   } else {
  //       data.value.list.slice(index, 1)
  //       data.value.records.comments = null
  //     data.value.num=0
  //   }
  data.value.list.splice(index, 1)
  console.log('删除之后的', data.value.list)
  data.value.records.comments = null
  count.value = 0
  data.value.num = 0
}
// 排序点击
const sortClick = (row, id, val) => {
  console.log('行', row)
  requests.updateSort({
    comment_id: id,
    article_id: data.value.records.article_id,
    sort: val
  })
    .then(res => {
      ElMessage({
        type: 'success',
        message: '排序成功！'
      })
      editSearch()

    })
    .catch(error => {
      console.log(error)
    })
}
defineExpose({
  drawer
})

</script>
<style lang="scss" scoped>
  .elem {
    display: flex;
    align-items: center;
    justify-content: center;
    .el-input {
      flex: 1;
    }
  }
  .elem1 {
    padding: 0;
    max-height: 120px;
    overflow-y: scroll;
    li {
      justify-content: space-between;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 5px;
      &.che {
        background: #ebebeb;
      }
      & > span {
        padding-right: 15px;
        font-size: 14px;
      }
      .el-button {
        height: 30px;
      }
    }
  }
  .elem2 {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
  }

</style>
