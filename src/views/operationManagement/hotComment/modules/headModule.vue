<template>
  <div class="head-box">
    <div class="left">
      <el-button v-auth="'comment_top:update_sort'" type="primary" @click="open">稿件内热评排序</el-button>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->
      <el-cascader
        v-model="cascadeValue"
        :options="options" :show-all-levels="false" clearable collapse-tags :props="optionsProps"
        placeholder="全部频道" @change="change"
      />

      <el-select
        v-model="searchData.comment_search_type"
        placeholder="请选择" style="margin-right: 0;"
        @change="typeChange"
      >
        <el-option v-for="item of commentSearchType" :key="item.key" :label="item.label" :value="item.key">
        </el-option>
      </el-select>
      <el-input
        v-if="searchData.comment_search_type!='ARTICLE_TITLE'"
        v-model="searchData.searchword"
        placeholder="请输入关键词" class="input-with-select" style="width: 300px;" clearable
      >
      </el-input>
      <!-- 稿件标题搜索的实时下拉 -->
      <el-select
        v-if="searchData.comment_search_type=='ARTICLE_TITLE'"
        v-model="title_search_data"
        style="width: 300px;margin-right: 0;" filterable remote :remote-method="remoteMethod" :loading="loading" clearable
        popper-class="selectElem" :teleported="false" placeholder="请输入稿件关键字" @change="titleChange"
      >
        <el-option v-for="item in title_select_Data" :key="item.key" :label="item.label" :value="item.label" />
      </el-select>
      <el-button type="primary" :icon="Search" @click="handSearch">
        搜索
      </el-button>
    </div>
  </div>
</template>
<script setup>
import {
  requests
} from '@/api/business/auditManagement'
import {
  Search
} from '@element-plus/icons'
import {
  ref,
  onMounted,
  defineEmits,
  defineExpose
} from 'vue'
// 组件传参声明方式
const props = defineProps({
  selectData: {
    type: Array
  }
})
// 组件接收事件
const emit = defineEmits(['search', 'openEdit'])
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})
// 稿件标题下拉的实时数据
const title_select_Data = ref([])
// 稿件标题时的关键字
const title_search_data = ref(null)
// 搜索类型
const commentSearchType = ref([
  {
    key: 'CONTENT',
    label: '评论内容'
  },
  {
    key: 'ARTICLE_TITLE',
    label: '稿件标题'
  },
  {
    key: 'COMMENT_PERSON',
    label: '评论人'
  }
])
const loading = ref(false)
// 搜索参数
const searchData = ref({
  category_id: '',
  comment_source: '', // 频道名称
  comment_search_type: 'CONTENT', // 搜索类型（ALL：全部  CONTENT：评论内容 COMMENT_SOURCE：评论来源 ARTICLE_TITLE：稿件标题 COMMENT_PERSON：评论人）
  searchword: '', // 关键词
  size: 100, // 每页条数
  current: 1 // 分页页码 默认1

})
onMounted(() => {
  requests.listCategoryTree().then(res => {
    console.log(res)
    // options.value = res.data.category_list

    let arr = res.data.category_list
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1)

      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1)
          } else {
            for (let a = 0; a < arr[i].children[k].children.length; a++) {
              if (arr[i].children[k].children[a].permission == 0) {
                arr[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr
    })
  })
  // 初始
  emit('search', searchData.value)

})
const typeChange = () => {
  title_search_data.value = ''
  searchData.value.searchword = ''
}
const remoteMethod = query => {
  console.log('query', query)
  title_select_Data.value = []
  if (query) {
    loading.value = true
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.size = 10
    newSearchData.searchword = query
    newSearchData.state = 'PASS'
    newSearchData.time_type = 'ALL'
    newSearchData.start_time = ''
    newSearchData.end_time = ''
    newSearchData.comment_type = 'ALL'
    // 请求
    requests.searchList(newSearchData).then(res => {
      loading.value = false
      for (let i = 0; i < res.data.list.length; i++) {
        title_select_Data.value.push({
          key: res.data.list[i].article_id,
          label: `【${res.data.list[i].article_id}】${res.data.list[i].article_title}`
        })
      }

    })
  } else {
    title_select_Data.value = []
  }
}
const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.category_id = data.join(',')
  } else {
    searchData.value.category_id = ''
  }
  handSearch()
}
const open = () => {
  emit('openEdit', true)
}
// 点击搜索 每次当前页码变为1
const handSearch = () => {
  searchData.value.current = 1
  if (searchData.value.comment_search_type == 'ARTICLE_TITLE') {
    console.log('title_search_data.value', title_search_data.value)
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.comment_search_type = 'ARTICLE_ID'
    newSearchData.searchword = title_search_data.value.slice(title_search_data.value.indexOf('【') + 1, title_search_data.value.indexOf('】'))
    emit('search', newSearchData)
  } else {
    emit('search', searchData.value)
  }
}
// 稿件远程 选中
const titleChange = val => {
  let newSearchData = JSON.parse(JSON.stringify(searchData.value))
  newSearchData.comment_search_type = 'ARTICLE_ID'
  newSearchData.searchword = val.slice(val.indexOf('【') + 1, val.indexOf('】'))
  emit('search', newSearchData)
}
defineExpose({
  searchData,
  title_search_data
})

</script>
<style lang="scss" scoped>
  ::v-deep .selectElem {
    width: 300px;
  }
  ::v-deep .selectElem .el-select-dropdown__item {
    white-space: pre-wrap;
    height: auto;
    line-height: 24px;
    padding: 5px 16px;
  }
  ::v-deep .selectElem .el-select-dropdown__item.hover,
  .selectElem .el-select-dropdown__item:hover {
    background-color: #ebebeb;
  }
  .head-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .right {
      & > .el-select {
        width: 150px;
        margin-right: 10px;
      }
      .el-input-group {
        width: 400px;
      }
    }
  }

</style>
<style>
  .input-with-select .el-input-group__append {
    background-color: #1890ff;
    color: #fff;
  }
  .el-cascader {
    margin-right: 12px;
  }

</style>
