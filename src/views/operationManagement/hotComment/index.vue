<template>
  <AbsoluteContainer>
    <page-main>
      <headModel
        ref="searchBox"
        @search="search"
        @openEdit="openEdit"
      />
      <div id="tabel-component">
        <el-table
          v-loading="loading"
          size="default"
          :data="data.records"
          :height="data.height"
          class="table"
          border stripe highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            align="center"
            prop="auto_pk"
            label="序号"
            width="150"
          />
          <el-table-column
            align="center"
            prop="content"
            label="评论内容"
          />
          <!-- <el-table-column
            align="center" label="评论内容" #default="scope" min-width="220">
                <p>{{scope.row.content.text}}</p>
                <p v-if="scope.row.content.text1" class="elem">{{scope.row.content.text1}}</p>
            </el-table-column> -->
          <el-table-column
            align="center"
            prop="source"
            label="来源频道"
            width="100"
          />
          <el-table-column
            align="center"
            prop="article_title"
            label="稿件标题"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="comment_account"
            label="评论人"
            width="115"
          />
          <el-table-column
            align="center"
            prop="created_at"
            label="评论时间"
            width="135"
          />
          <el-table-column
            align="center"
            prop="count_like"
            label="点赞数"
            width="100"
          />
          <el-table-column
            v-slot="scope"
            align="center"
            label="操作"
            width="130"
          >
            <!-- v-auth="'comment_top:set_top'" -->
            <el-button
              v-if="scope.row.top==true"
              v-auth="'comment_top:set_top'"
              text

              @click="setTopOff(scope.row)"
            >
              <span style="color:#0B82FD;">取消热评</span>
            </el-button>
            <!-- v-auth="'comment_top:set_top'" -->
            <el-button
              v-else
              v-auth="'comment_top:set_top'"
              text

              @click="setTopOn(scope.row)"
            >
              <span style="color:#0B82FD;">设为热评</span>
            </el-button>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="page">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.total"
          :page-size="data.size"
          :current-page="data.current"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import {
  requests
} from '@/api/business/operationManagement'
import headModel from './modules/headModule.vue'
import Edit from './modules/edit.vue'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  Delete
} from '@element-plus/icons-vue'
import {
  computed,
  nextTick,
  ref
} from 'vue'
const loading = ref(false)
const searchBox = ref(null)
const editBox = ref(null)

// 表格数据
const data = ref({
  height: null,
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName(
    'el-pagination'
  )[0].offsetHeight - 20
  data.value.height = height - bottom
})
// 搜索接口
const search = val => {
  let obj
  // 如果是稿件标题
  if (val.comment_search_type == 'ARTICLE_TITLE') {
    obj = JSON.parse(JSON.stringify(val))
    obj.comment_search_type = 'ARTICLE_ID'
    obj.searchword = searchBox.value.title_search_data.slice(searchBox.value.title_search_data.indexOf('【') + 1, searchBox.value.title_search_data.indexOf('】'))
  } else {
    obj = val
  }
  loading.value = true
  requests.list(obj)
    .then(res => {
      console.log('res', res)
      loading.value = false
      data.value.records = res.data.pg.records
      data.value.total = res.data.pg.total
      data.value.current = obj.current
    })
    .catch(error => {
      loading.value = false

      console.log(error)
    })
}
// 取消热评
const setTopOff = row => {
  console.log(row)
  ElMessageBox.confirm(
    '取消后，可重新设为热评！',
    '确定取消该评论为热评？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      confirmButtonClass: 'aa',
      type: 'Warning',
      icon: markRaw(Delete)
    }).then(() => {
    loading.value = true
    // 通过接口
    requests.setTop({
      action: 'OFF',
      comment_id: row.comment_id,
      article_id: row.article_id
    })
      .then(res => {
        if (res.code == 0) {
          ElMessage({
            type: 'success',
            message: '取消成功！'
          })
        } else {
          ElMessage({
            type: 'error',
            message: '取消失败！'
          })
        }
        search(searchBox.value.searchData)
      })
      .catch(error => {
        ElMessage({
          type: 'error',
          message: '取消失败！'
        })
      })

  })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 设置热评
// 取消热评
const setTopOn = row => {
  console.log(row)
  ElMessageBox.confirm(
    '设为热评后，可重新取消！',
    '确定设置该评论为热评？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      confirmButtonClass: 'aa',
      type: 'Warning',
      icon: markRaw(Delete)
    }).then(() => {
    // 通过接口
    requests.setTop({
      action: 'ON',
      comment_id: row.comment_id,
      article_id: row.article_id
    })
      .then(res => {
        if (res.code == 0) {
          ElMessage({
            type: 'success',
            message: '设置成功！'
          })
        } else {
          ElMessage({
            type: 'error',
            message: '设置失败！'
          })
        }
        search(searchBox.value.searchData)
      })
      .catch(error => {
        ElMessage({
          type: 'error',
          message: '设置失败！'
        })
      })

  })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 打开热评管理器
const openEdit = val => {
  console.log(val)
  editBox.value.drawer = val
}
// 选择每页几条
const handleSizeChange = val => {
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}

</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);

  .elem {
    color: #409eff;
  }
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
