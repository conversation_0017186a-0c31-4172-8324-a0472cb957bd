<template>
  <el-drawer
    v-model="drawer"
    title="添加黑名单"
    show-close="true"
    size="500px"
    @close="close"
  >
    <div class="elem">
      <span>请输入：</span>
      <el-input
        v-model="editData.keyword"
        placeholder="请输入用户ID、用户昵称" clearable
      />
    </div>
    <div class="elem1">
      <span>冻结原因：</span>
      <el-radio-group
        v-model="reason"
        style="width: 300px;"
        @change="radioChang"
      >
        <el-radio label="垃圾广告营销">垃圾广告营销</el-radio>
        <el-radio label="违法有害信息">违法有害信息</el-radio>
        <el-radio label="侮辱谩骂内容">侮辱谩骂内容</el-radio>
        <el-radio label="嘲讽/不友善内容">嘲讽/不友善内容</el-radio>
        <el-radio label="淫秽色情信息">淫秽色情信息</el-radio>
        <el-radio label="其他">其他</el-radio>
        <el-input
          v-if="data.textShow"
          v-model.trim="data.textarea"
          resize="none"
          :rows="2"
          type="textarea"
          placeholder="请输入原因，不超过15个字"
          maxlength="15"
        />
      </el-radio-group>
    </div>
    <div class="elem2">
      <span>冻结时间：</span>
      <el-radio-group v-model="editData.freeze_time">
        <el-radio label="5分钟">5分钟</el-radio>

        <el-radio label="24小时">24小时</el-radio>
        <el-radio label="一周">一周</el-radio>
        <el-radio label="一个月">一个月</el-radio>
        <el-radio label="永久">永久</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div style="flex: auto;">
        <el-button @click="cancelClick">取消</el-button>
        <el-button
          type="primary"
          :disabled="!editData.keyword||!reason||!editData.freeze_time"
          @click="sureClick"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup>
import {
  requests
} from '@/api/business/operationManagement'
import {
  ref
} from 'vue'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  defineExpose
} from 'vue'
const emit = defineEmits(['init'])

const drawer = ref(false)
const reason = ref(null)
// 接口数据
const editData = ref({
  keyword: '', // 关键字
  reason: null, // 冻结原因
  freeze_time: null// 冻结时长
})
const data = ref({
  textShow: false, // 显示其他输入框
  textarea: null, // 其他输入框文字
  inputData: '', // 输入框的值
  select: '稿件id', // 下拉的值
  // 表格数据
  records: [
    {
      text: '数据数据数据数据数据数据数据数'
    },
    {
      text: '数据数据数据数据数据数据数据数'
    }
  ]
})

// 单选
const radioChang = val => {
  if (val == '其他') {
    data.value.textShow = true

  } else {
    data.value.textShow = false
    data.value.textarea = ''
  }
}
// 取消
const cancelClick = () => {
  for (let key in editData.value) {
    editData.value[key] = ''
  }
  drawer.value = false
}
//  确定
const sureClick = () => {
  // 判断 如果是其他 就把其他的值赋到 冻结原因里
  if (data.value.textarea) {
    editData.value.reason = data.value.textarea
  } else {
    editData.value.reason = reason.value
  }
  requests.add(editData.value)
    .then(res => {
      console.log('res', res)
      ElMessage({
        type: 'success',
        message: '添加成功！'
      })
      emit('init')
      drawer.value = false
    })
    .catch(error => {
      console.log(error)
    })

  console.log(editData.value)
}
const close = () => {
  editData.value = {
    keyword: '', // 关键字
    reason: null, // 冻结原因
    freeze_time: null// 冻结时长
  }
  reason.value = ''
}
defineExpose({
  drawer
})

</script>
<style lang="scss" scoped>
.elem,
.elem1,
.elem2 {
  display: flex;
  // justify-content: center;
  margin-bottom: 20px;
  & > span {
    display: block;
    width: 100px;
    height: 40px;
    line-height: 40px;
  }
  .el-autocomplete {
    flex: 1;
  }
  .el-input {
    resize: none;
  }
}
.elem3 {
  display: flex;
  justify-content: flex-end;
}
</style>
