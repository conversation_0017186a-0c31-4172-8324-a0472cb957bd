<template>
  <div class="head-box">
    <div class="left">
      <el-button
        v-auth="'endpoint_blacklist:add'"

        type="primary" @click="open"
      >
        添加黑名单
      </el-button>
    </div>
    <div class="right">

      <el-input
        v-model="searchData.keyword"

        placeholder="请输入关键词"
        class="input-with-select" clearable
      >
        <template #prepend>
          <el-select
            v-model="searchData.search_type"

            placeholder="请选择"
            style="width:100px;"
          >
            <el-option
              v-for="item of searchTypeData"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
        <template #append>
          <el-button

            type="primary" :icon="Search"
            style="display: flex;align-items: center;"
            @click="handSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>
<script setup>
import {
  Search
} from '@element-plus/icons'
import {
  ref,
  onMounted,
  defineEmits,
  defineExpose
} from 'vue'
// 组件接收事件
const emit = defineEmits(['search', 'openEdit'])
// 搜索类型
const searchTypeData = ref([
  {
    key: 0,
    label: '用户id'
  },
  {
    key: 1,
    label: '用户昵称'
  }

])
// 搜索参数
const searchData = ref({
  search_type: 0, //	搜索类型（1：用户id 2：用户昵称）
  keyword: '', // 关键词
  size: 10, // 每页条数
  current: 1 // 分页页码 默认1
})
onMounted(() => {
  // 初始
  emit('search', searchData.value)
})
const open = () => {
  emit('openEdit', true)
}
// 点击搜索 每次当前页码变为1
const handSearch = () => {
  searchData.value.current = 1
  emit('search', searchData.value)
}
defineExpose({
  searchData
})
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .right {
    & > .el-select {
      width: 150px;
      margin-right: 10px;
    }

    .el-input-group {
      width: 400px;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
