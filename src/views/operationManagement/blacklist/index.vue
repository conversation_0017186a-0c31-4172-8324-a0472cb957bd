<template>
  <AbsoluteContainer>
    <page-main>
      <headModel
        ref="searchBox"
        @search="search"
        @openEdit="openEdit"
      />
      <Edit
        ref="editBox"
        @init="init"
      />
      <div id="tabel-component">
        <el-table
          v-loading="loading"
          size="default"
          :data="data.records"
          :height="data.height"
        >
          <el-table-column
            prop="auto_pk"
            label="序号"
            width="50"
          />
          <el-table-column
            prop="app_user_id"
            label="用户ID"
          />
          <el-table-column
            prop="app_user_name"
            label="用户昵称"
          ></el-table-column>
          <el-table-column
            prop="reason"
            label="冻结原因"
          />
          <el-table-column
            prop="freeze_time"
            label="冻结时长"
          />
          <el-table-column
            prop="operator_admin_name"
            label="操作人"
          />
          <el-table-column
            prop="created_at"
            label="操作时间"
          />
          <el-table-column
            v-slot="scope"
            label="操作"
            width="100"
          >
            <el-button
              v-auth="'endpoint_blacklist:delete'"
              text
              style="padding-left: 0;"
              @click="setClick(scope.row.app_user_id)"
            >
              <span style="color:#0B82FD;">恢复</span>
            </el-button>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="page">
        <el-pagination
          background
          :page-size="data.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.total"
          :current-page="data.current"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import {
  requests
} from '@/api/business/operationManagement'
import headModel from './modules/headModule.vue'
import Edit from './modules/edit.vue'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  Delete
} from '@element-plus/icons-vue'
import {
  computed,
  nextTick,
  ref
} from 'vue'
const loading = ref(false)
const searchBox = ref(null)
const editBox = ref(null)

// 表格数据
const data = ref({
  total: 2, // 总条目数
  size: 10, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName(
    'el-pagination'
  )[0].offsetHeight - 20
  data.value.height = height - bottom
})
// 搜索接口
const search = obj => {
  requests.blackList(obj)
    .then(res => {
      loading.value = false
      data.value.records = res.data.blacklist.records
      data.value.total = res.data.blacklist.total
      //   data.value.current = obj.current;
    })
    .catch(error => {
      console.log(error)
    })
}
// 恢复
const setClick = val => {
  ElMessageBox.confirm(
    '确认该用户从黑名单中恢复？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'Warning'
    }).then(() => {
    // 恢复接口
    requests.delete({
      app_user_id: val
    })
      .then(res => {
        console.log(res)
        ElMessage({
          type: 'success',
          message: '恢复成功'
        })
        search(searchBox.value.searchData)

      })
      .catch(error => {
        console.log(error)
      })

  })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}

// 打开添加黑名单
const openEdit = val => {
  editBox.value.drawer = val
}
// 选择每页几条
const handleSizeChange = val => {
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}
const init = () => {
  search(searchBox.value.searchData)
}

</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  .elem {
    color: #409eff;
  }
}
#tabel-component {
  height: 100%;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
