<template>
  <AbsoluteContainer>
    <page-main>
      <div class="head-box" style="margin-bottom:15px">
        <div class="left" style="margin-top:-5px">
          <el-button v-auth="'expression_package:create'" type="primary" @click="handleAdd">
            创建表情包
          </el-button>
        </div>
        <div class="right" @click="handelHistory">
          <span class="icon"></span>操作日志
        </div>
      </div>

      <div id="tabel-component">
        <el-table
          v-loading="loading"
          size="default"
          stripe
          highlight-current-row
          :data="dataValue"
          @selection-change="handleSelectionChange"
        >
          <el-table-column v-slot="scope" v-auth="'expression_package:sort'"  prop="px" label="排序" width="130">
            <span
              :class="scope.$index == 0 ? 'topDis' : 'top'"
              @click="
                scope.$index > 0 && handelSort(scope.row, scope.$index, 'top')
              "
            ></span>
            <span
              :class="scope.$index == dataValue.length - 1 ? 'dowDis' : 'dow'"
              @click="
                scope.$index < dataValue.length - 1 &&
                  handelSort(scope.row, scope.$index, 'dow')
              "
            ></span>
          </el-table-column>
          <el-table-column
            prop="name"
            label="表情包名称"
            width="400"
             align="left"
          >
          </el-table-column>
          <el-table-column
            v-slot="scope"
            align="center"
            label="表情包封面"
          >
            <img :src="scope.row.url" class="rowImg" />
          </el-table-column>
          <el-table-column
            v-slot="scope"
            align="center"
            label="表情数量"
          >
            {{ scope.row.expression_count + "个" }}
          </el-table-column>
          <el-table-column prop="create_by" label="创建人"  />
          <el-table-column  prop="created_at" label="创建时间"  width="150"/>
          <el-table-column v-slot="scope" v-auth="'expression_package:enable'"  label="状态" width="140">
            <el-switch
              v-model="scope.row.enable"
              inline-prompt
              :active-value="1"
              :inactive-value="0"
              @change="beforeConfirm(scope.row)"
            />{{ scope.row.enable === 1 ? " 已启用" : " 已停用" }}
          </el-table-column>
          <el-table-column
            v-slot="scope"
            align="center"
            label="操作"
            width="220"
          >
            <el-button v-auth="" text>
              <span v-auth="'expression_package:update'" style="color: #0b82fd" @click="handleEdit(scope.row)">编辑</span>
            </el-button>
            <el-button v-auth="" text>
              <span v-auth="'expression:save'" style="color: #0b82fd" @click="handleManage(scope.row.id)">管理</span>
            </el-button>
            <el-button v-auth="" text>
              <span v-auth="'expression_package:delete'" style="color: #0b82fd" @click="handleDel(scope.row.id,scope.row.name)">删除</span>
            </el-button>
          </el-table-column>
        </el-table>
      </div>

      <Add ref="add" @success="handleAddSucc" />
      <Manage ref="manage" @success="handleAddSucc" />
      <History ref="history" />
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import Add from './modules/add.vue'
import Manage from './modules/manage.vue'
import History from './modules/history.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, ref } from 'vue'
import { requests } from '@/api/business/emote'
const loading = ref(true)
const add = ref(null)
const manage = ref(null)
const history = ref(null)
const dataValue = ref([])

onMounted(() => {
  init()
})
// 操作日志
const handelHistory = () => {
  history.value.drawer = true
  requests.operator_log({ size: 100 }).then(res => {
    if (res.code === 0) {
      history.value.data = res.data
    }
  })
}
// 添加后刷新数据
const handleAddSucc = () => {
  init()
}
// 创建表情包
const handleAdd = () => {
  add.value.drawer = true
  add.value.isEdit = false

}
// 编辑表情包
const handleEdit = value => {
  add.value.drawer = true
  add.value.isEdit = true
  add.value.uploadSuccess = true
  add.value.infoValue.name = value.name
  add.value.infoValue.id = value.id
  add.value.infoValue.url = value.url
  add.value.fileList.push({ url: value.url })
}
// 管理表情包
const handleManage = id => {
  requests.query_by_package({ expression_package_id: id }).then(res => {
    if (res.code === 0) {
      manage.value.expression_package_id = id
      const updatedData = res.data.list.map((item, index) => ({
        ...item,
        sort_number: index + 1
      }))
      manage.value.gridItems = updatedData
      manage.value.drawer = true
    }
  })
}
// 删除
const handleDel = (id, name) => {
  console.log(id)
  ElMessageBox.confirm(
    '是否确认删除<span style="color: red;">' + name + '</span>?', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'Warning',
      dangerouslyUseHTMLString: true
    })
    .then(() => {
      requests.delete({ id }).then(res => {
        if (res.code === 0) {
          ElMessage.closeAll()
          ElMessage.success('删除成功')
          init()
        }
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 获取数据接口
const init = obj => {
  loading.value = false
  requests.list().then(res => {
    if (res.code === 0) {
      dataValue.value = res.data.list
    }
  })
}
// 表情包管理启/停用
const beforeConfirm = self => {
  self.enable === 1 ? (self.enable = 0) : (self.enable = 1)

  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(
      `是否${self.enable ? '停用' : '启用'}<span style="color: red;">${
        self.name
      }</span>?`,
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
      .then(() => {
        const value = {
          id: self.id,
          enable: self.enable === 1 ? 0 : 1
        }
        requests.enable(value).then(res => {
          if (res.code === 0) {
            ElMessage.closeAll()
            ElMessage.success('状态修改成功')
            init()
          }
        })
      })
      .catch(() => {})
  })
}

// 排序
const handelSort = (value, index, state) => {
  console.log(index, 'index')
  const val = {
    newSortNumber:
      state === 'top' ? value.sort_number - 1 : value.sort_number + 1,
    oldSortNumber: value.sort_number,
    id: value.id
  }
  requests.sort(val).then(res => {
    if (res.code === 0) {
      ElMessage.closeAll()
      ElMessage.success('排序成功')
      init()
    }
  })
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  .elem {
    color: #409eff;
  }
}
#tabel-component {
  height: 100%;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .right {
    width: 80px;
    font-size: 14px;
    float: right;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      background: url("@/assets/images/history.png") no-repeat;
      background-size: 20px auto;
      float: left;
    }
  }
}
.rowImg {
  width: 40px;
  height: 40px;
}
.top {
  width: 16px;
  height: 16px;
  background: url("@/assets/images/top.png") no-repeat;
  background-size: 16px auto;
  display: inline-block;
  margin: 0 3px;
  cursor: pointer;
}
.topDis {
  width: 16px;
  height: 16px;
  background: url("@/assets/images/top.png") no-repeat;
  background-size: 16px auto;
  display: inline-block;
  margin: 0 3px;
  filter: grayscale(100%);
  cursor: pointer;
}
.dow {
  width: 16px;
  height: 16px;
  background: url("@/assets/images/dow.png") no-repeat;
  background-size: 16px auto;
  display: inline-block;
  margin: 0 3px;
  cursor: pointer;
}
.dowDis {
  width: 16px;
  height: 16px;
  background: url("@/assets/images/dow.png") no-repeat;
  background-size: 16px auto;
  display: inline-block;
  margin: 0 3px;
  filter: grayscale(100%);
  cursor: pointer;
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
