<template>
  <el-drawer
    v-model="drawer"
    :visible="isDrawerVisible"
    direction="rtl"
    title="管理-表情包"
    :size="800"
    :before-close="handleClose"
    @open="handleDrawerOpen"
  >
    <el-form-item class="custom-form-item" style="margin-top: 50px">
      <template #label>
        <span>表情包管理</span>
      </template>
      <el-button type="primary" @click="addButton">点击添加</el-button>
      <div class="fontEdit">
        请上传表情，大小限制500KB，支持png、jpg、gif，长按可拖动排序。
      </div>
    </el-form-item>
    <el-dialog
      v-model="dialogVisible"
      title="添加"
      width="35%"
      :before-close="handleClose"
      style="margin-left: 32.5%"
    >
      <el-form-item>
        <span class="label-text">*</span>
        <span>添加表情</span>
        <el-upload
          v-model:file-list="fileListImg"
          list-type="picture-card"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :limit="1"
          :accept="'.jpg,.jpeg,.png,.gif'"
          style="margin-left: 40px"
          v-if="!uploadSuccess"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        <div v-else>
          <div  class="uploaded-image">
            <img v-for="(item, index) in fileListImg" :key="index" :src="item.url" >
            <span @click="re_upload">重新上传</span>
            </div>
        </div>
      </el-form-item>
      <el-form-item class="custom-form-item">
        <template #label>
          <span class="label-text">*</span>
          <span>图片名称</span>
        </template>
        <el-input
          v-model="addImg.name"
          style="width: 250px; margin-left: 30px"
          placeholder="请输入表情名称"
          :maxlength="4"
          :show-word-limit="true"
          class="custom-input"
        />
      </el-form-item>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAdd"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
    <div ref="sortableContainer" class="grid-container">
      <div
        v-for="(item, index) of gridItems"
        :key="item.id"
        :ref="`gridItem-${index}`"
        class="grid-item"
      >
        <div class="header">
          <span class="num">{{ index + 1 }}</span>
          <span class="del" @click="handelDel(index, item.name)"></span>
        </div>

        <img :src="item.url" class="imgBox" />
        <div class="text">{{ item.name }}</div>
      </div>
    </div>
     <template #footer>
        <div style="flex: auto;">
        <el-button :loading="loading" type="primary" @click="submitForm()">
          保 存
        </el-button>
    </div>
     </template>
  </el-drawer>
</template>

<script setup name='manage'>
import { ref, onBeforeMount, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import Sortable from "sortablejs";
import { requests } from "@/api/business/emote";
const drawer = ref(false);
const sortableContainer = ref(null);
const isDrawerVisible = ref(false);
const emit = defineEmits(["success"]);
const expression_package_id = ref(null);
const uploadSuccess = ref(false);
const addImg = ref({
  id: "",
  url: "",
  name: "",
  expression_package_id: "",
  sort_number: "",
});
const gridItems = ref([]);
// 预览图
const fileListImg = ref([{ url: "" }]);
// 编辑弹窗
const dialogVisible = ref(false);
const beforeImg = ref(null);

const handleDrawerOpen = () => {
  nextTick(() => {
    const sortable = new Sortable(sortableContainer.value, {
      animation: 200,
      draggable: ".grid-item",
      onEnd: handleSortEnd,
    });
  });
};

// 拖拽更新数据
const handleSortEnd = (event) => {
  const { oldIndex, newIndex } = event;
  // 更新 gridItems 数组
  gridItems.value.splice(newIndex, 0, gridItems.value.splice(oldIndex, 1)[0]);
};
// 重新上传
const re_upload = ()=>{
  fileListImg.value = [];
  uploadSuccess.value = false
}
// 删除单个表情
const handelDel = (index, name) => {
  ElMessageBox.confirm(
    '是否确认删除<span style="color: red;">' + name + "</span>?",
    {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: true,
    }
  ).then(() => {
    gridItems.value.splice(index, 1);
  });
};
// 点击添加按钮
const addButton = () => {
  if (gridItems.value.length < 30) {
    dialogVisible.value = true;
    fileListImg.value = [];
    addImg.value.id = "";
    addImg.value.name = "";
    addImg.value.url = "";
    addImg.value.expression_package_id = expression_package_id.value;
  } else {
    ElMessage.error("最多添加30个表情");
  }
};
// 确认添加图片
const handleAdd = () => {
  if (addImg.value.name === "") {
    ElMessage.error("图片名称不能为空");
  } else if (addImg.value.url === "") {
    ElMessage.error("表情未上传，或审核未通过");
  } else {
    const newItem = {
      id: addImg.value.id,
      url: addImg.value.url,
      name: addImg.value.name,
      expression_package_id: addImg.value.expression_package_id,
      sort_number: addImg.value.sort_number,
    };

    // 取id最大值
    const maxID = Math.max(...gridItems.value.map((item) => item.sort_number));
    newItem.sort_number = maxID + 1;

    gridItems.value.push(newItem);
    dialogVisible.value = false;
    uploadSuccess.value = false     
  }
};
// 上传前的验证
const beforeUpload = (file) => {
  console.log(file, "file");
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png" || file.type === "image/gif";
  const isLt10K = file.size < 500 * 1024;
  if (isJpgOrPng && isLt10K) {
    const formData = new FormData();
    formData.append("file", file); // 将文件添加到表单中
    requests.upload_expression(formData).then((res) => {
      if (res.code === 0) {
        beforeImg.value = res.data.url;

        ElMessage.warning({
          message: "等待图片审核中",
          duration: 0, // 将持续时间设置为 0
        });
        requests.audit_sync({ image_url: beforeImg.value }).then((v) => {
          if (v.data.resultCode === 0) {
            ElMessage.closeAll();
            ElMessage.success(v.data.resultMsg);
            addImg.value.url = beforeImg.value;
            fileListImg.value.push({ url: beforeImg.value });
            uploadSuccess.value = true     
          } else {
            ElMessage.closeAll();
            // 提示是否确认上传
            ElMessageBox.confirm(
              '<span style="color: red;">' +
                v.data.resultMsg +
                "</span> 是否确认上传?",
              {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning",
                dangerouslyUseHTMLString: true,
              }
            ).then(() => {
              addImg.value.url = beforeImg.value;
              fileListImg.value.push({ url: beforeImg.value });
              uploadSuccess.value = true     
            });
          }
        });
      } else {
        ElMessage.error("图片上传失败");
      }
    });
    return false;
  }
  if (!isJpgOrPng) {
    ElMessage.error("只能上传 JPG、PNG、GIF格式的图片");
  }
  if (!isLt10K) {
    ElMessage.error("图片大小不能超过 500KB");
  }
  return isJpgOrPng && isLt10K;
};
// 保存
const submitForm = () => {
  const dataInfo = gridItems.value;
  console.log(dataInfo, "dataInfo");
  const updatedData = dataInfo.map(({ id, ...rest }) => ({
    id: id,
    ...rest,
  }));
  // 重排sort_number
  const modifiedExpressions = updatedData.map((v, index) => {
    v.sort_number = index + 1;
    return v;
  });
  requests
    .save({
      expression_list: JSON.stringify(modifiedExpressions),
      expression_package_id: expression_package_id.value,
    })
    .then((res) => {
      if (res.code === 0) {
        ElMessage.closeAll();
        ElMessage.success("保存成功");
        emit("success");
        drawer.value = false;
      } else {
        ElMessage.error(res.data.message);
      }
    });
};
defineExpose({
  drawer,
  gridItems,
  expression_package_id,
});
</script>

<style lang='scss' scoped>
 .uploaded-image {
    width: 148px;
    height: 148px;
    position: relative;
    margin-left: 45px;
    img{
      width: 148px;
      height: 148px;
      object-fit: cover;
      position: absolute;
      left: 0;
      top:0;
    }
    span{
      width: 148px;
      height: 30px;
      display: block;
      line-height: 30px;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      text-align: center;
      position: absolute;
      bottom: 0;
      left: 0;
      cursor: pointer;
    }
  }
.grid-container {
  width: 640px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 10px;
  margin-left: 75px;
}
.fontEdit {
  width: 100%;
  color: #808080;
  margin-top: 10px;
}
.label-text {
  color: red;
}
.addImgStyle {
  width: 148px;
  height: 148px;
  margin-left: 40px;
  border-radius: 7px;
}
.custom-zoom-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: none !important; /* 隐藏放大图标 */
  /* 添加自定义图标样式 */
}
.grid-item {
  border: 1px solid #dcdcdc;
  height: 140px;
  padding: 10px;
  color: #808080;
  font-size: 12px;
  cursor: pointer;
  position: relative;
  border-radius: 4px;
  .num {
    position: absolute;
    left: 10px;
    top: 10px;
  }
  .edit {
    position: absolute;
    right: 24px;
    top: 10px;
    width: 14px;
    height: 14px;
    background: url("@/assets/images/edit.png") no-repeat;
    display: none;
  }
  .del {
    position: absolute;
    right: 5px;
    top: 10px;
    width: 14px;
    height: 14px;
    background: url("@/assets/images/del.png") no-repeat;
    display: none;
  }
  .imgBox {
    width: 60px;
    height: 60px;
    position: absolute;
    left: 26px;
    top: 35px;
  }
  .text {
    position: absolute;
    top: 105px;
    width: 100%;
    left: 0;
    text-align: center;
  }
  &:hover {
    .del {
      display: block;
    }
    .edit {
      display: block;
    }
  }
}
</style>
