<template>
  <el-drawer
    id="drawer"
    v-model="drawer"
    title="操作日志"
    :with-header="true"
    show-close="true"
    size="950px"
    class="drawer"
  >
    <el-table :data="data.records" style="width: 100%" :max-height="750">
      <el-table-column v-slot="scope" label="序号">
        {{ scope.$index + 1 }}
      </el-table-column>
      <el-table-column prop="expression_package_name" label="表情包名称" />
      <el-table-column prop="expression_name" label="图片名称" />
      <el-table-column prop="operation_person" label="操作人员" />
      <el-table-column prop="created_at" label="操作时间" width="180" />
      <el-table-column prop="item_name" label="操作事项" />
    </el-table>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        background
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :page-size="data.size"
        :current-page="data.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </el-drawer>
</template>

<script setup name='history'>
import { ref, onMounted, defineExpose } from 'vue'
import { requests } from '@/api/business/emote'
const data = ref([])
const drawer = ref(false)
const current = ref(null)
const size = ref(100)
const handleCurrentChange = newValue => {
  current.value = newValue
  search()
}
const handleSizeChange = newValue => {
  size.value = newValue
  search()
}
const search = () => {
  requests
    .operator_log({ current: current.value, size: size.value })
    .then(res => {
      if (res.code === 0) {
        data.value = res.data
      }
    })
}
defineExpose({
  drawer,
  data
})
</script>
<style>
  .drawer .el-drawer__header{
    margin-bottom: 0 !important;
  }
</style>
<style lang="scss" scoped>
.page {
  display: flex;
  justify-content: center;
  height: 45px;
  margin-top: 20px;
}
</style>
