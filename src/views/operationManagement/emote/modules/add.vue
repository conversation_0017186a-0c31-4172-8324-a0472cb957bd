<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    :title="isEdit ? '编辑表情包' : '创建表情包'"
    :size="800"
    :before-close="handleClose"
  >
    <el-form>
      <el-form-item class="custom-form-item">
        <template #label>
          <span class="label-text">*</span>
          <span>表情包名称</span>
        </template>
        <el-input
          v-model="infoValue.name"
          style="width: 428px"
          placeholder="请输入表情名称"
          :maxlength="5"
          :show-word-limit="true"
          class="custom-input"
        />
      </el-form-item>

      <el-form-item class="custom-form-item">
        <template #label>
          <span class="label-text">*</span>
          <span>表情包封面</span>
        </template>
        <div style="width: 100%; color: #808080; margin-bottom: 20px">
          请上传表情包封面图，大小限制500KB，支持png、jpg。
        </div>
        <el-upload
          v-model:file-list="fileList"
          list-type="picture-card"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :limit="1"
          :accept="'.jpg,.jpeg,.png'"
           v-if="!uploadSuccess"
        >
          <el-icon ><Plus /></el-icon>
        </el-upload>
        <div v-else>
          <div  class="uploaded-image">
            <img v-for="(item, index) in fileList" :key="index" :src="item.url" >
            <span @click="re_upload">重新上传</span>
            </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
        <div style="flex: auto;">
        <el-button :loading="loading" type="primary" @click="submitForm()">
        保 存
        </el-button>
      </div>
      </template>
  </el-drawer>
</template>

<script setup>
import { ref, defineExpose, onMounted, nextTick, defineEmits } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { requests } from "@/api/business/emote";
const emit = defineEmits(["success"]);
const drawer = ref(false);
const isEdit = ref(false);
const uploadSuccess = ref(false);
const infoValue = ref({
  id: "",
  name: "",
  url: "",
});
const beforeImg = ref(null);
const fileList = ref([]);

// 删除图片
const handleRemove = (uploadFile, uploadFiles) => {
  infoValue.value.url = "";
};

// 关闭窗口
const handleClose = () => {
  drawer.value = false;
  clear();
};
// 上传前的验证
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
  const isLt10K = file.size < 500 * 1024;

  if (isJpgOrPng && isLt10K) {
    const formData = new FormData();
    formData.append("file", file); // 将文件添加到表单中
    requests.upload_expression(formData).then((res) => {
      if (res.code === 0) {
        beforeImg.value = res.data.url;

        ElMessage.warning({
          message: "等待图片审核中",
          duration: 0, // 将持续时间设置为 0
        });
        requests.audit_sync({ image_url: beforeImg.value }).then((v) => {
          if (v.data.resultCode === 0) {
            ElMessage.closeAll();
            ElMessage.success(v.data.resultMsg);
            infoValue.value.url = beforeImg.value;
            fileList.value.push({ url: beforeImg.value });
            uploadSuccess.value = true
          } else {
            ElMessage.closeAll();
            // 提示是否确认上传
            ElMessageBox.confirm(
              '<span style="color: red;">' +
                v.data.resultMsg +
                "</span> 是否确认上传?",
              {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning",
                dangerouslyUseHTMLString: true,
              }
            ).then(() => {
              infoValue.value.url = beforeImg.value;
              fileList.value.push({ url: beforeImg.value });
              uploadSuccess.value = true            
            });
          }
        });
      } else {
        ElMessage.error("图片上传失败");
      }
    });
    return false;
  }

  if (!isJpgOrPng) {
    ElMessage.error("只能上传 JPG 或 PNG 格式的图片");
  }
  if (!isLt10K) {
    ElMessage.error("图片大小不能超过 500KB");
  }
  return isJpgOrPng && isLt10K;
};

// 清空内容
const clear = () => {
  infoValue.value = {
    id: "",
    name: "",
    url: "",
  };
  ElMessage.closeAll();
  beforeImg.value = "";
  fileList.value = [];
  uploadSuccess.value = false
};
// 重新上传
const re_upload = ()=>{
  fileList.value = [];
  uploadSuccess.value = false
}
// 保存
const submitForm = () => {
  if (infoValue.value.name === "") {
    ElMessage.error("表情包名称不能为空");
  } else if (infoValue.value.url === "") {
    ElMessage.error("表情包封面未上传，或审核未通过");
  } else {
    if (isEdit.value) {
      // 更新
      requests.update(infoValue.value).then((res) => {
        if (res.code === 0) {
          drawer.value = false;
          clear();
          ElMessage.closeAll();
          ElMessage.success("更新成功");
          emit("success");
        }
      });
    } else {
      // 新增
      requests.create(infoValue.value).then((res) => {
        if (res.code === 0) {
          drawer.value = false;
          clear();
          ElMessage.closeAll();
          ElMessage.success("创建成功");
          emit("success");
        }
      });
    }
  }
};
defineExpose({
  drawer,
  isEdit,
  infoValue,
  fileList,
  beforeImg,
  uploadSuccess
});
</script>

<style lang="scss" scoped>
 .uploaded-image {
    width: 148px;
    height: 148px;
    position: relative;
    img{
      width: 148px;
      height: 148px;
      object-fit: cover;
      position: absolute;
      left: 0;
      top:0;
    }
    span{
      width: 148px;
      height: 30px;
      display: block;
      line-height: 30px;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      text-align: center;
      position: absolute;
      bottom: 0;
      left: 0;
      cursor: pointer;
    }
  }
.addBox {
  color: #808080;
}
.custom-input ::placeholder {
  color: #dcdcdc;
}
.label-text {
  color: red;
}
</style>
