<template>
  <page-main>
    <headModel ref="searchBox" @search="handleSearch" @add="handleAdd" />
    <div id="tabel-component">
      <el-table
        v-loading="loading"
        :data="data.records"
        :height="data.height"
        highlight-current-row
      >
        <el-table-column prop="id" label="组件ID" width="80" :show-overflow-tooltip="true" />
        <el-table-column
          v-if="data.used === 1"
          prop="article_title"
          label="关联稿件标题"
          min-width="140"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="project_name"
          label="组件标题"
          min-width="100"
          :show-overflow-tooltip="true"
        />

        <el-table-column prop="creator" label="创建人" width="120" :show-overflow-tooltip="true" />
        <el-table-column prop="created_at_as_string" label="创建时间" width="180" />

        <el-table-column
          v-slot="scope"
          align="center"
          label="操作"
          :width="data.used === 0 ? 140 : 170"
          fixed="right"
        >
          <el-button
            v-if="data.used === 0"
            v-auth="'more_option_poll_project:update'"
            text
            style="padding-left: 0; margin-left: 0"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="data.used === 0"
            v-auth="'more_option_poll_project:delete'"
            text
            style="margin-left: 0"
            @click="handleDel(scope.row)"
          >
            删除
          </el-button>
          <el-button
            v-if="data.used === 1"
            text
            style="margin-left: 0"
            @click="handleDetail(scope.row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="data.used === 1"
            text
            style="margin-left: 0"
            @click="unbind(scope.row, 1)"
          >
            解绑
          </el-button>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        small
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :page-size="data.size"
        :current-page="data.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <Add ref="add" :data="editData" @init="handleSearch" />
    <Detail ref="datail" />
  </page-main>
</template>
<script setup>
import Add from './modules/add.vue'
import Detail from './modules/details.vue'
import headModel from './modules/headModule.vue'
import { ref, onMounted } from 'vue'
import { requests } from '@/api/business/interact'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
const route = useRoute(),
  router = useRouter()
const add = ref(null)
const loading = ref(false)
const datail = ref(null)
const data = ref({
  height: 0,
  records: [],
  total: 0,
  size: 100,
  current: 1,
  used: 0,
  start_time: '',
  end_time: '',
  project_name: '',
  article_title: ''
})
const editData = ref({})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  console.log(document.getElementsByClassName('el-pagination'))
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 20
  data.value.height = height - bottom
  init()
})
// 解绑
function unbind(val, num) {
  console.log(val)
  requests
    .onUnbind({
      project_id: val.id,
      activity_url: val.url,
      states: num
    })
    .then(res => {
      if (num === 1) {
        ElMessageBox.confirm(res.data.msg, {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          unbind(val, 2)
        })
      } else {
        if (res.code === 0) {
          ElMessage({
            type: 'success',
            message: '解绑成功！'
          })
          init()
        }
      }
    })
}
// 初始化
function init() {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    start_time: data.value.start_time,
    end_time: data.value.end_time,
    project_name: data.value.project_name,
    article_title: data.value.article_title,
    used: data.value.used
  }
  loading.value = true
  requests
    .getMoreList(searchData)
    .then(res => {
      loading.value = false
      data.value.total = res.data.total
      data.value.records = res.data.records
    })
    .catch(() => {
      loading.value = false
    })
}
// 搜索
function handleSearch(val) {
  data.value = {
    ...data.value,
    size: 100,
    current: 1,
    ...val
  }
  init()
}
// 添加
function handleAdd() {
  add.value.drawer = true
}
// 编辑
function handleEdit(val) {
  requests.getMoreDetail({ project_id: val.id }).then(res => {
    let arr = []
    res.data.more_option_poll_viewpoint_list.forEach(v => {
      arr.push({
        id: v.id,
        point_name: v.point_name
      })
    })
    editData.value = {
      id: res.data.id,
      project_name: res.data.project_name,
      end_time: handleTime(res.data.end_time),
      least: res.data.least,
      atmost: res.data.atmost,
      more_option_poll_viewpoint_dtos: arr
    }
    add.value.isEdit = true
    add.value.drawer = true
  })
}
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
// 删除
function handleDel(row) {
  ElMessageBox.confirm('是否删除该组件？', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  })
    .then(() => {
      requests
        .delMore({
          project_id: row.id
        })
        .then(res => {
          init()
          ElMessage({
            type: 'success',
            message: '删除成功！'
          })
        })
        .catch(error => {
          loading.value = false
          console.log(error)
        })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 查看详情
function handleDetail(val) {
  datail.value.drawer = true
  datail.value.init(val.id)
}
// 返回
function back() {
  router.push('/operationManagement/interact')
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
</script>
<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: 100%;
  padding: 0;
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.back {
  width: 150px;
  display: flex;
  margin-bottom: 20px;
  cursor: pointer;
  align-items: center;

  img {
    width: 14px;
    height: 15px;
    margin-right: 3px;
  }

  font-size: 14px;
  font-weight: 400;
  color: #0b82fd;
  line-height: 13px;
}
</style>
