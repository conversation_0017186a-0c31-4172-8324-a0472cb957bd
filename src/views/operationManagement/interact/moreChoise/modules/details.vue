<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    title="详情"
    :size="800"
    close-on-click-moda="false"
    @close="close"
    @open="open"
    @closed="closed"
  >
    <div class="label"><span></span> 组件详情</div>
    <div class="line"><span class="title">组件ID：</span>{{ detail.id }}</div>
    <div class="line"><span class="title">组件标题：</span>{{ detail.project_name }}</div>
    <div class="line"><span class="title">关联稿件：</span>{{ detail.article_title }}</div>
    <div class="line">
      <span class="title">投票规则：</span>
      <span v-if="detail.least !== detail.atmost"
        >可选{{ detail.least }}到{{ detail.atmost }}项</span
      >
      <span v-else>可选{{ detail.least }}项</span>
    </div>
    <el-divider />
    <div class="label"><span></span> 数据统计</div>
    <div class="line"><span class="title">用户参与数：</span> {{ detail.option_total_count }}</div>
    <div class="line">
      <span class="title">投票行为分析</span>
    </div>
    <el-table :data="detail.more_option_poll_viewpoint_list" style="width: 100%">
      <el-table-column prop="point_name" label="选项名称" />
      <el-table-column prop="voters" label="投票数" width="180" />
      <el-table-column prop="proportion" label="投票占比" width="180" />
    </el-table>
    <div class="tip">注：手机端展示数据为四舍五入后的整数</div>
  </el-drawer>
</template>
<script setup name="add">
import { ElMessage } from 'element-plus'
import { requests } from '@/api/business/interact'
import { ref, onMounted } from 'vue'

const drawer = ref(false)
const detail = ref({
  id: '',
  project_name: '',
  article_title: '',
  least: '',
  atmost: '',
  option_total_count: '',
  more_option_poll_viewpoint_list: []
})
function init(val) {
  requests.getMoreDetail({ project_id: val }).then(res => {
    detail.value = res.data
  })
}
defineExpose({
  drawer,
  init
})
</script>
<style lang="scss" scoped>
.label {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  line-height: 14px;
  font-weight: 600;

  span {
    width: 4px;
    height: 14px;
    background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
    border-radius: 3px;
    margin-right: 6px;
  }

  .must {
    color: var(--el-color-danger);
    margin-left: 4px;
  }
}

.line {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  line-height: 14px;
  margin-bottom: 15px;

  .title {
    color: #333;
    font-weight: 600;
  }
}

.tip {
  font-weight: 400;
  font-size: 12px;
  color: #ff0000;
  line-height: 12px;
  margin-top: 6px;
}
</style>
