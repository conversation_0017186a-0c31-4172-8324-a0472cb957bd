<template>
  <AbsoluteContainer>
    <page-main>
      <div class="main">
        <div class="left">
          <div v-if="status.showPoint" :class="act === 1 ? 'card act' : 'card'" @click="changeCard(1)">
            <img src="@/assets/images/interact/pk.png" alt="" />
            <div class="text">
              <div class="icon"></div>
              总使用次数：
              <span>{{ useState[0].project_used_count }} 次</span>
              <div class="icon" style="margin-left: 36px"></div>
              总参与人数：
              <span>{{ useState[0].participant }}人次</span>
            </div>
          </div>
          <div v-if="status.showMore" :class="act === 2 ? 'card act' : 'card'" @click="changeCard(2)">
            <img src="@/assets/images/interact/more.png" alt="" />
            <div class="text">
              <div class="icon"></div>
              总使用次数：
              <span>{{ useState[1].project_used_count }} 次</span>
              <div class="icon" style="margin-left: 36px"></div>
              总参与人数：
              <span>{{ useState[1].participant }}人次</span>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="right">
          <Point v-if="act === 1" />
          <More v-if="act === 2" />
        </div>
      </div>
    </page-main>
  </AbsoluteContainer>
</template>
<script setup name="interact">
import Point from './pointPk/index.vue'
import More from './moreChoise/index.vue'
const store = useStore()
import { onMounted, ref } from 'vue'
import { requests } from '@/api/business/interact'
const route = useRoute(),
  router = useRouter()
const status = ref({
  showMore: true,
  showPoint: true
})

const act = ref(1)
const useState = ref([
  {
    project_used_count: 0,
    participant: 0
  },
  {
    project_used_count: 0,
    participant: 0
  }
])
onMounted(() => {
  requests.projectList({}).then(res => {
    useState.value = res.data.list
  })
  let permissions = store.getters['menu/permissions']
  if (permissions.filter(v => v === 'dual_option_poll_project:query').length) {
    status.value.showPoint = true
  }else{
    status.value.showPoint = false
  }
  if (permissions.filter(v => v === 'more_option_poll_project:list').length) {
    status.value.showMore = true
  }else{
    status.value.showMore = false
  }
  if(!status.value.showPoint){
    act.value = 2
  }
})
function changeCard(val) {
  act.value = val
  if (val === 1) {
  } else if (val === 2) {
  }
}
// 打开观点PK
function goPoint() {
  router.push('/interact/pointPk')
}
// 打开多选投票
function goMore() {
  router.push('/interact/moreChoise')
}
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  display: flex;
  .left {
    width: 360px;
    .card {
      width: 360px;
      height: 180px;
      margin-bottom: 20px;
      border-radius: 6px;
      cursor: pointer;
      border: 1px solid #dcdfe6;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
      .text {
        position: absolute;
        left: 20px;
        bottom: 10px;
        display: flex;
        font-size: 14px;
        color: #667682;
        align-items: center;
        .icon {
          width: 6px;
          height: 6px;
          background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
          margin-right: 5px;
        }
        span {
          color: #000;
        }
      }
    }
    .card.act {
      border: 2px solid #0b82fd;
    }
  }
  .line {
    width: 1px;
    height: 100%;
    background: #efefef;
    margin: 0 20px;
  }
  .right {
    width: calc(100% - 400px);
  }
}
</style>
