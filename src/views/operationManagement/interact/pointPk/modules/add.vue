<template>
  <div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      :title="isEdit ?'编辑观点PK': '创建观点PK'"
      :size="800"
      close-on-click-moda="false"
      @close="close"
      @open="open"
      @closed="closed"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <div class="label">
          <span></span> 标题编辑 <div class="must">*</div>
        </div>
        <el-form-item prop="project_name">
          <el-input
            v-model="ruleForm.project_name"
            :rows="5"
            maxlength="100"
            type="textarea"
            show-word-limit
            placeholder="请输入标题"
          />
        </el-form-item>
        <div class="label">
          <span></span> 选项编辑
        </div>
        <div style="padding-left: 10px">
          <div class="label">
            选项一
          </div>
          <el-form-item
            label="观点名称"
            prop="option1_text"
          >
            <el-input
              v-model="ruleForm.option1_text"
              maxlength="20"
              show-word-limit
              placeholder="请输入观点"
            />
          </el-form-item>
          <el-form-item
            label="背景颜色"
            prop="option1_color"
          >
            <el-color-picker
              v-model="ruleForm.option1_color"
              size="large"
            />
          </el-form-item>
          <div class="label">
            选项二
          </div>
          <el-form-item
            label="观点名称"
            prop="option2_text"
          >
            <el-input
              v-model="ruleForm.option2_text"
              maxlength="20"
              show-word-limit
              placeholder="请输入观点"
            />
          </el-form-item>
          <el-form-item
            label="背景颜色"
            prop="option2_color"
          >
            <el-color-picker
              v-model="ruleForm.option2_color"
              size="large"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button
            type="primary"
            @click="sureClick(ruleFormRef)"
          >
            保存并提交
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { ElMessage } from 'element-plus'
import { requests } from '@/api/business/interact'
import { ref, onMounted } from 'vue'
const emit = defineEmits(['init'])
const ruleFormRef = ref(null)
const ruleForm = ref({
  component_type: 'dual_options_poll',
  project_name: '',
  option1_text: '',
  option1_color: '#FF4967',
  option2_text: '',
  option2_color: '#51ABFD'
})
// 验证信息
const rules = ref({
  project_name: [{
    required: true,
    message: '请输入标题',
    trigger: 'blur'
  }],
  option1_text: [{
    required: true,
    message: '请输入观点名称',
    trigger: 'blur'
  }],
  option1_color: [{
    required: true,
    message: '请选择选项一颜色',
    trigger: 'change'
  }],
  option2_text: [{
    required: true,
    message: '请输入观点名称',
    trigger: 'blur'
  }],
  option2_color: [{
    required: true,
    message: '请选择选项二颜色',
    trigger: 'change'
  }]
})
// 传入的数据
const props = defineProps({
  data: Object
})
const drawer = ref(false)
const isEdit = ref(false)
// 确定
const sureClick = formEl => {
  // 表单验证
  if (!formEl) return
  formEl.validate((valid, fields) => {
    if (valid) {
      let fun = isEdit.value ? 'editPoint' : 'creatPoint'
      requests[fun](ruleForm.value).then(res => {
        ruleForm.value = {
          component_type: 'dual_options_poll',
          project_name: '',
          option1_text: '',
          option1_color: '#FF4967',
          option2_text: '',
          option2_color: '#51ABFD'
        }
        isEdit.value = false
        drawer.value = false
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        emit('init')
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 打开弹窗时
const open = () => {
  //   点了编辑
  if (isEdit.value) {
    ruleForm.value = {
      ...ruleForm.value,
      ...props.data
    }

  }
}
// 取消弹窗时
const close = formEl => {
  ruleForm.value = {
    component_type: 'dual_options_poll',
    project_name: '',
    option1_text: '',
    option1_color: '#FF4967',
    option2_text: '',
    option2_color: '#51ABFD'
  }
  if (!formEl) return
  formEl.resetFields()
  drawer.value = false
  isEdit.value = false
}
// 弹窗动画结束时
const closed = () => {
  isEdit.value = false
}
defineExpose({
  drawer,
  isEdit
})

</script>
<style lang="scss" scoped>
.label {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 14px;
  span {
    width: 4px;
    height: 14px;
    background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
    border-radius: 3px;
    margin-right: 6px;
  }
  .must{
    color: var(--el-color-danger);
    margin-left: 4px
  }
}
</style>
