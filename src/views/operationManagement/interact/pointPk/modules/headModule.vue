<template>
  <div class="stateBox">
    <div :class="searchData.used === 0 ? 'child act' : 'child'" @click="changeUse(0)">待使用</div>
    <div :class="searchData.used === 1 ? 'child act' : 'child'" @click="changeUse(1)">已使用</div>
  </div>
  <div class="head-box">
    <div class="left">
      <el-button
        v-auth="'dual_option_poll_project:create'"
        type="primary"
        color="#0B82FD"
        style="width: 76px"
        @click="emit('add')"
      >
        创建
      </el-button>

      <el-date-picker
        v-model="time"
        style="width: 220px"
        class="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
      >
      </el-date-picker>
    </div>
    <div class="right">
      <el-select
        v-model="searchData.comment_search_type"
        placeholder="请选择"
        style="margin-right: 5px; width: 110px"
        @change="selectChage"
      >
        <el-option v-for="item of searchType" :key="item.key" :label="item.label" :value="item.key">
        </el-option>
      </el-select>
      <el-input
        v-model="searchData.searchword"
        placeholder="请输入内容"
        class="input-with-select"
        style="width: 200px"
        clearable
      >
      </el-input>
      <!-- 稿件标题搜索的实时下拉 -->

      <el-button
        color="#0B82FD"
        type="primary"
        :icon="Search"
        style="margin-left: 5px"
        @click="handSearch"
      >
        搜索
      </el-button>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose } from 'vue'
// 组件接收事件
const emit = defineEmits(['search', 'add'])

// 时间选择器
const time = ref(null)
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
// 搜索类型
const searchType = ref([
  {
    key: 'article_title',
    label: '稿件标题'
  },
  {
    key: 'project_name',
    label: '组件标题'
  }
])
const searchType2 = ref([
  {
    key: '0',
    label: '未使用'
  },
  {
    key: '1',
    label: '已使用'
  }
])
function selectChage() {
  searchData.value.searchword = ''
}
const loading = ref(false)
// 搜索参数
const searchData = ref({
  used: 0,
  comment_search_type: 'project_name',
  searchword: null // 关键词
})

function changeUse(val) {
  searchData.value.used = val
  handSearch()
}

const handSearch = () => {
  let data = {}

  if (time.value && time.value.length) {
    ;(data.start_time = time.value[0]), (data.end_time = time.value[1])
  } else {
    ;(data.start_time = ''), (data.end_time = '')
  }
  searchData.value.comment_search_type === 'article_title'
    ? (data.project_name = '')
    : (data.article_title = '')
  data.used = searchData.value.used
  data[searchData.value.comment_search_type] = searchData.value.searchword
  emit('search', data)
}

defineExpose({})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}
::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}
::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
  background-color: #ebebeb;
}
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  .left {
    .el-button {
      float: left;
      margin-right: 10px;
    }
    .el-select {
      width: 110px;
      float: left;
      margin-right: 10px;
    }
  }
  .right {
    & > .el-select {
      width: 110px;
    }
  }
}
.stateBox {
  width: 132px;
  height: 34px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  display: flex;
  padding: 3px;
  justify-content: space-between;
  margin-bottom: 15px;
  .child {
    width: 60px;
    height: 26px;
    background: #ffffff;
    font-size: 16px;
    color: #333333;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
  }
  .child.act {
    background: #eef5fe;
    color: #0b82fd;
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
