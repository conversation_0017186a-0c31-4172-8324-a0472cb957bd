<template>
  <headModel
    ref="search"
    @back="back"
    @search="handleSearch"
  />
  <div style="font-size: 14px;">
    <p style="margin: 5px 0">稿件ID：{{ Info.id }}</p>
    <p style="margin: 5px 0">稿件标题：{{ Info.list_title || Info.title }}</p>
  </div>
  <div class="tab">
    <div
      :class="activeName == 1 ? 'child act':'child'"
      @click="handleClickTab(1)"
    >
      普通评论
    </div>
    <div
      v-auth="'comment:search_article'"
      :class="activeName == 2 ? 'child act':'child'"
      @click="handleClickTab(2)"
    >
      热门评论
    </div>
    <div style="width: 100%;">
      <div
        class="line"
        :style="'transform: translateX(' + (56 * (activeName - 1) + (activeName - 1) * 40) + 'px)'"
      ></div>
    </div>
  </div>
  <el-divider />
  <el-table
    v-loading="loading"
    :data="data.dataList"
  >
<template v-if="data.enableScoreSort == 0">
<el-table-column
      v-if="activeName === 1"
      v-slot="scope"
      label="排序"
      width="120"
    >
      <el-button
        v-auth="'comment_sort:up_down_sort'"
        type="primary"
        :disabled="(scope.$index === 0 && data.current === 1) || scope.row.fixed_number !== 0"
        size="small"
        :icon="ArrowUpBold"
        circle
        color="#0C82E5"
        style="margin-right: 10px;"
        @click="sortUp(scope)"
      />
      <el-button
        v-auth="'comment_sort:up_down_sort'"
        type="success"
        size="small"
        :disabled="((scope.$index + 1) === data.dataList.length && data.current === data.pages) || scope.row.fixed_number !== 0"
        color="#31D9CC"
        circle
        @click="sortDown(scope)"
      >
        <el-icon color="#fff">
          <ArrowDownBold />
        </el-icon>
      </el-button>
    </el-table-column>
    <el-table-column
      v-else
      v-slot="scope"
      label="排序"
      width="120"
    >
      <el-button
        v-auth="'comment_top:update_sort'"
        type="primary"
        :disabled="(scope.$index === 0 && data.current === 1) || scope.row.fixed_number !== 0"
        size="small"
        :icon="ArrowUpBold"
        circle
        color="#0C82E5"
        style="margin-right: 10px;"
        @click="sortUp(scope)"
      />
      <el-button
        v-auth="'comment_top:update_sort'"
        type="success"
        size="small"
        :disabled="((scope.$index + 1) === data.dataList.length && data.current === data.pages) || scope.row.fixed_number !== 0"
        color="#31D9CC"
        circle
        @click="sortDown(scope)"
      >
        <el-icon color="#fff">
          <ArrowDownBold />
        </el-icon>
      </el-button>
    </el-table-column>
  </template>
    <el-table-column
      v-if="activeName === 1"
      v-slot="scope"
      width="100"
      label="序号"
    >
      {{ scope.$index + 1 }}
      <span
        v-if="scope.$index < 10 && data.current === 1 "
        v-auth="'comment_sort:fixed'"
      >
        <svg-icon
          v-if="scope.row.fixed_number === 0"
          name="regular"
          @click="handleRegular(scope.$index + 1, scope.row.id )"
        />
        <svg-icon
          v-else
          name="regular-act"
          @click="cancelRegular(scope.$index + 1, scope.row.id )"
        />
      </span>
    </el-table-column>
    <el-table-column
      v-else
      v-slot="scope"
      width="100"
      label="序号"
    >
      {{ scope.$index + 1 }}
      <span
        v-if="scope.$index < 10 && data.current === 1 "
        v-auth="'comment_sort:fixed_hot'"
      >
        <svg-icon
          v-if="scope.row.fixed_number === 0"
          name="regular"
          @click="handleRegular(scope.$index + 1, scope.row.id )"
        />
        <svg-icon
          v-else
          name="regular-act"
          @click="cancelRegular(scope.$index + 1, scope.row.id )"
        />
      </span>
    </el-table-column>
    <el-table-column
      prop="id"
      label="评论ID"
      width="100"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      v-slot="scope"
      prop="content"
      label="评论内容"
    >
      <span v-if="scope.row.fixed_number === 0" v-html="scope.row.content"></span>
      <span
        v-else
        style="color: #F56C6C;"
      ><span v-html="scope.row.content"></span>（固定）</span>
    </el-table-column>

    <el-table-column
      prop="comment_user_name"
      label="评论人"
      width="120"
    />

    <!-- <el-table-column prop="channel_name" label="回复评论数" width="100"  /> -->
    <el-table-column
      v-if="activeName === 1"
      v-slot="scope"
      prop="audit_time"
      label="评论时间"
      width="110"
    >
      {{ handleTime(scope.row.created_at) }}
    </el-table-column>
    <el-table-column
      v-else
      prop="created_at"
      label="评论时间"
      width="200"
    ></el-table-column>

    <el-table-column
      v-slot="scope"
      label="操作"
      width="180"
      fixed="right"
    >
      <el-button
        v-if="activeName === 1"
        v-auth="'comment_top:set_top'"
        text
        size="small"
        style="margin-left: 0; padding: 5px 5px;"
        @click="setHot(scope.row, 1)"
      >
        <span style="color:#0B82FD;">设为热评</span>
      </el-button>
      <el-button
        v-else
        v-auth="'comment_top:set_top'"
        text
        size="small"
        style="margin-left: 0; padding: 5px 5px;"
        @click="setHot(scope.row, 2)"
      >
        <span style="color:#0B82FD;">取消热评</span>
      </el-button>
      <el-button
        v-if="scope.row.fixed_number === 0"
        v-auth="'comment_sort:change'"
        text
        size="small"
        style="margin-left: 0; padding: 5px 5px;"
        @click="setPosition(scope.row)"
      >
        <span v-if="data.enableScoreSort == 0" style="color:#0B82FD;">插入位置</span>
      </el-button>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <div
    v-if="activeName === 1"
    class="page"
  >
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      small
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
  <History ref="history" />
  <el-dialog
    v-model="dialogVisible"
    title="插入位置"
    width="300px"
  >
    <el-input-number
      v-model="positionNum"
      :min="1"
      :max="data.total"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click=" positionCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="loading2"
          @click="positionConfirm"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="sort">
import headModel from './modules/sortHead.vue'
import { computed, nextTick, ref, defineEmits } from 'vue'
import History from './modules/history.vue'
import { requests } from '@/api/business/sort'
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['search', 'back'])
const data = ref({
  dataList: [],
  total: 0,
  size: 100,
  current: 1,
  comment_content: '',
  pages: '',
  enableScoreSort: ''
})
const Info = ref({

})
// 日志
const history = ref(null)
// 搜索
const search = ref(null)
const loading = ref(false)
const activeName = ref(1)
const loading2 = ref(false)
// 插入位置
const dialogVisible = ref(false)
const positionNum = ref(0)
const positionData = ref({})
onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementsByClassName(
      'el-table__header-wrapper'
    )[0].offsetHeight
    document.getElementsByClassName(
      'el-table__body-wrapper'
    )[0].style.height = `calc(100% - ${height}px)`
  })
})
// 插入位置
function setPosition (val) {
  positionData.value = val
  dialogVisible.value = true
}
// 插入位置确认
function positionConfirm () {
  loading2.value = true
  requests.position({
    comment_id: positionData.value.id,
    position: positionNum.value,
    type: activeName.value
  }).then(res => {
    loading2.value = false
    dialogVisible.value = false
    positionNum.value = 1
    ElMessage({
      type: 'success',
      message: '操作成功'
    })
    if (activeName.value == 1) {
      init()
    } else {
      init2()
    }
  }).catch(() => {
    loading2.value = false
  })
}
// 插入位置取消
function positionCancel () {
  positionNum.value = 1
  dialogVisible.value = false
}
// 设为热评/取消热评
function setHot (val, type) {
  let id = Info.value.original_id ? Info.value.original_id : Info.value.id
  if (Info.value.uuid || Info.value.ugc_uuid) {
    id = Info.value.uuid || Info.value.ugc_uuid
  }
  let Data = {
    action: type == 1 ? 'ON' : 'OFF',
    comment_id: val.id,
    article_id: id
  }
  loading.value = true
  requests.setHotComment(Data).then(res => {
    ElMessage({
      type: 'success',
      message: '设置成功'
    })
    if (type == 1) {
      init()
    } else {
      init2()
    }
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}
// 切换标签
function handleClickTab (val) {
  activeName.value = val
  data.value.comment_content = ''
  data.value.current = 1
  data.value.dataList = []
  if (activeName.value == 1) {
    init()
  } else {
    init2()
  }
}
// 排序向上
function sortUp (val) {
  let id = Info.value.original_id ? Info.value.original_id : Info.value.id
  if (Info.value.uuid || Info.value.ugc_uuid) {
    id = Info.value.uuid || Info.value.ugc_uuid
  }
  if (activeName.value == 1) {
    let Data = {
      from_comment_id: val.row.id,
      up_or_down: 0
    }
    loading.value = true
    requests.sort(Data).then(res => {
      ElMessage({
        type: 'success',
        message: '排序成功'
      })
      init()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  } else {
    let Data = {
      sort: 'UP',
      comment_id: val.row.id,
      article_id: id
    }
    loading.value = true
    requests.hotSort(Data).then(res => {
      ElMessage({
        type: 'success',
        message: '排序成功'
      })
      init2()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }

}
// 排序向下
function sortDown (val) {
  if (activeName.value == 1) {
    let Data = {
      from_comment_id: val.row.id,
      up_or_down: 1
    }
    loading.value = true
    requests.sort(Data).then(res => {
      ElMessage({
        type: 'success',
        message: '排序成功'
      })
      init()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  } else {
    let id = Info.value.original_id ? Info.value.original_id : Info.value.id
    if (Info.value.uuid || Info.value.ugc_uuid) {
      id = Info.value.uuid || Info.value.ugc_uuid
    }
    let Data = {
      sort: 'DOWN',
      comment_id: val.row.id,
      article_id: id
    }
    loading.value = true
    requests.hotSort(Data).then(res => {
      ElMessage({
        type: 'success',
        message: '排序成功'
      })
      init2()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }

}
// 固定
function handleRegular (num, id) {
  loading.value = true
  if (activeName.value == 1) {
    requests.regular({ comment_id: id, position: num, fixed: true }).then(res => {
      loading.value = false
      if (res.code === 0) {
        data.value.dataList[num - 1].fixed_number = 1
        ElMessage({
          type: 'success',
          message: '固定成功'
        })
        if (data.value.enableScoreSort != 0) {
          init()
        }
      }
    }).catch(() => {
      loading.value = false
    })
  } else {
    let id2 = Info.value.original_id ? Info.value.original_id : Info.value.id
    if (Info.value.uuid || Info.value.ugc_uuid) {
      id2 = Info.value.uuid || Info.value.ugc_uuid
    }
    requests.regular2({ comment_id: id, position: num, article_id: id2 }).then(res => {
      loading.value = false
      if (res.code === 0) {
        data.value.dataList[num - 1].fixed_number = 1
        ElMessage({
          type: 'success',
          message: '固定成功'
        })
        init2()
      }
    }).catch(() => {
      loading.value = false
    })
  }

}
// 取消固定
function cancelRegular (num, id) {
  loading.value = true

  if (activeName.value == 1) {
    requests.cancelRegular({ comment_id: id }).then(res => {
      loading.value = false
      if (res.code === 0) {
        data.value.dataList[num - 1].fixed_number = 0
        ElMessage({
          type: 'success',
          message: '取消固定成功'
        })
        if (data.value.enableScoreSort != 0) {
          init()
        }
      }
    }).catch(() => {
      loading.value = false
    })
  } else {
    let id2 = Info.value.original_id ? Info.value.original_id : Info.value.id
    if (Info.value.uuid || Info.value.ugc_uuid) {
      id2 = Info.value.uuid || Info.value.ugc_uuid
    }
    requests.cancelRegular2({ comment_id: id, article_id: id2 }).then(res => {
      loading.value = false
      if (res.code === 0) {
        data.value.dataList[num - 1].fixed_number = 0
        ElMessage({
          type: 'success',
          message: '取消固定成功'
        })
        init2()
      }
    }).catch(() => {
      loading.value = false
    })
  }
}
// 时间转换
function handleTime (time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s

}
// 初始化
function init () {
  let id = Info.value.original_id ? Info.value.original_id : Info.value.id
  if (Info.value.uuid || Info.value.ugc_uuid) {
    id = Info.value.uuid || Info.value.ugc_uuid
  }
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    article_id: id,
    comment_content: data.value.comment_content,
    channel_id: Info.value.channel_id,
    article_created_at: Info.value.published_at
  }
  loading.value = true
  requests.sortList(searchData).then(res => {
    data.value.total = res.data.pg.total
    data.value.enableScoreSort = res.data.enableScoreSort
    data.value.dataList = res.data.pg.records
    data.value.pages = Math.ceil(res.data.pg.total / data.value.size)
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}
// 热评初始化
function init2 () {
  let id = Info.value.original_id ? Info.value.original_id : Info.value.id
  if (Info.value.uuid || Info.value.ugc_uuid) {
    id = Info.value.uuid || Info.value.ugc_uuid
  }
  let searchData = {
    article_id: id,
    comment_content: data.value.comment_content
  }
  loading.value = true
  requests.hotList(searchData).then(res => {
    data.value.total = res.data.commentTops.length
    data.value.dataList = res.data.commentTops
    data.value.pages = 1
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}
// 搜索
function handleSearch (val) {
  data.value.comment_content = val.comment_content
  if (activeName.value == 1) {
    init()
  } else {
    init2()
  }
}
// 返回
function back () {
  activeName.value = 1
  data.value.comment_content = ''
  emit('back')
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  if (activeName.value == 1) {
    init()
  } else {
    init2()
  }
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  if (activeName.value == 1) {
    init()
  } else {
    init2()
  }
}
defineExpose({
  Info,
  init
})
</script>

<style lang="scss" scoped>
.svg-icon {
  cursor: pointer;
  width: 24px;
  height: 24px;
  margin-left: 10px;
  color: #0b82fd;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.page-main {
  display: flex;
  flex-direction: column;

  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  :deep(.el-table) {
    height: 100%;
    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}
.tab {
  height: 40px;
  display: flex;
  font-size: 14px;
  line-height: 40px;
  flex-wrap: wrap;
  .child {
    cursor: pointer;
    margin-right: 40px;
  }
  .child.act {
    color: rgb(64, 158, 255);
  }
  .line {
    flex-wrap: wrap;
    height: 2px;
    background: rgb(64, 158, 255);
    width: 54px;
    transition: transform 0.2s linear;
    border-radius: 1px;
  }
}
.el-divider--horizontal {
  margin-top: -1px;
}
</style>
