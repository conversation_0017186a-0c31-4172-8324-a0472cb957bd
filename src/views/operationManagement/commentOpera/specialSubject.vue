<template>
  <page-main v-show="!showTask && !showSort && !showSubject">
    <el-button
      plain
      type="primary"
      style="width: 80px; margin-bottom: 10px"
      @click="onBack"
    >
      <el-icon><Back /></el-icon>
      <span style="margin-left: 5px">返回</span>
    </el-button>
    <div style="font-size: 14px">
      <p style="margin: 5px 0">稿件ID：{{ props.subject.id }}</p>
      <p style="margin: 5px 0">
        专题标题：{{ props.subject.list_title || props.subject.title }}
      </p>
    </div>
    <el-table
      v-loading="loading"
      :data="data.dataList"
      size="default"
      :height="data.height"
    >
      <el-table-column v-slot="scope" label="稿件ID" width="100">
        {{ scope.row.uuid ? scope.row.uuid : scope.row.id }}
      </el-table-column>
      <el-table-column
        v-slot="scope"
        label="稿件标题"
        :show-overflow-tooltip="true"
      >
        <a class="elemLink" target="_blank" :href="scope.row.url">{{
          scope.row.title
        }}</a>
      </el-table-column>
      <!-- <el-table-column prop="doc_type_name" label="稿件类型" width="100"  />
      <el-table-column prop="channel_name" label="来源频道" width="120"  /> -->
      <el-table-column
        prop="published_at_str"
        label="发布时间"
        width="180"
/>
      <!-- <el-table-column prop="activityCommentCount" label="组件数" width="100"  /> -->
      <el-table-column
        prop="read_count"
        label="稿件阅读数"
        width="120"
/>
      <el-table-column
        prop="passed_comment_count"
        label="已通过评论数"
        width="120"
/>
      <el-table-column v-slot="scope" label="操作" width="330" fixed="right">
        <el-button
          v-if="scope.row.doc_type_name !== '专题'"
          v-auth="'comment_operation_task:save'"
          style="margin-left: 0; padding: 5px 5px"
          text
          size="small"
          @click="handleAdd(scope.row)"
        >
          <span style="color: #0b82fd">创建任务</span>
        </el-button>
        <el-button
          v-if="scope.row.doc_type_name !== '专题'"
          v-auth="'comment:batch_list'"
          text
          size="small"
          style="margin-left: 0; padding: 5px 5px"
          @click="handleAll(scope.row)"
        >
          <span style="color: #0b82fd">赞/评/删</span>
        </el-button>
        <el-button
          v-if="scope.row.doc_type_name !== '专题'"
          v-auth="'comment_sort:list'"
          text
          size="small"
          style="margin-left: 0; padding: 5px 5px"
          @click="toSort(scope.row)"
        >
          <span style="color: #0b82fd">评论排序</span>
        </el-button>
        <el-button
          v-if="scope.row.doc_type_name === '专题'"
          text
          size="small"
          style="margin-left: 0; padding: 5px 5px"
          @click="handleShowSubject(scope.row)"
        >
          <span style="color: #0b82fd">查看稿件</span>
        </el-button>
        <el-button
          v-auth="'comment_operation_task:list'"
          text
          size="small"
          style="margin-left: 0; padding: 5px 5px"
          @click="openTask(scope.row)"
        >
          <span style="color: #0b82fd">查看任务</span>
        </el-button>
        <el-button
          v-auth="'article_operator_log:list'"
          text
          size="small"
          style="margin-left: 0; padding: 5px 5px"
          @click="showHistory(scope.row)"
        >
          <span style="color: #0b82fd">操作日志</span>
        </el-button>
        <!-- <el-popover
          placement="bottom"
          :width="85"
          trigger="hover"
        >
          <template #reference>
            <el-button v-if="scope.row.doc_type_name !== '专题'" text size="small" style="margin-left: 0; padding: 5px 5px;">
              <span style="color:#0B82FD;">更多</span>
            </el-button>
          </template>
          <template #default>
            <el-button v-auth="'comment_operation_task:list'" text size="small" style="margin-left: 0; padding: 5px 5px;" @click="openTask(scope.row)">
              <span style="color:#0B82FD;">查看任务</span>
            </el-button>
            <el-button v-auth="'article_operator_log:list'" text size="small" style="margin-left: 0; padding: 5px 5px;" @click="showHistory(scope.row)">
              <span style="color:#0B82FD;">操作日志</span>
            </el-button>
          </template>
        </el-popover> -->
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        background
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :page-size="data.size"
        :current-page="data.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </page-main>
  <page-main v-show="showTask && !showSort">
    <CommentTask ref="commentTask" @back="back" />
  </page-main>
  <page-main v-show="showSort && !showTask">
    <Sort ref="sort" @back="back" />
  </page-main>
  <Add ref="add" />
  <AllComment ref="allComment" />
  <History ref="history" />
</template>

<script setup name="cardList">
import { computed, nextTick, ref } from 'vue'
import { requests } from '@/api/business/commentOpera'
import Add from './modules/add.vue'
import AllComment from './modules/allComment.vue'
import CommentTask from './commentTask.vue'
import History from './modules/operateHistory.vue'

const props = defineProps({
  subject: {
    // 图片路径，支持url 地址, base64, blob
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['back'])

const allComment = ref(null)
import Sort from './sort.vue'
const route = useRoute()
const add = ref(null)
const showTask = ref(false)
const showSort = ref(false)
const data = ref({
  height: null,
  dataList: [],
  total: 0,
  size: 100,
  current: 1,
  channel_id: '',
  article_title: '',
  article_id: ''
})
const commentTask = ref(null)
const sort = ref(null)
const loading = ref(false)
const history = ref(null)
const subjectId = ref()
onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementsByClassName('el-table__header-wrapper')[0]
      .offsetHeight
    document.getElementsByClassName(
      'el-table__body-wrapper'
    )[0].style.height = `calc(100% - ${height}px)`
  })
  if (route.query.id) {
    data.value.article_id = route.query.id
  }
  init()
})
// 返回
function onBack () {
  emit('back')
}
// 初始化
function init () {
  let id
  if(subjectId.value){
    id = subjectId.value
  }else{
    if(props.subject.original_id && props.subject.original_id !== 0){
      id = props.subject.original_id
    }else{
      id = props.subject.id
    }
  }
  let searchData = {
    article_id: id,
    size: data.value.size,
    current: data.value.current
  }
  loading.value = true
  requests
    .subjectList(searchData)
    .then(res => {
      loading.value = false
      data.value.total =
        res.data.subject_list.total > 10000
          ? 10000
          : res.data.subject_list.total
      data.value.dataList = res.data.subject_list.records
    })
    .catch(() => {
      loading.value = false
    })
}
// 展示专题稿件内容
function handleShowSubject (val) {
  props.subject = val
  if(val.original_id && val.original_id !== 0){
    subjectId.value = val.original_id
  }else{
    subjectId.value = val.id
  }
  subjectId.value = val.id
  data.value.current = 1
  init()
}
// 搜索
function handleSearch (val) {
  data.value.channel_id = val.channel_id
  data.value.article_title = val.article_title
  data.value.article_id = val.article_id
  init()
}
// 操作日志
function showHistory (val) {
  console.log(val)
  history.value.drawer = true
  history.value.info = val
  history.value.init()
}
// 查看所有评论
function handleAll (row) {
  // 马甲号列表
  requests.vest_list({ size: 10 }).then(res => {
    if (res.code == 0) {
      allComment.value.vestList = res.data.vest_list
      if (allComment.value.vestList.length > 0) {
        for (let i = 0; i < allComment.value.vestList.length; i++) {
          allComment.value.vestList[i].isClick = false
        }
        // 默认第一个马甲
        const vestOne = allComment.value.vestList[0]
        vestOne.isClick = true
        allComment.value.dataVal.comment_user_id = vestOne.id
        allComment.value.dataVal.comment_user_name = vestOne.nick_name
        allComment.value.dataVal.comment_user_portrait = vestOne.image_url
        allComment.value.dataVal.comment_user_location = vestOne.location
      }
    }
  })
  let newData = Object.assign({}, row)
  let article_id = ''
  if (newData.uuid) {
    article_id = newData.uuid
  } else if (newData.ugc_uuid) {
    article_id = newData.ugc_uuid
  } else if (newData.original_id) {
    article_id = newData.original_id
  } else {
    article_id = newData.id
  }
  if (article_id) {
    // 总评论数
    requests.count({ article_id }).then(res => {
      if (res.code == 0) {
        allComment.value.dataVal.countNum = res.data.count
      }
    })
    requests
      .floor_list({ article_id, sort_number: 0, size: 10 })
      .then(res => {
        if (res.code == 0) {
          allComment.value.drawer = true
          allComment.value.moreNullData = false
          allComment.value.comment_level = res.data.dtos
          allComment.value.dataVal.article_id = article_id
          allComment.value.dataVal.list_pics = newData.list_pics
          allComment.value.dataVal.article_title = newData.title
          allComment.value.dataVal.channel_name = newData.channel_name
          allComment.value.dataVal.article_url = newData.url
          allComment.value.dataVal.article_published_at = handleTime(
            newData.published_at
          )
          newData.account_id
            ? (allComment.value.dataVal.article_user_id = newData.account_id)
            : (allComment.value.dataVal.article_user_id = '')
          allComment.value.dataVal.channel_id = newData.channel_id
        }
      })
      .then(v => {
        requests.hot_comments({ article_id }).then(res => {
          if (res.code == 0) {
            let hotData = res.data.dtos
            for (let i = 0; i < hotData.length; i++) {
              hotData[i].hotCommentsFlag = true
            }
            allComment.value.comment_level = [
              ...hotData,
              ...allComment.value.comment_level
            ]
          }
        })
      })
  }
}
// 创建评论任务
function handleAdd (row) {
  add.value.ruleForm.isEdit = false
  // 马甲号列表
  requests.vest_list({ size: 10 }).then(res => {
    if (res.code == 0) {
      add.value.drawer = true
      add.value.gridData = []
      // 马甲号列表处理
      add.value.vestList = res.data.vest_list
      if (add.value.vestList.length > 0) {
        for (let i = 0; i < add.value.vestList.length; i++) {
          add.value.vestList[i].isClick = false
        }
        // 默认第一个马甲
        const vestOne = add.value.vestList[0]
        vestOne.isClick = true
        add.value.ruleForm.comment_user_id = vestOne.id
        add.value.ruleForm.comment_user_name = vestOne.nick_name
        add.value.ruleForm.comment_user_portrait = vestOne.image_url
        add.value.ruleForm.comment_user_location = vestOne.location
      }

      let newData = Object.assign({}, row)
      add.value.ruleForm.list_pics = newData.list_pics
      add.value.ruleForm.article_title = newData.title
      add.value.ruleForm.channel_id = newData.channel_id
      add.value.ruleForm.channel_name = newData.channel_name
      add.value.ruleForm.article_url = newData.url
      add.value.ruleForm.article_published_at = handleTime(
        newData.published_at
      )
      newData.account_id
        ? (add.value.ruleForm.article_user_id = newData.account_id)
        : (add.value.ruleForm.article_user_id = '')

      if (newData.uuid) {
        add.value.ruleForm.article_id = newData.uuid
        console.log('uuid')
      } else if (newData.ugc_uuid) {
        add.value.ruleForm.article_id = newData.ugc_uuid
        console.log('ugc_uuid')
      } else if (newData.original_id) {
        add.value.ruleForm.article_id = newData.original_id
        console.log('original_id')
      } else {
        add.value.ruleForm.article_id = newData.id
        console.log('id')
      }
    }
  })
}
// 时间转换
function handleTime (time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M =
    (date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h =
    (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m =
    (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
    ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
// 打开全部任务
function openTask (value) {
  showTask.value = true
  if (value) {
    let id = value.original_id ? value.original_id : value.id
    if (value.uuid || value.ugc_uuid) {
      id = value.uuid || value.ugc_uuid
    }
    commentTask.value.searchTitle(id)
    commentTask.value.getStateNum(id)
  } else {
    commentTask.value.getStateNum()
    commentTask.value.init()
  }
}
// 评论排序
function toSort (val) {
  showSort.value = true
  sort.value.Info = val
  sort.value.init()
}
function back () {
  showTask.value = false
  showSort.value = false
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #333;
}
a:hover,
a:visited,
a:link,
a:active {
  color: #333;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);

  :deep(.el-table) {
    height: 100%;
    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}
</style>
