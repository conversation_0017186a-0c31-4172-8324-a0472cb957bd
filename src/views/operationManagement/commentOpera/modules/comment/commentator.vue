<template>
<div :class="isCircle ? 'commentStyle' : ''">
 <el-form-item>
            昵称：
            <el-input
              v-model="vestSearch.nick_name"
              style="width: 185px; margin-right: 25px"
              placeholder="请输入昵称"
              class="input-with-select"
              clearable
            >
            </el-input>
            归属地：
            <el-input
              v-model="addressVal"
              style="width: 200px;"
              placeholder="请输入归属地"
              class="input-with-select"
              @change="addressChange"
            >
            </el-input>
            <elui-china-area-dht
              v-model="dhtVal"
              isall
              :leave="2"
              class="dtlClass"
              @change="dhtChange"
            ></elui-china-area-dht>
            <el-button
              type="primary"
              :icon="Search"
              style="margin-left:-20px;"
              @click="handleSwitch"
            >
              搜索
            </el-button>
            <el-button
              type="primary"
              text="primary"
              style="background: rgba(11, 130, 253, 0.05);"
              :icon="Refresh"
              @click="handleSwitch"
            >
              换一批
            </el-button>
          </el-form-item>
          <el-form-item class="vestList">
            <!-- 置顶列表 -->
            <div
              v-for="item of vest_list_top"
              v-show="vest_list_top.length"
              :key="item.id"
              :class="item.isClick ? 'imgListClick': 'imgList'"
              @click="handleVestClick(item.image_url,item.nick_name,item.location,item.id)"
            >
              <img :src="item.image_url" />
              <div class="topingDef">置顶</div>
              <div class="toping" @click.stop="handleTopingDel(item.auto_pk)">取消置顶</div>
              <div class="sub-title">
                <span>{{ item.nick_name }}</span>
                <p>{{ item.location.split(',')[1] + item.location.split(',')[2] }}</p>
              </div>
            </div>
            <!-- 随机列表 -->
            <div
              v-for="item of vestList"
              v-show="vestList.length"
              :key="item.id"
              :class="item.isClick ? 'imgListClick': 'imgList'"
              @click="handleVestClick(item.image_url,item.nick_name,item.location,item.id)"
            >
              <img :src="item.image_url" />
              <div class="toping" @click.stop="handleToping(item.id)">置顶</div>
              <div class="sub-title">
                <span>{{ item.nick_name }}</span>
                <p>{{ item.location.split(',')[1] + item.location.split(',')[2] }}</p>
              </div>
            </div>
            <div v-show="!vestList.length && !vest_list_top.length">暂无更多评论人</div>
          </el-form-item>
</div>
</template>

<script setup name='commentator'>
import { EluiChinaAreaDht } from 'elui-china-area-dht'
import { ref, onMounted, defineEmits, defineExpose, defineProps } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { requests } from '@/api/business/commentOpera'
import { Search, Refresh } from '@element-plus/icons'
const chinaData = new EluiChinaAreaDht.ChinaArea().chinaAreaflat
const emit = defineEmits(['selectCommentator'])
const props = defineProps({
  isCircle: {
    type: Boolean
  }
})
const vestSearch = ref({
  nick_name: '',
  location: '',
  size: 10
})
const addressVal = ref()
const dhtVal = ref([])
const vestList = ref([])
const commentInfo = ref({
  comment_user_id: '',
  comment_user_name: '',
  comment_user_portrait: '',
  comment_user_location: ''
})

onMounted(() => {
  handleSwitch()
  vest_collect_list()
})
const addressChange = () => {
  vestSearch.value.location = addressVal.value
}
// 省市级联
const dhtChange = e => {
  const sheng = chinaData[e[0]]
  const shi = chinaData[e[1]]
  if (sheng == undefined && shi == undefined) {
    vestSearch.value.location = ''
    addressVal.value = ''
  } else if (shi == undefined || shi.label == '市辖区') {
    vestSearch.value.location = sheng.label
    addressVal.value = sheng.label
  } else {
    vestSearch.value.location = shi.label
    addressVal.value = shi.label
  }
}
// 换一批 搜索
const handleSwitch = () => {
  requests.vest_list(vestSearch.value).then(res => {
    if (res.code == 0) {
      vestList.value = res.data.vest_list
      if (vestList.value.length > 0) {
        for (let i = 0; i < vestList.value.length; i++) {
          vestList.value[i].isClick = false
        }
        // 默认第一个马甲
        const vestOne = vestList.value[0]
        vestOne.isClick = true
        commentInfo.value.comment_user_id = vestOne.id
        commentInfo.value.comment_user_name = vestOne.nick_name
        commentInfo.value.comment_user_portrait = vestOne.image_url
        commentInfo.value.comment_user_location = vestOne.location
        emit('selectCommentator')
      } else {
        commentInfo.value.comment_user_id = ''
        commentInfo.value.comment_user_name = ''
        commentInfo.value.comment_user_portrait = ''
        commentInfo.value.comment_user_location = ''
      }
    } else {
      ElMessage({
        type: 'error',
        message: '替换失败！'
      })
    }
  })
}
// 点击选中马甲号
const handleVestClick = (image_url, nick_name, location, id) => {
  emit('selectCommentator')
  commentInfo.value.comment_user_id = id
  commentInfo.value.comment_user_name = nick_name
  commentInfo.value.comment_user_portrait = image_url
  commentInfo.value.comment_user_location = location

  vestList.value.forEach((v, index) => {
    if (v.id == id) {
      vestList.value[index].isClick = true
    } else {
      vestList.value[index].isClick = false
    }
  })
  vest_list_top.value.forEach((v, index) => {
    if (v.id == id) {
      vest_list_top.value[index].isClick = true
    } else {
      vest_list_top.value[index].isClick = false
    }
  })
}
// 获取置顶列表
const vest_list_top = ref([])
const vest_collect_list = () => {
  const data = {
    nick_name: '',
    location: '',
    size: '20'
  }
  requests.vest_collect_list(data).then(res => {
    if (res.code == 0) {
      vest_list_top.value = res.data.vest_list_top
    }
  })
}

// 置顶
const handleToping = id => {
  requests.vest_collect_top({ id }).then(res => {
    if (res.code == 0) {
      ElMessage({
        message: '置顶成功',
        type: 'success'
      })
      vest_collect_list()
      handleSwitch()
    } else {
      ElMessage({
        message: '置顶失败',
        type: 'error'
      })
    }
  })
}
// 取消置顶
const handleTopingDel = auto_pk => {
  requests.vest_collect_delete({ auto_pk }).then(res => {
    if (res.code == 0) {
      ElMessage({
        message: '取消置顶成功',
        type: 'success'
      })
      // handleSwitch()
      vest_collect_list()
    } else {
      ElMessage({
        message: '取消置顶失败',
        type: 'error'
      })
    }
  })
}
defineExpose({
  vestSearch,
  addressVal,
  dhtVal,
  commentInfo,
  handleSwitch,
  vest_list_top,
  vestList
})

</script>
<style>
.dtlClass {
  margin-left: -45px;
  width: 25px;
  margin-right: 50px !important;
}
.dtlClass .el-input__wrapper {
  box-shadow: 0 0 0 0 !important;
  background-color: transparent !important;
}
</style>
<style lang='scss' scoped>
.commentStyle{
  margin-top: -5px;
  margin-left: -35px;
  transform: scale(.90);
}
.vestList{
  width: 765px;
  margin-top: 20px;
  max-height: 340px;
  overflow: hidden;
}
.imgList,
  .imgListClick {
    width: 135px;
    height: 65px;
    padding-top: 5px;
    float: left;
    margin: 0 18px 20px 0px;
    cursor: pointer;
    background-color: #f2f2f2;
    position: relative;
    border-radius: 5px;
    img {
      width: 20px;
      height: 20px;
      margin: 5px 5px 0 5px;
      border-radius: 50%;
      object-fit: cover;
      flex: 1;
      float: left;
    }
    .toping{
      padding: 0 7px;
      height: 15px;
      font-size: 12px;
      color: #0B82FD;
      text-align: center;
      line-height: 15px;
      position: absolute;
      right: 0;
      top: 0;
      background: #F3F9FF;
      border-radius: 0px 4px 0px 4px;
      display: none;
    }
    .topingDef{
      padding: 0 7px;
      height: 15px;
      font-size: 12px;
      color: #fff;
      text-align: center;
      line-height: 15px;
      position: absolute;
      right: 0;
      top: 0;
      background: #0B82FD;
      border-radius: 0px 4px 0px 4px;
    }
    .sub-title {
      float: left;
      margin-top: 5px;
      span {
        display: block;
        height: 25px;
        width: 100px;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
      p {
        width: 100px;
        margin-left: -23px;
        color: rgb(11, 130, 253);
        font-size: 13px;
        margin-top: -5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &:hover {
      border: 1px dashed #0B82FD;
       .toping{
         display: block;
      }
    }
  }
  .imgListClick {
    background-color: #deebfe;
    border-radius: 5px;
  }

</style>
