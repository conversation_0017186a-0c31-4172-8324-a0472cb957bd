<template>
  <div :class="isCircle ? 'myCollectionStyle' : 'box'">
  <div class="line"><i></i><span>评论时间</span></div>
          <el-form-item>
            <el-radio-group v-model="taskType" style="margin-top:-5px" @change="handleRadio()">
              <el-radio :label="1" size="large">
                实时评论
              </el-radio>
              <el-radio :label="2" size="large">
                定时评论
              </el-radio>
            </el-radio-group>
            <div v-show="taskType == 2" style="margin-left:20px">
              <el-date-picker
                v-model="comment_at" type="datetime" placeholder="请选择定时日期"
                :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss" 
                :disabled-date="disabledDate" style="width:200px;margin-right:10px"
              />
            </div>
          </el-form-item>
          <div v-show="taskType == 2" class="timeList">
            <el-button v-for="(item,index) of user_defined_time" :key="index" plain size="small" @click="setTime(item)">
              {{ item }}分钟后
            </el-button>
            <el-button plain size="small" @click="selectTime()">
              +设置
            </el-button>
          </div>
          <el-dialog v-model="timeSet.TimeShow" title="时间设置（最多选择12项）" width="480px" align-center :append-to-body="true" :before-close="handleColseSetTime">
        <div v-for="item of timeSet.timeList" :key="item.id" :class="item.flag ? 'smallHover' :'small'" @click="handleSelectTime(item)">
          {{ item.value }}分钟后
        </div>
          
        <template #footer>
          <el-form-item style="float:right;margin-top:30px">
            <el-button style="margin-right:10px" @click="handleColseSetTime">
              取消
            </el-button>
            <el-button type="primary" @click="handleClickSetTime">
              确认
            </el-button>
          </el-form-item>
        </template>
      </el-dialog>
    </div>
</template>

<script setup name='commentTime'>

import { ref, onMounted, defineProps, defineEmits, defineExpose } from 'vue'
import { requests } from '@/api/business/commentOpera'
import { ElMessage } from 'element-plus'
const user_defined_time = ref([])
const comment_at = ref()
const taskType = ref(1)
const timeSet = ref({
  TimeShow: false,
  timeList: [
    {
      id: 1,
      value: 5,
      flag: false
    },
    {
      id: 2,
      value: 10,
      flag: false
    },
    {
      id: 3,
      value: 15,
      flag: false
    },
    {
      id: 4,
      value: 20,
      flag: false
    },
    {
      id: 5,
      value: 25,
      flag: false
    },
    {
      id: 6,
      value: 30,
      flag: false
    },
    {
      id: 7,
      value: 35,
      flag: false
    },
    {
      id: 8,
      value: 40,
      flag: false
    },
    {
      id: 9,
      value: 45,
      flag: false
    },
    {
      id: 10,
      value: 50,
      flag: false
    },
    {
      id: 11,
      value: 55,
      flag: false
    },
    {
      id: 12,
      value: 60,
      flag: false
    },
    {
      id: 13,
      value: 65,
      flag: false
    },
    {
      id: 14,
      value: 70,
      flag: false
    },
    {
      id: 15,
      value: 75,
      flag: false
    },
    {
      id: 16,
      value: 80,
      flag: false
    },
    {
      id: 17,
      value: 90,
      flag: false
    },
    {
      id: 18,
      value: 100,
      flag: false
    },
    {
      id: 19,
      value: 110,
      flag: false
    },
    {
      id: 20,
      value: 120,
      flag: false
    },
    {
      id: 21,
      value: 150,
      flag: false
    },
    {
      id: 22,
      value: 180,
      flag: false
    },
    {
      id: 23,
      value: 210,
      flag: false
    },
    {
      id: 24,
      value: 240,
      flag: false
    }
  ],
  pushList: []
})
const props = defineProps({
  isCircle: {
    type: Boolean
  }
})
// 快捷时间设置
const handleSelectTime = val => {
  val.flag = !val.flag
  if (val.flag) {
    const index = timeSet.value.pushList.indexOf(val.value)
    if (index !== -1) {
      timeSet.value.pushList.splice(index, 1)
    } else {
      timeSet.value.pushList.push(val.value)
    }
  } else {
    const index = timeSet.value.pushList.indexOf(val.value)
    if (index !== -1) {
      timeSet.value.pushList.splice(index, 1)
    }
  }
}
const handleColseSetTime = () => {
  timeSet.value.TimeShow = false
  timeSet.value.pushList = []
  timeSet.value.timeList.forEach(v => {
    return v.flag = false
  })
}
const handleClickSetTime = () => {
  if (timeSet.value.pushList.length > 12) {
    ElMessage({
      message: '最多选择12个时间点',
      type: 'error'
    })
  } else if (timeSet.value.pushList.length == 0) {
    ElMessage({
      message: '最少选择1个时间点',
      type: 'error'
    })
  } else {
    requests.create_or_update({ times: timeSet.value.pushList.sort((a, b) => a - b).join(',') }).then(res => {
      if (res.code == 0) {
        ElMessage({
          message: '时间设置成功',
          type: 'success'
        })
        handleColseSetTime()
        setDefaultTime()
      }
    })
  }
}
onMounted(() => {
  if (taskType.value == 1) {
    const a = new Date().getTime() // 获取到当前时间戳
    const b = new Date(a)
    comment_at.value = nowDate(b)
  }
})
// 切换实时评论
function handleRadio() {
  if (taskType.value == 2) {
    comment_at.value = ''
    setDefaultTime()
  } else {
    const a = new Date().getTime() // 获取到当前时间戳
    const b = new Date(a)
    comment_at.value = nowDate(b)
  }
}
function nowDate (now) {
  let year = now.getFullYear() // 年份
  let month = now.getMonth() + 1 // 月份（0-11）
  let date = now.getDate() // 天数（1到31）
  let hour = now.getHours() // 小时数（0到23）
  let minute = now.getMinutes() // 分钟数（0到59）
  let second = now.getSeconds() // 秒数（0到59）
  month < 10 ? month = '0' + month : month
  date < 10 ? date = '0' + date : date
  hour < 10 ? hour = '0' + hour : hour
  minute < 10 ? minute = '0' + minute : minute
  second < 10 ? second = '0' + second : second
  return (
    year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
  )
}
// 之前日期不能选
function disabledDate (time) {
  return time.getTime() < Date.now() - 8.64e7
}
// 快捷时间设置
function setTime (val) {
  const date = new Date()
  date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * val)
  let y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? ('0' + m) : m
  let d = date.getDate()
  d = d < 10 ? ('0' + d) : d
  let h = date.getHours()
  h = h < 10 ? ('0' + h) : h
  let M = date.getMinutes()
  M = M < 10 ? ('0' + M) : M
  let s = date.getSeconds()
  s = s < 10 ? ('0' + s) : s
  let dateTime = y + '-' + m + '-' + d + ' ' + h + ':' + M + ':' + s
  comment_at.value = dateTime
  // emit('setTime', dateTime)
}
// 快捷时间回显
const selectTime = (() => {
  timeSet.value.TimeShow = true
  user_defined_time.value.forEach(v => {
    timeSet.value.timeList.forEach(item => {
      if (item.value === parseInt(v)) {
        item.flag = true
        timeSet.value.pushList.push(item.value)
      }
    })
  })
})
// 读取自定义时间
const setDefaultTime = () => {
  requests.get_time_array().then(res => {
    if (res.code == 0) {
      if (res.data.user_defined_time.length > 0) {
        user_defined_time.value = res.data.user_defined_time
      }
    }
  })
}

defineExpose({
  comment_at,
  taskType
})
</script>
<style lang='scss' scoped>
.box{
  width: 760px;
  overflow: hidden;
  height: 200px;
}
.myCollectionStyle{
  width: 740px;
  margin-top: -5px;
  margin-left: -35px;
  transform: scale(.90);
}
.line {
  height: 35px;
  line-height: 35px;
  position: relative;
  margin-bottom: 15px;
  border-radius: 2px;

  i {
    position: absolute;
    left: 0;
    top: 10px;
    width: 5px;
    height: 16px;
    background-color: #46a5e2;
  }

  span {
    font-size: 14px;
    position: absolute;
    left: 15px;
  }
}
.timeList{
  width: 320px;
  float: right;
  margin-top: -55px;
  z-index: 999;
  position: relative;
  button{
    width: 70px;
    float: left !important;
    margin:5px 3px  !important
  }
}

.small{
  width: 79px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 4px;
  text-align: center;
  line-height: 32px;
  border: 1px solid rgba(0,0,0,0.2);
  margin: 5px;
  float: left;
  cursor: pointer;
}
.smallHover{
  color:#0B82FD;
  width: 79px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  background: rgba(11,130,253,0.2);
  border-radius: 4px;
  margin: 5px;
  border: 1px solid #0B82FD;
  float: left;
  cursor: pointer;
}
</style>