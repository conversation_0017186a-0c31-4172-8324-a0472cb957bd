<template>
  <el-dialog
    class="deleteAlert"
    @open="open"
    @close="close"
    :align-center="true"
    v-model="dialogShow"
    :title="
      deleteAlertData.type == '已通过'
        ? `是否确认删除？`
        : `是否确认撤销并通过？`
    "
    width="35%"
  >
    <!-- width="1000" -->

    <p class="dialogTableVisibleTitle1">已选中评论 <b>1</b> 条</p>
    <p class="dialogTableVisibleTitle2" v-show="dialogData.total > 0">
      <img :src="gantan" alt="" />{{
        deleteAlertData.type == "已通过"
          ? `将同步删除评论`
          : `将同步撤销并通过评论`
      }}
      <b>{{ dialogData.total }}</b> 条<span
        v-if="dialogData.total > 0"
        v-show="!commentTableShow"
        @click="commentTableShow = true"
        >展开查看</span
      >
      <span
        v-if="dialogData.total > 0"
        v-show="commentTableShow"
        @click="commentTableShow = false"
        >收起</span
      >
    </p>
    <el-table
      v-show="commentTableShow"
      :data="dialogData.records"
      :border="true"
      height="350"
      :stripe="true"
    >
      <el-table-column property="auto_pk" label="序号" width="150" />
     <el-table-column label="评论内容"> <template #default="scope"> <div v-html="scope.row.content"></div> </template> </el-table-column>
      <el-table-column property="comment_account" width="150" label="评论人" />
    </el-table>
    <!-- 分页 -->
    <div class="dialogPage" v-show="commentTableShow">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="dialogData.total"
        :page-size="dialogData.size"
        :current-page="dialogData.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="dialogHandleCurrentChange"
        @size-change="dialogHandleSizeChange"
      >
      </el-pagination>
    </div>
    <p
      v-show="deleteAlertData.type == '已通过'"
      class="dialogTableVisibleTitle3"
    >
      删除后，可在“已删除”页面撤销并通过
    </p>
    <p
      v-show="deleteAlertData.type == '已删除'"
      class="dialogTableVisibleTitle3"
    >
      撤销并通过后，可在“已通过”页面删除
    </p>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="
            dialogShow = false;
            commentTableShow = false;
          "
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="
            emit('deleteClickFun');
            loading = true
          "
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup name="deleteAlert">
import gantan from "@/assets/images/gantan.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { requests } from "@/api/business/auditManagement";
import { ref, onMounted, defineEmits } from "vue";
import { WarningFilled } from "@element-plus/icons";
const props = defineProps({
  deleteAlertData: {
    type: Object,
  },
});
const emit = defineEmits(["search", "deleteClickFun"]);
const dialogShow = ref(false);
const commentTableShow = ref(false);
const loading = ref(false)
// 删除弹出框数据
const dialogData = ref({
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: [],
});
//   打开时调用接口 已删除弹窗级联的二级和三级评论列表接口
const open = () => {
  console.log("接收的数据", props.deleteAlertData);
  dialogData.value.records = [];
  dialogData.value.total = 0;
  dialogData.value.current = 1;
  if (props.deleteAlertData.comment_level == 3) {
  } else {
    alertSearch({
      state: props.deleteAlertData.type == "已通过" ? "PASS" : "DELETE",
      size: dialogData.value.size,
      current: dialogData.value.current,
      comment_id: props.deleteAlertData.comment_id,
      comment_level: props.deleteAlertData.comment_level,
    });
  }
};
const close = () => {
  commentTableShow.value = false;
  dialogData.value = {
    total: 0, // 总条目数
    size: 100, // 每页显示条目
    current: 1, // 当前页码
    // 列表数据
    records: [],
  };
};
//   搜索接口
const alertSearch = (obj) => {
  requests
    .getSubComments(obj)
    .then((res) => {
      if (res.code == 0) {
        dialogData.value.records = res.data.pg.records;
        dialogData.value.total = res.data.pg.total;
        dialogData.value.current = obj.current;
      } else {
        ElMessage({
          type: "error",
          message: "请求失败！",
        });
      }
    })
    .catch((error) => {
      ElMessage({
        type: "error",
        message: "请求失败！",
      });
    });
};

// 点击分页器
const dialogHandleCurrentChange = (val) => {
  dialogData.value.current = val;
  alertSearch({
    state: props.deleteAlertData.type == "已通过" ? "PASS" : "DELETE",
    size: dialogData.value.size,
    current: val,
    comment_id: props.deleteAlertData.comment_id,
    comment_level: props.deleteAlertData.comment_level,
  });
};
defineExpose({
  dialogShow,
  commentTableShow,
  loading
});
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}
.deleteAlert {
  .dialogTableVisibleTitle1,
  .dialogTableVisibleTitle2,
  .dialogTableVisibleTitle3 {
    span {
      display: inline-block;
      margin-left: 15px;
      color: #f59a23;
      cursor: pointer;
    }
  }
  .dialogTableVisibleTitle2 {
    margin: 20px 0;
    img {
      width: 20px;
      margin-right: 5px;
      position: relative;
      top: 3px;
    }
  }
  .dialogTableVisibleTitle3 {
    margin-bottom: 0;
    color: #f59a23;
  }
  .dialogPage {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
