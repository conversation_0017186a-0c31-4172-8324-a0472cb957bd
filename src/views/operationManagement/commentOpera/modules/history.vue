<template>
  <el-drawer
    id="drawer" v-model="drawer" title="操作日志" :with-header="true" show-close="true" size="700px"
    @open="open" @close="close"
  >
    <p>评论内容：<span v-html="title"></span></p>
    <el-table :data="data" style="width: 100%" :max-height="700">
      <el-table-column v-slot="scope" prop="created_at" label="操作时间">
        {{ handleTime(scope.row.created_at) }}
      </el-table-column>
      <el-table-column v-slot="scope" prop="item_type" label="操作事项">
        <span v-if="scope.row.item_type === 1">创建评论任务</span>
        <span v-else-if="scope.row.item_type === 2">执行评论任务</span>
        <span v-else-if="scope.row.item_type === 3">停止评论任务</span>
        <span v-else-if="scope.row.item_type === 4">系统异常</span>
        <span v-else-if="scope.row.item_type === 5">编辑评论任务</span>
      </el-table-column>
      <el-table-column prop="item_desc" label="事项说明" />
      <el-table-column prop="operation_person" label="操作人" width="100" />
    </el-table>
  </el-drawer>
</template>
<script setup>
import { ref } from 'vue'
import { requests } from '@/api/business/commentOpera'
const drawer = ref(false) // 界面显示
const id = ref(null)
const title = ref('')
// 下拉选择的值
const data = ref([])

function init () {
  requests.getHistory({ task_id: id.value }).then(res => {
    data.value = res.data.log_list.records
  })
}
// 时间转换
function handleTime (time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s

}
defineExpose({
  drawer,
  id,
  title,
  init
})

</script>
  <style lang="scss" scoped>
    .elem {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-input {
        flex: 1;
      }
    }
    .elem1 {
      padding: 0;
      max-height: 120px;
      overflow-y: scroll;
      li {
        justify-content: space-between;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px;
        &.che {
          background: #ebebeb;
        }
        & > span {
          padding-right: 15px;
          font-size: 14px;
        }
        .el-button {
          height: 30px;
        }
      }
    }
    .elem2 {
      margin-top: 30px;
      display: flex;
      justify-content: flex-end;
    }

  </style>
