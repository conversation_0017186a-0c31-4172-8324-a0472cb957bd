<template>
  <div class="head-box">
    <div class="left">
      <el-button plain type="primary" @click="back">
        <el-icon><Back /></el-icon>
        <span style="margin-left:5px;">返回</span>
      </el-button>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->

      <el-date-picker
        v-model="time"
        style="width: 250px;margin-right: 10px"
        class="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
      />
      <el-cascader
        v-model="cascadeValue"
        :options="options"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="optionsProps"
        placeholder="全部频道"

        @change="change"
      />
      <el-input v-model="task_create_name" clearable placeholder="请输入创建人" style="width: 150px;margin-right: 10px" />
      <el-input
        v-model="searchData.searchword"
        placeholder="请输入关键词"
        class="input-with-select"
        clearable @keyup.enter="handSearch"
      >
        <template #prepend>
          <el-select
            v-model="searchData.comment_search_type"

            placeholder="请选择"
            style="width:100px;"
            @change="typeChange"
          >
            <el-option v-for="item of commentSearchType" :key="item.key" :label="item.label" :value="item.key">
            </el-option>
          </el-select>
        </template>
        <template #append>
          <el-button

            type="primary" :icon="Search"
            style="display: flex;align-items: center;"
            @click="handSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search, Back } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose } from 'vue'
// 组件传参声明方式
const props = defineProps({
  selectData: {
    type: Array
  }
})
// 组件接收事件
const emit = defineEmits(['search', 'back'])
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const task_create_name = ref('')
const optionsProps = ref({
  value: 'category_id',
  label: 'name'
})
const time = ref([])
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
]
// 稿件标题时的关键字
const title_search_data = ref(null)
// 搜索类型
const commentSearchType = ref([
  {
    key: 'article_title',
    label: '稿件标题'
  },
  {
    key: 'article_id',
    label: '稿件ID'
  }
])
const loading = ref(false)
// 搜索参数
const searchData = ref({
  category_id: '',
  comment_source: '', // 频道名称
  comment_search_type: 'article_id',
  searchword: '' // 关键词
})
onMounted(() => {
  requests.listCategoryTree().then(res => {
    console.log(res)
    // options.value = res.data.category_list

    let arr = res.data.category_list
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1)
      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1)
          } else {
            for (
              let a = 0;
              a < arr[i].children[k].children.length;
              a++
            ) {
              if (
                arr[i].children[k].children[a].permission == 0
              ) {
                arr[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr
    })
  })
})
function back() {
  searchData.value.category_id = ''
  task_create_name.value = ''
  searchData.value.searchword = ''
  searchData.value.comment_search_type = 'article_id'
  cascadeValue.value = []
  time.value = []
  emit('back')
}

const typeChange = () => {
  title_search_data.value = ''
  searchData.value.searchword = ''
}

const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.category_id = data[data.length - 1]
  } else {
    searchData.value.category_id = ''
  }
  handSearch()
}

// 点击搜索 每次当前页码变为1
const handSearch = () => {
  let newData = {
    channel_id: searchData.value.category_id,
    task_create_name: task_create_name.value
  }
  newData[searchData.value.comment_search_type] = searchData.value.searchword
  console.log(Boolean(time.value))
  if (time.value) {
    newData.start_create_date = time.value[0]
    newData.end_create_date = time.value[1]
  }
  emit('search', newData)
}

defineExpose({
  searchData,
  title_search_data

})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
    width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
    white-space: pre-wrap;
    height: auto;
    line-height: 24px;
    padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
    background-color: #ebebeb;
}

.head-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .left {
        width: 100px;
    }

    .right {
        display: flex;
        align-items: center;

        & > .el-select {
            width: 150px;
            margin-right: 10px;
        }

        .el-input-group {
            width: 400px;
        }
    }
}
</style>
<style>
.input-with-select .el-input-group__append {
    background-color: #1890ff;
    color: #fff;
}

.el-cascader {
    margin-right: 12px;
}
</style>
