<template>
  <el-drawer
    id="drawer"
    v-model="drawer"
    title="操作日志"
    :with-header="true"
    show-close="true"
    size="800px"
    @open="open"
    @close="close"
  >
    <div class="header">
      <img v-if="info.list_pics && info.list_pics[0]" :src="info.list_pics || info.list_pics[0]" />
      <div>
        <div class="title">
          <a :href="info.article_url" target="_blank">{{ info.list_title || info.title }}</a>
        </div>
        <div class="info">
          <el-button type="primary" bg text="primary" size="small">{{
            info.channel_name
          }}</el-button>
          <span>{{ handleTime(info.published_at) }}</span>
        </div>
      </div>
    </div>
    <el-table id="history-table" :data="data" style="width: 100%">
      <el-table-column v-slot="scope" prop="created_at" label="操作时间" width="180">
        {{ handleTime(scope.row.created_at) }}
      </el-table-column>
      <el-table-column prop="comment_id" label="评论ID" />
      <el-table-column v-slot="scope" prop="item_type" label="操作事项">
        <span v-if="scope.row.item_type === 17">评论回复</span>
        <span v-else-if="scope.row.item_type === 10">点赞加权</span>
        <span v-else-if="scope.row.item_type === 12">评论排序</span>
        <span v-else-if="scope.row.item_type === 11">创建评论任务</span>
        <span v-else-if="scope.row.item_type === 14">评论审核通过</span>
        <span v-else-if="scope.row.item_type === 15 || scope.row.item_type === 16"
          >固定/取消固定</span
        >
        <span v-else-if="scope.row.item_type === 27">删除</span>
        <span v-else-if="scope.row.item_type === 28">通过</span>
        <span v-else-if="scope.row.item_type === 29">撤销并通过</span>
        <span v-else-if="scope.row.item_type === 30">插入位置</span>
        <span v-else-if="scope.row.item_type === 31">热评固定位置</span>
        <span v-else-if="scope.row.item_type === 32">热评取消固定位置</span>
        <span v-else-if="scope.row.item_type === 33">热评上下调序</span>
        <span v-else-if="scope.row.item_type === 34">新建互动组件评论</span>
      </el-table-column>
      <el-table-column prop="item_desc" label="事项说明" />
      <el-table-column prop="operation_person" label="操作人" width="100" />
    </el-table>
  </el-drawer>
</template>
<script setup>
import { ref } from 'vue'
import { requests } from '@/api/business/commentOpera'
const drawer = ref(false) // 界面显示
const info = ref({})
// 下拉选择的值
const data = ref([])

function init() {
  let id = info.value.original_id ? info.value.original_id : info.value.id
  if (info.value.uuid || info.value.ugc_uuid) {
    id = info.value.uuid || info.value.ugc_uuid
  }
  requests.getOperateHistory({ article_id: id }).then(res => {
    data.value = res.data.log_list
  })
}
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
defineExpose({
  drawer,
  info,
  init
})
</script>
<style lang="scss" scoped>
#history-table{
  height: calc(100vh - 242px);
}
.elem {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-input {
    flex: 1;
  }
}
.elem1 {
  padding: 0;
  max-height: 120px;
  overflow-y: scroll;
  li {
    justify-content: space-between;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    &.che {
      background: #ebebeb;
    }
    & > span {
      padding-right: 15px;
      font-size: 14px;
    }
    .el-button {
      height: 30px;
    }
  }
}
.elem2 {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}
.header {
  width: 747px;
  min-height: 80px;
  margin-bottom: 35px;
  position: relative;
  display: flex;
  img {
    width: 100px;
    height: 80px;
    margin-right: 20px;
    object-fit: cover;
    flex-shrink: 0;
  }
  .title {
    width: 610px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
    margin-bottom: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    a {
      color: #606266;
      text-decoration: none;
    }
  }
  .info {
    width: 430px;
    font-size: 12px;
    i {
      font-style: normal;
      padding: 2px 5px;
      text-align: center;
      background-color: #409eff;
      color: #fff;
    }
    span {
      margin-left: 5px;
      color: #666;
    }
  }
}
</style>
