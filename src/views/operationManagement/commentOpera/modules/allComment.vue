<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    :title="'共有' + dataVal.countNum + '条评论'"
    :size="900"
    :before-close="handleClose"
    @open="openDrawer"
  >
    <div class="header">
      <img v-if="dataVal.list_pics" :src="dataVal.list_pics" />
      <div class="title">
        <a :href="dataVal.article_url" target="_blank">{{ dataVal.article_title }}</a>
      </div>
      <div class="info">
        <el-button
          type="primary"
          bg
          text="primary"
          size="small"
          style="border: 1px solid #0b82fd; padding: 0 5px; height: 18px"
        >
          {{ dataVal.channel_name }}
        </el-button>
        <span>{{ dataVal.article_published_at }}</span>
      </div>
    </div>
    <div v-infinite-scroll="load" class="comment">
      <div v-for="(item, index) of comment_level" :key="item.id" class="level_1">
        <div class="name">
          <img class="userImg" :src="item.portrait_url" @error="setDefaultImage" />{{
            item.comment_user_name
          }}
        </div>

        <div class="cont">
          <span v-html="item.content"></span
          ><span v-show="item.hotCommentsFlag" class="icon"></span>
        </div>
        <img v-if="item.expression_url" :src="item.expression_url" class="expression" />
        <img v-if="item.pic_url" :src="item.pic_url" class="expression" />
        <iframe
          v-if="item.activity_url"
          class="ifrBox"
          scrolling="no"
          :src="item.activity_url"
          frameborder="0"
          width="300"
          :height="item.activity_ratio_height * (300 / item.activity_ratio_width)"
          @error="handleIframeError"
        ></iframe>
        <div class="time">{{ changeFormdate(item.created_at) }} {{ item.location }}</div>
        <div class="edit">
          <span class="font" @click="level2_handleReply(item, index)"
            ><span class="reply"></span>回复</span
          >
          <span
            v-auth="'vest_like:like_save'"
            class="cheat"
            @click="
              handleCheat(
                item.like_count,
                item.id,
                item.account_id,
                item.comment_user_name,
                dataVal.article_id
              )
            "
            ><span class="likeIcon"></span>点赞加权({{ item.like_count }})</span
          >
          <span
            v-auth="'comment_top:set_top'"
            class="heat"
            @click="handleHot(item.hotCommentsFlag, item.id, dataVal.article_id)"
            ><span class="hotCommentIcon"></span
            >{{ item.hotCommentsFlag ? '取消热评' : '设为热评' }}</span
          >
          <span
            v-auth="'comment:batch_action'"
            class="delete"
            @click="handleDelete(item, article_id)"
            ><span class="delComment"></span>删除</span
          >
        </div>

        <div v-if="item.sub_comment" class="level_2">
          <div class="name">
            <img class="userImg" :src="item.sub_comment.portrait_url" @error="setDefaultImage" />{{
              item.sub_comment.comment_user_name
            }}
          </div>

          <div class="cont" v-html="item.sub_comment.content"></div>
          <img
            v-if="item.sub_comment.expression_url"
            :src="item.sub_comment.expression_url"
            class="expression"
          />
          <div class="time">
            {{ changeFormdate(item.sub_comment.created_at) }} {{ item.sub_comment.location }}
          </div>
          <div class="edit">
            <span class="font" @click="level3_handleReply(item.sub_comment, index)"
              ><span class="reply"></span>回复</span
            >
            <span
              v-auth="'vest_like:like_save'"
              class="cheat"
              @click="
                handleCheat(
                  item.sub_comment.like_count,
                  item.sub_comment.id,
                  item.sub_comment.account_id,
                  item.sub_comment.comment_user_name,
                  dataVal.article_id
                )
              "
              ><span class="likeIcon"></span>点赞加权({{ item.sub_comment.like_count }})</span
            >
            <span
              v-auth="'comment:batch_action'"
              class="delete"
              @click="handleDelete(item.sub_comment, article_id)"
              ><span class="delComment"></span>删除</span
            >
          </div>
        </div>
        <div
          v-show="item.sub_count >= 1 && !item.more_Comment"
          class="expansion"
          @click="
            expansion(
              item.id,
              item.sub_comment.id,
              item.sort_number,
              item.sub_comment.sort_number,
              item.channel_article_id,
              1000,
              index
            )
          "
        >
          <span></span>展开{{ item.sub_count }}条评论<el-icon>
            <ArrowDown />
          </el-icon>
        </div>
        <div v-for="more_comment of item.more_Comment" :key="more_comment.id" class="level_2">
          <div class="name">
            <img class="userImg" :src="more_comment.portrait_url" @error="setDefaultImage" />{{
              more_comment.comment_user_name
            }}
            <span v-if="more_comment.comment_level == 3"
              ><span class="arrow"></span>
              <img
                class="userImg"
                :src="more_comment.parent_portrait_url"
                @error="setDefaultImage"
              />{{ more_comment.parent_nick_name }}</span
            >
          </div>
          <div
            v-show="more_comment.comment_level == 2"
            class="cont"
            v-html="more_comment.content"
          ></div>
          <div
            v-show="more_comment.comment_level == 3"
            class="cont"
            v-html="more_comment.content"
          ></div>
          <img
            v-if="more_comment.expression_url"
            :src="more_comment.expression_url"
            class="expression"
          />
          <div class="time">
            {{ changeFormdate(more_comment.created_at) }} {{ more_comment.location }}
          </div>
          <div class="edit">
            <span class="font" @click="levelMore_handleReply(more_comment, index)"
              ><span class="reply"></span>回复</span
            >
            <span
              v-auth="'vest_like:like_save'"
              class="cheat"
              @click="
                handleCheat(
                  more_comment.like_count,
                  more_comment.id,
                  more_comment.account_id,
                  more_comment.comment_user_name,
                  dataVal.article_id
                )
              "
            >
              <span class="likeIcon"></span>点赞加权({{ more_comment.like_count }})</span
            >
            <span
              v-auth="'comment:batch_action'"
              class="delete"
              @click="handleDelete(more_comment, article_id)"
              ><span class="delComment"></span>删除</span
            >
          </div>
        </div>
        <div v-show="item.more_Comment" class="putAway" @click="putAway(index)">
          <span></span>收起回复<el-icon>
            <ArrowUp />
          </el-icon>
        </div>
      </div>
      <div v-show="moreNullData" class="more_null">没有更多数据了</div>
    </div>
    <el-dialog v-model="cheatShow" title="马甲号点赞" width="35%" align-center :show-close="false">
      <span style="margin-right: 20px">请选择需要加权<span style="color: red">至</span>多少赞</span>
      <el-input-number
        v-model="ehatVal"
        :max="minEhatVal + 50"
        :min="minEhatVal"
        size="default"
        @keydown="channelInputLimit"
      ></el-input-number>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="margin-right: 20px" @click="cheatShow = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleAddCheatNum"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
    <DeleteAlert
      ref="deleteAlert"
      :delete-alert-data="deleteAlertData"
      @search="search"
      @deleteClickFun="deleteClickFun"
    />
    <el-drawer
      v-model="innerDrawer"
      :append-to-body="true"
      direction="btt"
      :size="820"
      style="width: 900px; left: auto"
      :with-header="false"
      :before-close="handleInnerDrawer"
    >
      <el-radio-group
        v-model="selectTye"
        class="ml-4"
        style="margin-bottom: 5px"
        @change="handleChangeSelectTye"
      >
        <el-radio label="1" size="large">马甲号评论</el-radio>
        <el-radio v-auth="'comment:editor_replay'" label="2" size="large">潮新闻小编</el-radio>
      </el-radio-group>
      <div v-if="selectTye == 1" class="innerDrawer">
        <el-form-item label="回复评论">
          <div>
            <div class="text" v-html="replyData.content"></div>
          </div>
        </el-form-item>

        <WeditorV5
          ref="weditorRef"
          class="handlePa"
          :comment_content="dataVal.comment_content"
          @contentVal="handleContentVal"
        ></WeditorV5>
        <div
          v-show="sensitive.length > 0"
          style="width: 100%; text-align: left; margin-bottom: 20px"
        >
          <span style="color: red">发现敏感词：{{ sensitive }}</span>
        </div>
        <div style="margin: 15px 0 10px 0; width: 100%; overflow: hidden">
          <div class="emoji" @click="emojiClick"><span class="emojiIcon"></span>emoji</div>
          <picManagement
            ref="picmanagementRef"
            @succ="handlePicManagement"
            @onOpen="showEmojis = false"
          >
          </picManagement>
        </div>
        <!-- emoji表情 -->
        <Picker
          v-show="showEmojis"
          :data="emojiIndex"
          set="apple"
          :show-preview="false"
          :show-search="false"
          :emoji-tooltip="false"
          :show-skin-tones="false"
          :auto-focus="true"
          style="margin-bottom: 25px"
          @select="showEmoji"
        />
        <div class="line"><i></i><span>随机生成评论人</span></div>
        <Commentator ref="commentatorRef" @selectCommentator="handleCommentator"></Commentator>
        <div class="line"><i></i><span>收藏的评论人</span></div>
        <myCollection ref="myCollectionRef" @selectMyCollection="handleMyCollection"></myCollection>
        <div style="position: absolute; right: 20px; bottom: 20px">
          <el-button style="margin-right: 10px" @click="handleInnerDrawer()">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submitForm()">提交</el-button>
        </div>
      </div>
      <div v-else class="reply">
        <el-form :model="form" label-width="80px" style="padding-right: 12px">
          <!-- 回复 -->
          <el-form-item label="回复评论">
            <div>
              <p class="text" v-html="replyData.content"></p>
            </div>
          </el-form-item>
          <el-form-item label="回复内容">
            <!-- <el-input v-model="comment_content" type="textarea" maxlength="250" show-word-limit :rows="10" resize="none"
              placeholder="请输入回复内容" @input="change" /> -->
            <WeditorV5
              ref="weditorRef"
              :comment_content="comment_content"
              @contentVal="handleContentVal"
            ></WeditorV5>
            <div v-show="comment_content" class="endText">{{ sensitive_words }}</div>
            <div style="margin: 15px 0 10px 0; width: 100%; overflow: hidden">
              <div class="emoji" @click="emojiClick"><span class="emojiIcon"></span>emoji</div>
              <picManagement
                ref="picmanagementRef"
                @succ="handlePicManagement"
                @onOpen="showEmojis = false"
              >
              </picManagement>
            </div>
            <!-- emoji表情 -->
            <Picker
              v-show="showEmojis"
              :data="emojiIndex"
              set="apple"
              :i18n="I18n"
              :show-preview="false"
              :show-search="false"
              :emoji-tooltip="false"
              :show-skin-tones="false"
              :auto-focus="true"
              @select="showEmoji2"
            />
          </el-form-item>
        </el-form>
        <div style="position: absolute; right: 20px; bottom: 20px">
          <el-button style="margin-right: 10px" @click="handleInnerDrawer()">取消</el-button>
          <el-button :loading="loading" type="primary" @click="sureClick()">提交</el-button>
        </div>
      </div>
      <CommentTime ref="commentTimeRef"></CommentTime>
    </el-drawer>
  </el-drawer>
</template>
<script setup name="allComment">
import { EluiChinaAreaDht } from 'elui-china-area-dht'
import { Picker, EmojiIndex } from 'emoj-vue-chao/src'
import Commentator from './comment/commentator.vue'
import CommentTime from './comment/commentTime.vue'
import myCollection from './comment/myCollection.vue'
import WeditorV5 from '@/components/weditor/v5.vue'
import picManagement from '@/views/operationManagement/commentOpera/modules/picManagement.vue'
import data from '@/assets/google.json'
import 'emoj-vue-chao/css/emoji-mart.css'
import { ref, watch, nextTick } from 'vue'
import { formatDate } from '@/util/filter'
import { ElMessage, ElMessageBox } from 'element-plus'
import { requests } from '@/api/business/commentOpera'
import { ChatDotRound, CaretRight, ArrowDown, Refresh, ArrowUp } from '@element-plus/icons'
import DeleteAlert from './deleteAlert.vue'
import defaultImg from '@/assets/images/def.png'
const commentatorRef = ref(null)
const myCollectionRef = ref(null)
const commentTimeRef = ref(null)
// 删除弹窗
const deleteAlert = ref(null)
// 传入删除弹窗的数据
const deleteAlertData = ref({
  type: '已通过', // 判断哪个页面进去的
  comment_id: '', // 删除接口需要的comment_id
  comment_level: '',
  article_id: '',
  top_comment_id: '',
  second_comment_id: ''
})

const selectTye = ref('1')
const dataVal = ref({
  countNum: '',
  article_title: '',
  article_id: '',
  article_url: '',
  article_published_at: '',
  list_pics: '',
  article_user_id: '',
  channel_id: '',
  channel_name: '',
  comment_content: '',
  user_placeholder: '',
  comment_user_id: '',
  comment_user_name: '',
  comment_user_portrait: '',
  comment_user_location: '',
  parent_comment_id: '',
  top_comment_id: '',
  second_comment_id: '',
  comment_level: '',
  expression_id: '',
  expression_url: '',
  comment_at: '',
  task_type: 1
})
const ehatVal = ref(0)
const minEhatVal = ref(0)
const cheatShow = ref(false)
const likeVal = ref({
  comment_id: '',
  comment_user_id: '',
  comment_user_name: '',
  article_id: '',
  size: ''
})
const emojiIndex = new EmojiIndex(data)
const loading = ref(false)
const dataIndex = ref('')
const comment_level = ref([])
const sensitive = ref('')
const drawer = ref(false)
const innerDrawer = ref(false)
const moreNullData = ref(false)
const showEmojis = ref(false)
const myCommentFlag = ref(false)
const loadFlag = ref(true)
const hotFlag = ref(false)
const weditorRef = ref()
const picmanagementRef = ref(null)
// 生成记录一个时间戳
const timeStamp = ref()
watch(innerDrawer, (newValue, oldValue) => {
  if (innerDrawer.value) {
    const a = new Date().getTime() // 获取到当前时间戳
    const b = new Date(a)
    if (commentTimeRef.value) {
      commentTimeRef.value.comment_at = nowDate(b)
    }
  }
})
// 弹窗打开
function openDrawer() {

  timeStamp.value = new Date().getTime()
  console.log('openDrawer',timeStamp.value)
}
// 打开emoji弹窗
function emojiClick() {
  showEmojis.value = !showEmojis.value
  picmanagementRef.value.presetData.imgBox = false
  picmanagementRef.value.showCustom = false
}
function nowDate(now) {
  let year = now.getFullYear() // 年份
  let month = now.getMonth() + 1 // 月份（0-11）
  let date = now.getDate() // 天数（1到31）
  let hour = now.getHours() // 小时数（0到23）
  let minute = now.getMinutes() // 分钟数（0到59）
  let second = now.getSeconds() // 秒数（0到59）
  month < 10 ? (month = '0' + month) : month
  date < 10 ? (date = '0' + date) : date
  hour < 10 ? (hour = '0' + hour) : hour
  minute < 10 ? (minute = '0' + minute) : minute
  second < 10 ? (second = '0' + second) : second
  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}
// 预设 本地传值
function handlePicManagement(url, id) {
  dataVal.value.expression_id = id
  dataVal.value.expression_url = url
}
// 富文本内容
const handleContentVal = val => {
  if (selectTye.value == '1') {
    dataVal.value.comment_content = val
  } else {
    comment_content.value = val
  }
}
const handleChangeSelectTye = () => {
  weditorRef.value.clearEditor()
  commentTimeRef.value.comment_at = ''
  commentTimeRef.value.taskType = 1
}
const handleIframeError = event => {
  try {
    event.preventDefault()
  } catch (error) {
    console.error('发生错误:', error)
  }
}
// 评论条数
const countNum = () => {
  requests.count({ article_id: dataVal.value.article_id }).then(res => {
    if (res.code == 0) {
      dataVal.value.countNum = res.data.count
    }
  })
}
// 默认头像图片
const setDefaultImage = event => {
  event.target.src = defaultImg
}
// 随机评论事件
const handleCommentator = () => {
  myCollectionRef.value.vestList.forEach(v => {
    v.isClick = false
  })
  myCommentFlag.value = false
}
// 收藏评论点击事件
const handleMyCollection = () => {
  commentatorRef.value.vestList.forEach(v => {
    v.isClick = false
  })
  commentatorRef.value.vest_list_top.forEach(v => {
    v.isClick = false
  })
  myCommentFlag.value = true
}
// 删除评论
function handleDelete(item, article_id) {
  deleteAlertData.value.comment_id = item.id
  deleteAlertData.value.comment_level = item.comment_level
  ;(deleteAlertData.value.article_id = article_id),
    (deleteAlertData.value.top_comment_id = item.top_comment_id)
  deleteAlertData.value.second_comment_id = item.second_comment_id
  // 显示弹窗
  deleteAlert.value.dialogShow = true
}
function deleteClickFun() {
  // 删除接口
  requests
    .batchAction({
      comment_ids: deleteAlertData.value.comment_id,
      status: 'DELETE',
      current_status: 'PASS',
      article_id: deleteAlertData.value.article_id,
      top_comment_id: deleteAlertData.value.top_comment_id,
      second_comment_id: deleteAlertData.value.second_comment_id,
      comment_level: deleteAlertData.value.comment_level
    })
    .then(res => {
      if (res.code == 0) {
        ElMessage({
          type: 'success',
          message: '删除成功！'
        })
        deleteAlert.value.dialogShow = false
        deleteAlert.value.loading = false
        floor_list()
        countNum()
        clear()
      } else {
        ElMessage({
          type: 'error',
          message: '删除失败！'
        })
        deleteAlert.value.dialogShow = false
        deleteAlert.value.loading = false
      }
    })
}
// 取消设置热评
function handleHot(v, comment_id, article_id) {
  hotFlag.value = true
  // 设置热评
  let action
  v ? (action = 'OFF') : (action = 'ON')
  requests.set_top({ action, comment_id, article_id }).then(res => {
    if (res.code == 0) {
      floor_list()
      ElMessage({
        type: 'success',
        message: '设置成功！'
      })
    } else {
      ElMessage({
        type: 'error',
        message: '设置失败！'
      })
    }
  })
}
// 小编回复相关
const comment_content = ref('') // 回复评论
const sensitive_words = ref(null) // 提示语句
const replyData = ref({
  id: '',
  article_title: '',
  comment_account: '',
  content: ''
})

// 点击确定
const sureClick = () => {
  // 敏感词接口
  requests
    .checkSensitiveWord({
      comment_content: comment_content.value.replace(/<[^>]+>/g, '')
    })
    .then(res => {
      console.log(res)
      // 校验
      if (res.code == 0) {
        // 存在敏感词
        if (res.data.result.length) {
          // 提取敏感词数组
          let detectResult = res.data.result
          let arr = []
          for (let i = 0; i < detectResult.length; i++) {
            arr.push(detectResult[i].content)
          }
          const sensitiveText = arr.toString()
          sensitive_words.value = `发现敏感词：${sensitiveText}`
          // 如果有敏感词
          ElMessageBox.confirm(
            `经校验，发现<b style="color:red;">${sensitiveText}</b>为敏感词，是否确认继续提交当前回复！`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'Warning',
              dangerouslyUseHTMLString: true,
              center: true
            }
          )
            .then(() => {
              // 忽略敏感词 继续提交
              editorReplay()
            })
            .catch(() => {
              ElMessage({
                type: 'info',
                message: '已取消！'
              })
            })
        } else {
          // 没有敏感词
          editorReplay()
        }
      }
    })
}

function showEmoji2(emoji) {
  weditorRef.value.editorRef.focus()
  weditorRef.value.editorRef.insertText(emoji.native)
}
// 小编回复接口
const editorReplay = () => {
  comment_content.value = removeTagsExceptA(comment_content.value)
  loading.value = true

  requests
    .editorReplay({
      replay_comment_id: replyData.value.id,
      replay_comment_content: comment_content.value,
      expression_id: dataVal.value.expression_id,
      expression_url: dataVal.value.expression_url,
      comment_at: commentTimeRef.value.comment_at,
      task_type: commentTimeRef.value.taskType,
      article_published_at: dataVal.value.article_published_at,
      list_pics: dataVal.value.list_pics,
      channel_id: dataVal.value.channel_id,
      channel_name: dataVal.value.channel_name
    })
    .then(res => {
      if (res.code == 0) {
        setTimeout(() => {
          ElMessage({
            type: 'success',
            message: '回复成功！'
          })
          loading.value = false
          floor_list()
          countNum()
          // 清除
          replyData.value = {
            id: '',
            article_title: '',
            comment_account: '',
            content: ''
          }
          selectTye.value = '1'
          comment_content.value = ''
          sensitive_words.value = null
          innerDrawer.value = false

          clear()
        }, 1500)
      } else {
        loading.value = false
      }
    })
    .catch(error => {
      loading.value = false
    })
}
// 加权
function handleCheat(minVal, comment_id, comment_user_id, comment_user_name, article_id) {
  // 最小值
  minEhatVal.value = minVal
  // 判断是否能加权
  requests.is_exists({ comment_id: comment_id }).then(res => {
    if (res.code == 0) {
      if (res.data.exists == 0) {
        ehatVal.value = minEhatVal.value
        loading.value = false
        cheatShow.value = true
        likeVal.value.comment_id = comment_id
        likeVal.value.comment_user_id = comment_user_id
        likeVal.value.comment_user_name = comment_user_name
        likeVal.value.article_id = article_id
      } else if (res.data.exists == 1) {
        ElMessage({
          type: 'info',
          message: '此条评论已加权过！'
        })
      }
    }
  })
}
// 加权接口
function handleAddCheatNum() {
  likeVal.value.size = ehatVal.value - minEhatVal.value
  if (likeVal.value.size) {
    loading.value = true
    requests.like_save(likeVal.value).then(res => {
      if (res.code == 0) {
        ElMessage({
          type: 'success',
          message: '加权成功！'
        })
        floor_list()
        loading.value = false
        cheatShow.value = false
      } else {
        ElMessage({
          type: 'error',
          message: '加权失败！'
        })
      }
    })
  } else {
    ElMessage({
      type: 'error',
      message: '数值需要大于0！'
    })
  }
}
// 禁止输入数字
function channelInputLimit(e) {
  e.returnValue = ''
}
function showEmoji(emoji) {
  weditorRef.value.editorRef.focus()
  weditorRef.value.editorRef.insertText(emoji.native)
}
// 清除状态
function clear() {
  var scrollBox = document.getElementsByClassName('el-drawer__body')[1]
  scrollBox.scrollTo(0, 0)
  showEmojis.value = false
  if (commentTimeRef.value) {
    commentTimeRef.value.comment_at = ''
    commentTimeRef.value.taskType = 1
  }
  selectTye.value = '1'
  dataVal.value.task_type = 1
  dataVal.value.comment_user_id = ''
  dataVal.value.comment_user_name = ''
  dataVal.value.comment_user_portrait = ''
  dataVal.value.comment_user_location = ''
  dataVal.value.comment_content = ''
  dataVal.value.user_placeholder = ''
  dataVal.value.parent_comment_id = ''
  dataVal.value.top_comment_id = ''
  dataVal.value.second_comment_id = ''
  dataVal.value.comment_level = ''
  dataVal.value.article_user_id = ''
  dataVal.value.expression_url = ''
  dataVal.value.expression_id = ''
  sensitive.value = ''
  sensitive_words.value = ''
  if (commentatorRef.value) {
    commentatorRef.value.vestSearch = {
      nick_name: '',
      location: '',
      size: 10
    }
    commentatorRef.value.dhtVal = []
    commentatorRef.value.addressVal = ''
    commentatorRef.value.vest_list_top.forEach(v => {
      v.isClick = false
    })
  }

  if (picmanagementRef.value) {
    picmanagementRef.value.presetData = {
      activeTab: 'tab1',
      imgValue: '',
      imgBox: false,
      cover: [],
      selectId: '',
      imgList: []
    }
    picmanagementRef.value.showCustom = false
  }
  loadFlag.value = true
  hotFlag.value = false
}
// 关闭窗口
function handleInnerDrawer() {
  showEmojis.value = false
  innerDrawer.value = false
  loading.value = false
  cheatShow.value = false
  clear()
}

// 窗口置顶
function scrollToTop(element) {
  element.scrollTop = 0
  if (element.parentElement) {
    scrollToTop(element.parentElement)
  }
}

function handleClose() {
  drawer.value = false
  loading.value = false
  comment_level.value = []
  moreNullData.value = false
  // 复位滚动条
  clear()
}

// 展开更多
function expansion(
  top_comment_id,
  second_comment_id,
  sort_number,
  last_sort_number,
  article_id,
  size,
  index
) {
  requests
    .get_more({
      top_comment_id,
      second_comment_id,
      sort_number: last_sort_number,
      last_sort_number,
      article_id,
      size
    })
    .then(res => {
      if (res.code == 0) {
        comment_level.value[index].more_Comment = res.data.comments
      }
    })
}
// 收起
function putAway(index) {
  delete comment_level.value[index].more_Comment
}
// 回复处理
function level2_handleReply(val, index) {
  innerDrawer.value = true
  dataVal.value.second_comment_id = ''
  dataVal.value.user_placeholder = val.comment_user_name
  dataVal.value.parent_comment_id = val.id
  dataVal.value.top_comment_id = val.id
  dataVal.value.article_user_id = val.article_user_id
  dataVal.value.comment_level = 2
  dataIndex.value = index
  replyData.value.id = val.id
  replyData.value.content = val.content
  if (commentatorRef.value) {
    commentatorRef.value.handleSwitch()
  }
}
function load() {
  if (comment_level.value.length > 0 && loadFlag.value) {
    loadFlag.value = false
    let sort_number = comment_level.value[comment_level.value.length - 1].sort_number
    let article_id = dataVal.value.article_id
    const innderTimeStamp = timeStamp.value
    requests
      .floor_list({ article_id, sort_number, size: 20 })
      .then(res => {
        if (innderTimeStamp !== timeStamp.value) {
          return
        }
        if (res.code == 0 && res.data.dtos.length > 0) {
          comment_level.value.push(...res.data.dtos)
          loadFlag.value = true
        } else {
          moreNullData.value = true
        }
      })
  }
}
function level3_handleReply(val, index) {
  innerDrawer.value = true
  dataVal.value.user_placeholder = val.comment_user_name
  dataVal.value.parent_comment_id = val.id
  dataVal.value.top_comment_id = val.top_comment_id
  dataVal.value.second_comment_id = val.id
  dataVal.value.article_user_id = val.article_user_id
  dataVal.value.comment_level = 3
  dataIndex.value = index
  replyData.value.id = val.id
  replyData.value.content = val.content
  if (commentatorRef.value) {
    commentatorRef.value.handleSwitch()
  }
}

function levelMore_handleReply(val, index) {
  innerDrawer.value = true
  dataVal.value.user_placeholder = val.comment_user_name
  dataVal.value.parent_comment_id = val.id
  dataVal.value.top_comment_id = val.top_comment_id
  dataVal.value.article_user_id = val.article_user_id
  val.comment_level == 2
    ? (dataVal.value.second_comment_id = val.id)
    : (dataVal.value.second_comment_id = val.second_comment_id)
  dataVal.value.comment_level = 3
  dataIndex.value = index
  replyData.value.id = val.id
  replyData.value.content = val.content
  if (commentatorRef.value) {
    commentatorRef.value.handleSwitch()
  }
}
// 时间戳转换
const changeFormdate = computed(() => {
  return time => {
    let date = new Date(time)
    return formatDate(date, 'yyyy-m-d')
  }
})
// 回复提交接口
function submitForm() {
  if (dataVal.value.comment_content) {
    loading.value = true
    requests
      .checkSensitiveWord({
        comment_content: dataVal.value.comment_content.replace(/<[^>]+>/g, '')
      })
      .then(res => {
        // 校验
        if (res.code == 0) {
          // 存在敏感词
          if (res.data.result.length) {
            // 提取敏感词数组
            let detectResult = res.data.result
            let arr = []
            for (let i = 0; i < detectResult.length; i++) {
              arr.push(detectResult[i].content)
            }
            const sensitiveText = arr.toString()
            sensitive.value = sensitiveText
            // 如果有敏感词
            ElMessageBox.confirm(
              `发现<b style="color:red;">${sensitiveText}</b>为敏感词，是否继续？`,
              '提示',
              {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'Warning',
                dangerouslyUseHTMLString: true,
                center: true
              }
            )
              .then(() => {
                // 忽略敏感词 继续提交
                submitInit()
                sensitive.value = ''
              })
              .catch(() => {
                ElMessage({
                  type: 'info',
                  message: '已取消！'
                })
                loading.value = false
              })
          } else {
            // 没有敏感词
            console.log('没有敏感词')
            submitInit()
          }
        } else {
          ElMessage({
            type: 'error',
            message: '敏感词校验接口失败！'
          })
        }
      })
      .catch(() => {
        loading.value = false
      })
  } else {
    loading.value = false
    ElMessage({
      type: 'error',
      message: '评论内容不能为空'
    })
  }
}
// 去除html标签 A除外
function removeTagsExceptA(html) {
  // 删除除了a标签以外的所有HTML标签
  if (weditorRef.value.editorRef.getText().length > 0) {
    // 匹配所有<p>标签对
    const pTagRegex = /<p[^>]*>[^]*?<\/p>/gi
    const pTags = html.match(pTagRegex) || []
    // 如果只有一个<p>标签对，删除所有<p>标签对
    if (pTags.length === 1) {
      return html.replace(pTagRegex, match => match.replace(/<\/?p[^>]*>/g, ''))
    }
    // 正则表达式匹配<p>和<a>标签对及其内容
    const regex = /<(p|a)(\s[^>]*)?>[^]*?<\/\1>|<(\/?)(p|a)(\s[^>]*)?>/gi
    let result = html.replace(regex, (match, p1, p2, p3, p4, p5) => {
      // 如果是<p>或<a>标签，保留
      return match
    })
    // 删除其他所有标签
    result = result.replace(/<\/?(?!p|a)\b[^>]*>/gi, '')
    return result
  }
}
function submitInit() {
  // 是否收藏马甲号
  if (myCommentFlag.value) {
    dataVal.value.comment_user_id = myCollectionRef.value.commentInfo.comment_user_id
    dataVal.value.comment_user_name = myCollectionRef.value.commentInfo.comment_user_name
    dataVal.value.comment_user_portrait = myCollectionRef.value.commentInfo.comment_user_portrait
    dataVal.value.comment_user_location = myCollectionRef.value.commentInfo.comment_user_location
  } else {
    dataVal.value.comment_user_id = commentatorRef.value.commentInfo.comment_user_id
    dataVal.value.comment_user_name = commentatorRef.value.commentInfo.comment_user_name
    dataVal.value.comment_user_portrait = commentatorRef.value.commentInfo.comment_user_portrait
    dataVal.value.comment_user_location = commentatorRef.value.commentInfo.comment_user_location
  }
  // 去除内容a标签以外的html标签
  dataVal.value.comment_content = removeTagsExceptA(dataVal.value.comment_content)
  // 评论时间
  dataVal.value.comment_at = commentTimeRef.value.comment_at
  dataVal.value.task_type = commentTimeRef.value.taskType
  requests
    .vest_comment_reply(dataVal.value)
    .then(res => {
      if (res.code == 0) {
        setTimeout(() => {
          countNum()
          floor_list()
        }, 500)
        // 清除
        innerDrawer.value = false
        loading.value = false
        clear()
        ElMessage({
          message: '回复成功',
          type: 'success'
        })
      }
    })
    .catch(e => {
      loading.value = false
      console.log(e)
    })
}
function floor_list() {
  let article_id = dataVal.value.article_id
  // 添加loading状态
  loading.value = true
  const innderTimeStamp = timeStamp.value
  console.log('floor_list',innderTimeStamp)
  // 使用Promise.all同时请求两个接口
  Promise.all([
    requests.floor_list({ article_id, sort_number: 0, size: 1000 }),
    requests.hot_comments({ article_id: dataVal.value.article_id })
  ]).then(([floorRes, hotRes]) => {
    // 检查article_id是否已改变
    if (innderTimeStamp !== timeStamp.value) {
      loading.value = false
      return
    }

    if (floorRes.code === 0) {
      comment_level.value = floorRes.data.dtos
    }

    if (hotRes.code === 0) {
      let hotData = hotRes.data.dtos.map(item => ({
        ...item,
        hotCommentsFlag: true
      }))

      comment_level.value = [...hotData, ...comment_level.value]

      // 回复后找到对应回复 展开
      let as = comment_level.value[dataIndex.value]
      if (as && as.sub_count > 1 && !hotFlag.value) {
        expansion(
          as.id,
          as.sub_comment.id,
          as.sort_number,
          as.sub_comment.sort_number,
          as.channel_article_id,
          1000,
          dataIndex.value
        )
      }
    }
  }).catch(e => {
    console.log(e)
  }).finally(() => {
    loading.value = false
  })
}
defineExpose({
  moreNullData,
  drawer,
  dataVal,
  comment_level
})
</script>
<style>
.handlePa .w-e-text-placeholder {
  top: 15px !important;
}
.comment a:-webkit-any-link {
  color: #0b82fd;
  text-decoration: none;
}
.text a:-webkit-any-link {
  color: #0b82fd;
  text-decoration: none;
}
.text p {
  line-height: 16px !important;
  margin-top: 8px;
}
</style>
<style lang="scss" scoped>
.expression {
  display: block;
  margin-top: 20px;
  width: 60px;
  height: 60px;
  overflow: hidden;
  object-fit: cover;
  margin-bottom: 20px;
}

.icon2 {
  z-index: 999;
  margin-left: 97%;
  margin-top: 10px;
}

.line {
  height: 35px;
  line-height: 35px;
  position: relative;
  margin-bottom: 15px;
  border-radius: 2px;

  i {
    position: absolute;
    left: 0;
    top: 10px;
    width: 5px;
    height: 16px;
    background-color: #46a5e2;
  }

  span {
    font-size: 14px;
    position: absolute;
    left: 15px;
  }
}

.header {
  padding: 20px 30px;
  background: #f8f8f8;
  margin-bottom: 35px;
  position: relative;
  overflow: hidden;
  clear: both;

  img {
    width: 107px;
    height: 80px;
    object-fit: cover;
    flex: 1;
    float: left;
    margin-right: 20px;
    border-radius: 3px;
  }

  .title {
    width: 550px;
    float: left;
    height: 30px;
    font-size: 14px;
    margin-top: 3px;
    margin-bottom: 25px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    a {
      color: #333;
      text-decoration: none;
    }
  }

  .info {
    width: 430px;
    font-size: 12px;
    float: left;

    i {
      font-style: normal;
      padding: 2px 5px;
      text-align: center;
      background-color: #409eff;
      color: #fff;
    }

    span {
      margin-left: 5px;
      color: #333;
    }
  }
}

.ifrBox {
  display: block;
  margin: 15px 0;
}

.hotComment,
.comment {
  margin-bottom: 20px;
  height: calc(100vh - 300px);
  padding-left: 20px;
  overflow-y: auto;
  .level_1 {
    font-size: 14px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 30px;
    border-bottom: 1px dashed #dedede;

    .name {
      font-weight: 500;
      height: 24px;
      line-height: 24px;
      margin-bottom: 15px;
      color: #666;

      .userImg {
        display: inline-block;
        vertical-align: middle;
        width: 25px;
        height: 25px;
        border-radius: 100%;
        margin-right: 5px;
        object-fit: cover;
      }
    }

    .time {
      width: 50%;
      float: left;
      font-size: 12px;
      color: #acacac;
      height: 20px;
      margin-bottom: 10px;
    }

    .cont {
      color: #333;
      line-height: 21px;
      margin-bottom: 10px;
      .icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url('@/assets/images/hotIcon.png') no-repeat;
        background-size: 20px auto;
        margin-left: 7px;
        vertical-align: text-bottom;
      }

      i {
        display: inline-block;
        width: 36px;
        height: 18px;
        background: #ff3030;
        border-radius: 2px;
        line-height: 18px;
        font-style: normal;
        color: #fff;
        text-align: center;
        font-size: 12px;
        margin-right: 5px;
      }
      a {
        color: #0b82fd;
      }
    }

    .edit {
      float: right;
      width: 50%;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      text-align: right;

      .icon {
        margin-right: 3px;
        margin-top: -3px;
        vertical-align: middle;
      }
      .reply {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: url('@/assets/images/commt.png') no-repeat;
        background-size: 14px auto;
        vertical-align: middle;
        margin-right: 5px;
        margin-top: -1px;
      }
      .likeIcon {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: url('@/assets/images/like.png') no-repeat;
        background-size: 14px auto;
        vertical-align: middle;
        margin-right: 5px;
        margin-top: -2px;
      }

      .hotCommentIcon {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: url('@/assets/images/hotCommt.png') no-repeat;
        background-size: 14px auto;
        vertical-align: middle;
        margin-right: 5px;
        margin-top: -2px;
        &:hover {
          background: url('@/assets/images/hotCommt_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }

      .delComment {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: url('@/assets/images/delDef.png') no-repeat;
        background-size: 14px auto;
        vertical-align: middle;
        margin-right: 5px;
        margin-top: -3px;
      }

      .like {
        height: 14px;
        vertical-align: middle;
        margin-right: 25px;
        &:hover .likeIcon {
          background: url('@/assets/images/like_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }

      .cheat {
        height: 14px;
        vertical-align: middle;
        margin-right: 20px;
        &:hover .likeIcon {
          background: url('@/assets/images/like_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }
      .heat {
        height: 14px;
        vertical-align: middle;
        margin-right: 20px;
        &:hover .hotCommentIcon {
          background: url('@/assets/images/hotCommt_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }
      .delete {
        height: 14px;
        vertical-align: middle;
        &:hover .delComment {
          background: url('@/assets/images/delDef_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }

      .font {
        margin-right: 20px;
        &:hover .reply {
          background: url('@/assets/images/commt_hover.png') no-repeat;
          background-size: 14px auto;
        }
      }
      span {
        &:hover {
          color: #0b82fd;
        }
      }
    }

    .level_2 {
      background: #f7fbff;
      border-radius: 6px;
      clear: both;
      font-size: 14px;
      color: #333;
      padding: 10px;
      overflow: hidden;

      .name {
        font-weight: 500;
        height: 24px;
        margin-bottom: 15px;

        .arrow {
          display: inline-block;
          width: 8px;
          height: 12px;
          background: url('@/assets/images/arrowRight.png') no-repeat;
          background-size: 8px auto;
          margin: -3px 5px 0 5px;
          vertical-align: middle;
        }
      }

      .time {
        font-size: 12px;
        color: #acacac;
        height: 20px;
        margin-bottom: 10px;
      }

      .cont {
        color: #333;
        line-height: 22px;
        margin-bottom: 15px;
        word-wrap: break-word;
        i {
          display: inline-block;
          width: 36px;
          height: 18px;
          background: #ff3030;
          border-radius: 2px;
          line-height: 18px;
          font-style: normal;
          color: #fff;
          text-align: center;
          font-size: 12px;
          margin-right: 5px;
        }
        a {
          color: #0b82fd;
        }
      }

      .edit {
        font-size: 12px;
        color: #666;
        cursor: pointer;

        .icon {
          margin-right: 3px;
        }

        .likeIcon {
          width: 14px;
          height: 14px;
          display: inline-block;
          background: url('@/assets/images/like.png') no-repeat;
          background-size: 14px auto;
          vertical-align: middle;
          margin-right: 5px;
          margin-top: -2px;
        }

        .like {
          height: 14px;
          vertical-align: middle;
        }

        .font {
          margin-right: 20px;
          &:hover {
            color: #0b82fd;
          }
        }
      }
    }
  }

  .expansion,
  .putAway {
    font-size: 12px;
    color: #0b82fd;
    margin-top: 15px;
    cursor: pointer;

    span {
      display: block;
      width: 30px;
      height: 1px;
      background-color: #0b82fd;
      float: left;
      margin-top: 8px;
      margin-right: 10px;
    }
  }

  .putAway {
    // margin-left: 30px;
  }
}

.innerDrawer {
  font-size: 14px;
}

.more_null {
  text-align: center;
  font-size: 12px;
  color: #acacac;
}

p {
  margin: 0;
}

.reply {
  .endText {
    width: 100%;
    text-align: left;
    color: red;
  }

  .icon {
    z-index: 999;
    margin-left: 97%;
    margin-top: 10px;
  }
}
.emoji {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  margin-right: 5px;
  cursor: pointer;

  .emojiIcon {
    width: 15px;
    height: 15px;
    background: url('@/assets/images/emoji.png') no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 7px;
    margin-right: 5px;
  }
}
</style>
