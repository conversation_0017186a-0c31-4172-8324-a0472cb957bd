<template>
  <div class="head-box">
    <div class="content-row">
      <div class="left">
        <el-button v-auth="'comment_operation_task:list'" type="primary" @click="open"
          >全部评论任务</el-button
        >
      </div>
      <div class="right">
        <!-- 圈子 -->
        <el-cascader
          v-model="circleValue"
          :options="circleList"
          :show-all-levels="false"
          clearable
          collapse-tags
          :props="optionsProps2"
          placeholder="请选择圈子"
          style="margin-right: 10px; width: 180px"
          @change="changeCircle"
        />
        <!-- 全部频道 筛选-->
        <el-cascader
          v-model="cascadeValue"
          :options="options"
          :show-all-levels="false"
          clearable
          collapse-tags
          :props="optionsProps"
          placeholder="全部频道"
          @change="change"
        />
        <el-input
          v-model="searchData.searchword"
          placeholder="请输入关键词"
          style="margin-right: 10px"
          class="input-with-select"
          clearable
          @keyup.enter="handSearch"
        >
          <template #prepend>
            <el-select
              v-model="searchData.comment_search_type"
              placeholder="请选择"
              style="width: 100px"
              @change="typeChange"
            >
              <el-option
                v-for="item of commentSearchType"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              >
              </el-option>
            </el-select>
          </template>
          <template #append>
            <el-button
              type="primary"
              :icon="Search"
              style="display: flex; align-items: center"
              @click="handSearch"
            >
              搜索
            </el-button>
          </template>
        </el-input>
        <el-divider direction="vertical" />
        <div class="right-item" @click="handleColumnSetting">
          <img :src="SettingIcon" class="icon-setting-column" />
          <span>列设置</span>
        </div>
      </div>
    </div>
    <ColumnSettingsComponent
      v-if="columnSettingVisible"
      :type="'comment_opera'"
      @close="handleCloseColumnSetting"
    />
  </div>
</template>
<script setup>
const route = useRoute(),
  router = useRouter()
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { defineEmits, defineExpose, onMounted, ref } from 'vue'
import ColumnSettingsComponent from '@/components/ColumnSettings/ColumnsettingsComponent.vue'
import SettingIcon from '@/assets/images/settings_column_group.png'

const columnSettingVisible = ref(false)
// 组件传参声明方式
const props = defineProps({
  selectData: {
    type: Array
  }
})
// 组件接收事件
const emit = defineEmits(['search', 'openTask'])
// 圈子类型
const circleList = ref([])
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name'
})
// 稿件标题下拉的实时数据
const title_select_Data = ref([])
// 稿件标题时的关键字
const title_search_data = ref(null)
// 搜索类型
const commentSearchType = ref([
  {
    key: 'article_title',
    label: '稿件标题'
  },
  {
    key: 'article_id',
    label: '稿件ID'
  }
])
// 圈子
const circleValue = ref(null)
const optionsProps2 = ref({
  value: 'id',
  label: 'name',
  multiple: true
})
const loading = ref(false)
// 搜索参数
const searchData = ref({
  circle_id: '',
  channel_id: '',
  comment_search_type: 'article_title',
  searchword: ''
})
onMounted(() => {
  requests.listCategoryTree().then(res => {
    let arr = res.data.category_list
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1)
      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1)
          } else {
            for (let a = 0; a < arr[i].children[k].children.length; a++) {
              if (arr[i].children[k].children[a].permission == 0) {
                arr[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr
    })
  })
  requests.getcircleList({}).then(res => {
    circleList.value = res.data.circle_list
  })
  // 获取地址栏内ID信息并回填到数据
  if (route.query.id) {
    searchData.value.comment_search_type = 'article_id'
    searchData.value.searchword = route.query.id
  }
  // 初始
  // handSearch();
})
const typeChange = () => {
  searchData.value.searchword = ''
}

const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    console.log(cascadeValue.value)
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.channel_id = data[data.length - 1]
  } else {
    searchData.value.channel_id = ''
  }
  handSearch()
}
// 圈子选中调用
function changeCircle() {
  if (circleValue.value) {
    searchData.value.circle_id = circleValue.value.join(',')
  } else {
    searchData.value.circle_id = ''
  }
  handSearch()
}

const open = () => {
  emit('openTask', true)
}
// 点击搜索
const handSearch = () => {
  let searchObj = {}
  searchObj.channel_id = searchData.value.channel_id
  searchObj[searchData.value.comment_search_type] = searchData.value.searchword
  searchObj.circle_id = searchData.value.circle_id
  console.log(searchObj)
  emit('search', searchObj)
}

const handleColumnSetting = () => {
  columnSettingVisible.value = true
}

const handleCloseColumnSetting = () => {
  columnSettingVisible.value = false
}

defineExpose({
  searchData,
  title_search_data
})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}
::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

.head-box {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  margin-bottom: 10px;

  .settings-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    margin-bottom: 2px;
    .settings-btn {
      cursor: pointer;
      margin-right: 10px;
      font-size: 14px;
      margin-bottom: 2px;
    }
  }
  .content-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .right {
      display: flex;
      align-items: center;
      & > .el-select {
        width: 150px;
        margin-right: 10px;
      }
      .el-input-group {
        width: 400px;
      }
    }
  }
  .right-item {
    font-size: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .icon-setting {
      width: 13px;
      height: 13px;
      margin-right: 3px;
    }

    .icon-setting-column {
      width: 13px;
      height: 13px;
      margin-right: 3px;
    }
  }
}
</style>
