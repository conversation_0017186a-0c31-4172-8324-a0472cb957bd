<template>
  <div class="comment">
    <el-drawer
      v-model="drawer" direction="rtl" :title="isEdit ? '编辑评论任务' : '创建评论任务'" :size="800"
      :before-close="handleClose"
    >
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" class="ruleForm" status-icon>
        <div class="header">
          <img v-show="ruleForm.list_pics" :src="ruleForm.list_pics" />
          <div class="title">
            <a :href="ruleForm.article_url" target="_blank">{{ ruleForm.article_title }}</a>
          </div>
          <div class="info">
            <el-button type="primary" bg text="primary" size="small">
              {{ ruleForm.channel_name }}
            </el-button> <span>{{ ruleForm.article_published_at }}</span>
          </div>
        </div>

        <div v-show="!isEdit" class="commentMore">
          <span :class="!batchTasks ? 'act' : ''" @click="handleCommentMore(false)">单条评论</span>
          <span :class="batchTasks ? 'act' : ''" @click="handleCommentMore(true); batchList = []">批量评论</span>
        </div>
        <div class="line"><i></i><span>评论任务</span></div>
        <el-dialog
          v-model="timeChange" :title="'评论时间修改 当前第' + batchIndex + '条'" width="40%" align-center
          :show-close="false"
        >
          <el-date-picker
            v-model="changeComment_at" type="datetime" placeholder="请选择日期" :default-time="defaultTime"
            value-format="YYYY-MM-DD HH:mm:ss" :shortcuts="shortcuts" :disabled-date="disabledDate"
            style="margin-right:10px"
          />
          <template #footer>
            <el-form-item style="float:right">
              <el-button style="margin-right:10px" @click="timeChange = false">
                取消
              </el-button>
              <el-button type="primary" @click="handleBatchTime">
                确认
              </el-button>
            </el-form-item>
          </template>
        </el-dialog>
        <el-table
          v-show="batchData.length > 0 && !isEdit && batchTasks" :data="batchData" style="margin-bottom: 30px;"
          stripe max-height="288"
        >
          <el-table-column label="序号" width="80">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="评论内容">
            <template #default="scope">
              <div class="comment-content">
                <div class="comment-text" v-html="scope.row.comment_content"></div>
                <img v-show="scope.row.expression_url" :src="scope.row.expression_url" class="comment-image" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评论时间" width="110" align="center">
            <template #default="scope">
              <span style="color:#0B82FD; cursor: pointer;" @click="handleChangeTime(scope.row, scope.$index)">{{
                scope.row.comment_at }}</span>
            </template>
          </el-table-column>
          <el-table-column property="comment_user_name" label="评论人" width="110" align="center" />
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <span
                style="color:#0B82FD; cursor: pointer;"
                @click="handleCommentDelete(scope.row, scope.$index)"
              >删除</span>
            </template>
          </el-table-column>
        </el-table>
        <div v-show="batchData.length < 1 && batchTasks" class="noComment">目前还没有评论任务~ 赶紧编辑吧~</div>
        <el-table
          v-show="gridData.length > 0 && !isEdit && !batchTasks" :data="gridData" style="margin-bottom: 30px;"
          stripe max-height="288"
        >
          <el-table-column
            prop="comment_content"
            label="评论内容"
            width="460"
          >
            <template #default="{ row }">
              <div v-html="row.comment_content"></div>
            </template>
          </el-table-column>
          <el-table-column property="comment_at" label="评论时间" width="180" align="center" />
          <el-table-column label="点击刷新" align="center" width="100">
            <template #default="scope">
              <span
                v-show="scope.row.task_state == 1" class="stateStyle" style="color:#67C23A;"
                @click="handleStateUpdeta(scope.row, scope.$index)"
              ><el-icon>
                <SuccessFilled />
              </el-icon> <b>已完成</b> </span>
              <span
                v-show="scope.row.task_state == 2" class="stateStyle" style="color:#409EFF;"
                @click="handleStateUpdeta(scope.row, scope.$index)"
              ><el-icon>
                <InfoFilled />
              </el-icon> <b>执行中</b></span>
              <span
                v-show="scope.row.task_state == 3" class="stateStyle" style="color:#E6A23C"
                @click="handleStateUpdeta(scope.row, scope.$index)"
              ><el-icon>
                <CircleCloseFilled />
              </el-icon> <b>已停止</b></span>
              <span
                v-show="scope.row.task_state == 4" class="stateStyle" style="color:#F56C6C"
                @click="handleStateUpdeta(scope.row, scope.$index)"
              ><el-icon>
                <WarningFilled />
              </el-icon> <b>异常</b></span>
            </template>
          </el-table-column>
        </el-table>
        <div v-show="gridData.length < 1 && !batchTasks" class="noComment">目前还没有评论任务~ 赶紧编辑吧~</div>
        <div class="line"><i></i><span>评论内容</span></div>
        <el-form-item prop="comment_content" style="margin-bottom: 20px; width:100%">
          <!-- <el-input v-model="ruleForm.comment_content" type="textarea" maxlength="255" show-word-limit :rows="5" /> -->
          <WeditorV5 ref="weditorRef" :comment_content="ruleForm.comment_content" @contentVal="handleContentVal"></WeditorV5>
          <div v-show="sensitive.length > 0" style="width: 100%; text-align:left">
            <span style="color:red;">发现敏感词：{{ sensitive }}</span>
          </div>
          <div v-if="!module.moduleBox" style="margin-top: 15px; width:100%">
            <div
              v-show="!isEdit && !ruleForm.expression_url" v-auth="'comment_operation:module'" class="module"
              @click="showModel"
            >
              <span class="moduleIcon"></span>组件
            </div>
            <div class="emoji" @click="emojiClick">
              <span class="emojiIcon"></span>emoji
            </div>
            <picManagement v-if="!ruleForm.activity_url2" ref="picmanagementRef" @succ="handlePicManagement" @onOpen="showEmojis = false">
            </picManagement>
            <div v-auth="'comment_operation:ai_generate'" class="AI">
              <div class="aiAction" @click="getComments">
                <span class="aiIcon"></span>AI辅助撰写
              </div>
            </div>
          </div>
          <!-- emoji表情 -->
          <Picker
            v-show="showEmojis" :data="emojiIndex" set="apple" :i18n="I18n" :show-preview="false"
            :show-search="false" :emoji-tooltip="false" :show-skin-tones="false" :auto-focus="true" @select="showEmoji"
          />
          <!-- 组件预览 -->
          <div v-if="module.moduleBox" class="moduleBox">
            <el-icon class="icon1" @click="handleMouduleDel">
              <Delete />
            </el-icon>
            <iframe :src="ruleForm.activity_url2" frameborder="0" :height="module.ifHeight" scrolling="no"></iframe>
          </div>
        </el-form-item>
        <div v-show="!isEdit">
          <div class="commentBox">
            <div class="line"><i></i><span>评论人信息</span> </div>
            <div class="tabLine">
              <span v-show="commentSwitch" class="sel" @click="commentSwitch = false"><i></i>我的收藏</span>
              <span v-show="!commentSwitch" class="def" @click="commentSwitch = true"><i></i>随机生成</span>
            </div>
          </div>

          <div v-show="commentSwitch">
            <Commentator ref="commentatorRef" @selectCommentator="handleCommentator"></Commentator>
          </div>
          <div v-show="!commentSwitch">
            <myCollection
              ref="myCollectionRef" @selectMyCollection="handleMyCollection"
              @delMyCollection="handleDelMyCollection"
            ></myCollection>
          </div>
        </div>
        <div v-if="!batchTasks">
          <div class="line"><i></i><span>评论时间</span></div>
          <el-form-item prop="comment_at">
            <el-radio-group v-model="ruleForm.task_type" style="margin-top:-5px" @change="handleRadio">
              <el-radio :label="1" size="large">
                实时评论
              </el-radio>
              <el-radio :label="2" size="large">
                定时评论
              </el-radio>
            </el-radio-group>
            <div v-show="ruleForm.task_type == 2" style="margin-left:20px">
              <el-date-picker
                v-model="ruleForm.comment_at" type="datetime" placeholder="请选择定时日期"
                :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate" style="width:200px;margin-right:10px"
              />
            </div>
          </el-form-item>
          <div v-show="ruleForm.task_type == 2" class="timeList">
            <el-button v-for="(item,index) of user_defined_time" :key="index" plain size="small" @click="setTime(item)">
              {{ item }}分钟后
            </el-button>
            <el-button plain size="small" @click="selectTime()">
              +设置
            </el-button>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button v-show="!batchTasks" :loading="loading" type="primary" @click="submitForm('ruleFormRef')">
            保存并提交
          </el-button>
          <el-button
            v-show="batchTasks" :loading="taskLoading"
            style="background: #F3F9FF; color: #0B82FD; border:none" @click="submitForm('ruleFormRef')"
          >
            保存任务
          </el-button>
          <el-button v-show="batchTasks" :loading="loading" type="primary" @click="hanldeBatchTasks">
            提交
          </el-button>
        </div>
      </template>
      <el-dialog
        v-model="module.moduleShow" title="互动组件" width="960" align-center
        :before-close="moduleCancel"
      >
      <interact v-if="module.moduleShow" @setModule="handleModule"></interact>
      </el-dialog>
      <el-dialog v-model="timeSet.TimeShow" title="时间设置（最多选择12项）" width="480px" align-center :before-close="handleColseSetTime">
        <div v-for="item of timeSet.timeList" :key="item.id" :class="item.flag ? 'smallHover' :'small'" @click="handleSelectTime(item)">
          {{ item.value }}分钟后
        </div>

        <template #footer>
          <el-form-item style="float:right;margin-top:30px">
            <el-button style="margin-right:10px" @click="handleColseSetTime">
              取消
            </el-button>
            <el-button type="primary" @click="handleClickSetTime">
              确认
            </el-button>
          </el-form-item>
        </template>
      </el-dialog>
      <AI :id="ruleForm.article_id" ref="ai" @setComment="choiseComment" />
    </el-drawer>
  </div>
</template>
<script setup name="add">
import AI from '../AI/index.vue'
import { Picker, EmojiIndex } from 'emoj-vue-chao/src'
import picManagement from './picManagement.vue'
import Commentator from './comment/commentator.vue'
import myCollection from './comment/myCollection.vue'
import WeditorV5 from '@/components/weditor/v5.vue'
import interact from '@/components/interact/index.vue'
import data from '@/assets/google.json'
import 'emoj-vue-chao/css/emoji-mart.css'
import { requests } from '@/api/business/commentOpera'
import { ref, watch, nextTick, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const batchIndex = ref('')
const changeComment_at = ref('')
const timeChange = ref(false)
const batchData = ref([])
const batchList = ref([])
const batchTasks = ref(false)
const emit = defineEmits(['success'])
const ai = ref(null)
const commentSwitch = ref(true)
const commentatorRef = ref()
const myCollectionRef = ref()
const module = ref({
  width: '',
  height: '',
  url: '',
  moduleShow: false,
  moduleBox: false,
  ifHeight: ''
})
const shortcuts = [
  {
    text: '5分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 5)
      return date
    }
  },
  {
    text: '10分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 10)
      return date
    }
  },

  {
    text: '20分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 20)
      return date
    }
  },
  {
    text: '25分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 25)
      return date
    }
  },
  {
    text: '40分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 40)
      return date
    }
  },
  {
    text: '50分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 50)
      return date
    }
  },
  {
    text: '70分钟后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * 70)
      return date
    }
  }
]
const sensitive = ref('')
const gridData = ref([])
const ruleForm = ref({
  article_title: '',
  article_id: '',
  article_url: '',
  article_published_at: '',
  channel_id: '',
  channel_name: '',
  comment_content: '',
  comment_user_id: '',
  comment_user_name: '',
  list_pics: '',
  comment_user_portrait: '',
  comment_at: '',
  task_type: 1,
  id: '',
  article_user_id: '',
  activity_url2: '',
  activity_ratio_width: '',
  activity_ratio_height: '',
  expression_id: '',
  expression_url: '',
  component_type: '',
  project_id: ''
})
const editForm = ref({
  article_title: '',
  article_id: '',
  article_url: '',
  article_published_at: '',
  channel_id: '',
  channel_name: '',
  comment_content: '',
  comment_user_id: '',
  comment_user_name: '',
  list_pics: '',
  comment_user_portrait: '',
  comment_at: '',
  task_type: 1,
  id: '',
  article_user_id: '',
  activity_url2: '',
  activity_ratio_width: '',
  activity_ratio_height: ''
})
const I18n = ref({
  search: 'Search',
  notfound: 'No Emoji Found',
  categories: {
    recent: '常用',
    people: '人物'
  }
})
const emojiIndex = new EmojiIndex(data)
const showEmojis = ref(false)
const rules = ref({
  comment_content: [
    { required: true, message: '请输入评论内容', trigger: 'blur' | 'change' }
  ],
  comment_at: [
    { required: true, message: '请输入评论时间', trigger: 'blur' }
  ]
})
const loading = ref(false)
const taskLoading = ref(false)
const drawer = ref(false)
const isEdit = ref(false)
const ruleFormRef = ref(null)
const picmanagementRef = ref(null)
const user_defined_time = ref([])
const weditorRef = ref(null)
// 组件列表
const moduleOptions = ref(
  [
    {
      label: '观点PK组件',
      value: 'dual_options_poll'
    },
    {
      label: '多选投票组件',
      value: 'more_options_poll'
    }
  ]
)
// 项目列表
const projectOptions = ref([])
const timeSet = ref({
  TimeShow: false,
  timeList: [
    {
      id: 1,
      value: 5,
      flag: false
    },
    {
      id: 2,
      value: 10,
      flag: false
    },
    {
      id: 3,
      value: 15,
      flag: false
    },
    {
      id: 4,
      value: 20,
      flag: false
    },
    {
      id: 5,
      value: 25,
      flag: false
    },
    {
      id: 6,
      value: 30,
      flag: false
    },
    {
      id: 7,
      value: 35,
      flag: false
    },
    {
      id: 8,
      value: 40,
      flag: false
    },
    {
      id: 9,
      value: 45,
      flag: false
    },
    {
      id: 10,
      value: 50,
      flag: false
    },
    {
      id: 11,
      value: 55,
      flag: false
    },
    {
      id: 12,
      value: 60,
      flag: false
    },
    {
      id: 13,
      value: 65,
      flag: false
    },
    {
      id: 14,
      value: 70,
      flag: false
    },
    {
      id: 15,
      value: 75,
      flag: false
    },
    {
      id: 16,
      value: 80,
      flag: false
    },
    {
      id: 17,
      value: 90,
      flag: false
    },
    {
      id: 18,
      value: 100,
      flag: false
    },
    {
      id: 19,
      value: 110,
      flag: false
    },
    {
      id: 20,
      value: 120,
      flag: false
    },
    {
      id: 21,
      value: 150,
      flag: false
    },
    {
      id: 22,
      value: 180,
      flag: false
    },
    {
      id: 23,
      value: 210,
      flag: false
    },
    {
      id: 24,
      value: 240,
      flag: false
    }
  ],
  pushList: []
})
watch(drawer, (newValue, oldValue) => {
  if (!newValue) {
    ruleFormRef.value.resetFields()
    isEdit.value = false
    commentatorRef.value.handleSwitch()
    commentSwitch.value = true
  } else {
    nextTick(() => {
      if (isEdit.value) {
        weditorRef.value.changeText = true
        ruleForm.value = editForm.value
        // 时间戳转化
        const time = new Date(ruleForm.value.comment_at)
        ruleForm.value.comment_at = nowDate(time)
      }
    })
  }
})
// 快捷时间回显
const selectTime = (() => {
  timeSet.value.TimeShow = true
  user_defined_time.value.forEach(v => {
    timeSet.value.timeList.forEach(item => {
      if (item.value === parseInt(v)) {
        item.flag = true
        timeSet.value.pushList.push(item.value)
      }
    })
  })
})
// 快捷时间设置
const handleSelectTime = val => {
  val.flag = !val.flag
  if (val.flag) {
    const index = timeSet.value.pushList.indexOf(val.value)
    if (index !== -1) {
      timeSet.value.pushList.splice(index, 1)
    } else {
      timeSet.value.pushList.push(val.value)
    }
  } else {
    const index = timeSet.value.pushList.indexOf(val.value)
    if (index !== -1) {
      timeSet.value.pushList.splice(index, 1)
    }
  }
  console.log(timeSet.value.pushList, 'timeSet.value.pushList')
}

const handleClickSetTime = () => {
  if (timeSet.value.pushList.length > 12) {
    ElMessage({
      message: '最多选择12个时间点',
      type: 'error'
    })
  } else if (timeSet.value.pushList.length == 0) {
    ElMessage({
      message: '最少选择1个时间点',
      type: 'error'
    })
  } else {
    requests.create_or_update({ times: timeSet.value.pushList.sort((a, b) => a - b).join(',') }).then(res => {
      if (res.code == 0) {
        ElMessage({
          message: '时间设置成功',
          type: 'success'
        })
        handleColseSetTime()
        setDefaultTime()
      }
    })
  }
}
const handleColseSetTime = () => {
  timeSet.value.TimeShow = false
  timeSet.value.pushList = []
  timeSet.value.timeList.forEach(v => {
    return v.flag = false
  })
}
// 读取自定义时间
const setDefaultTime = () => {
  requests.get_time_array().then(res => {
    if (res.code == 0) {
      if (res.data.user_defined_time.length > 0) {
        user_defined_time.value = res.data.user_defined_time
      }
    }
  })
}
// 富文本内容
const handleContentVal = val => {
  ruleForm.value.comment_content = val
}
// 单条and批量
const handleCommentMore = flag => {

  if (batchTasks.value && batchData.value.length > 0) {
    ElMessageBox.confirm(
      '您当前有评论任务待执行！切换后，评论内容将清空。',
      '确认切换单条评论模式？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        batchTasks.value = flag
        batchData.value = []
        batchList.value = []
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消'
        })
      })
  } else {
    batchTasks.value = flag
  }

}
// 随机评论事件
const handleCommentator = () => {
  myCollectionRef.value.vestList.forEach(v => { v.isClick = false })
}
// 收藏评论点击事件
const handleMyCollection = () => {
  commentatorRef.value.vestList.forEach(v => { v.isClick = false })
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
}
// 收藏马甲号删除事件
const handleDelMyCollection = () => {
  myCollectionRef.value.commentInfo = {
    comment_user_id: '',
    comment_user_name: '',
    comment_user_portrait: '',
    comment_user_location: ''
  }
}
// 自动生成评论
function getComments () {
  if (!ai.value.showCard) {
    ai.value.show()
  } else {
    ai.value.hide()
  }
}
// 选择评论
function choiseComment (val) {
  weditorRef.value.changeText = true
  let content = removeTagsExceptA(ruleForm.value.comment_content.trim())
  if (content && content != '<br>') {
    ElMessageBox.confirm(
      '您当前有评论内容！替换后，原评论内容将清空。',
      '确定替换当前评论内容？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {

        ruleForm.value.comment_content = val
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消'
        })
      })
  } else {
    ruleForm.value.comment_content = val
  }
}
// 预设 本地传值
function handlePicManagement (url, id) {
  ruleForm.value.expression_id = id
  ruleForm.value.expression_url = url
}
// 选择组件类型
function componentTypeChage (val) {
  if (val) {
    getModelsList(val)
  }
}
// 获取组件列表
function getModelsList (val) {
  requests.getModelsList({ component_type: val }).then(res => {
    projectOptions.value = res.data.list
  })
}
// 快捷时间设置
function setTime (val) {
  const date = new Date()
  date.setTime(date.getTime() + (3600 * 1000 * 1) / 60 * val)
  let y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? ('0' + m) : m
  let d = date.getDate()
  d = d < 10 ? ('0' + d) : d
  let h = date.getHours()
  h = h < 10 ? ('0' + h) : h
  let M = date.getMinutes()
  M = M < 10 ? ('0' + M) : M
  let s = date.getSeconds()
  s = s < 10 ? ('0' + s) : s
  let dateTime = y + '-' + m + '-' + d + ' ' + h + ':' + M + ':' + s
  ruleForm.value.comment_at = dateTime
}
// 取消关联组件
function moduleCancel () {
  module.value.moduleShow = false
  ruleForm.value.project_id = ''
  ruleForm.value.component_type = ''
}
// 展示组件
function showModel () {
  module.value.moduleShow = true
  showEmojis.value = false
}
// 删除组件
function handleMouduleDel () {
  ElMessageBox.confirm(
    '是否删除当前组件，删除后不可恢复',
    '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'Warning',
      dangerouslyUseHTMLString: true,
      center: true
    }
  ).then(() => {
    module.value.moduleBox = false
    ruleForm.value.activity_url2 = ''
    ruleForm.value.project_id = ''
    ruleForm.value.component_type = ''
    ruleForm.value.comment_content = ''
  })
}
// 插入组件
function handleModule(data) {
  weditorRef.value.changeText = true
  ruleForm.value.activity_url2 = data.url
  ruleForm.value.comment_content = data.project_name
  ruleForm.value.project_id = data.project_id || data.id
  ruleForm.value.component_type = data.component_type
  module.value.moduleShow = false
  module.value.moduleBox = true
  if (data.component_type == 'more_options_poll') {
    module.value.ifHeight = data.ratio_height
  } else {
    module.value.ifHeight = 120
    ruleForm.value.component_type = 'dual_options_poll'
  }

}
// 打开emoji弹窗
function emojiClick() {
  showEmojis.value = !showEmojis.value
  picmanagementRef.value.presetData.imgBox = false
  picmanagementRef.value.showCustom = false
}
// 展示emoji
function showEmoji (emoji) {
  // ruleForm.value.comment_content += emoji.native
  weditorRef.value.editorRef.focus()
  weditorRef.value.editorRef.insertText(emoji.native)
}
// 切换实时评论
function handleRadio () {
  if (!isEdit.value) {
    if (ruleForm.value.task_type == 2) {
      ruleForm.value.comment_at = ''
      setDefaultTime()
    } else {
      ruleForm.value.comment_at
    }
  }
}
// 之前日期不能选
function disabledDate (time) {
  return time.getTime() < Date.now() - 8.64e7
}
// 关闭窗口
function handleClose () {
  var scrollBox = document.getElementsByClassName('el-drawer__body')[0]
  scrollBox.scrollTo(0, 0);
  ai.value.hide()
  ruleForm.value.task_type = 1
  commentatorRef.value.vestSearch.nick_name = ''
  commentatorRef.value.dhtVal = []
  commentatorRef.value.addressVal = ''
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
  myCollectionRef.value.vestList.forEach(v => { v.isClick = false })
  myCollectionRef.value.commentInfo = {
    comment_user_id: '',
    comment_user_name: '',
    comment_user_portrait: '',
    comment_user_location: ''
  }
  drawer.value = false
  sensitive.value = ''
  showEmojis.value = false
  module.value.moduleBox = false
  module.value.ifHeight = 120
  ruleForm.value.activity_url2 = ''
  ruleForm.value.component_type = ''
  ruleForm.value.project_id = ''
  if (picmanagementRef.value) {
    picmanagementRef.value.presetData = {
      activeTab: 'tab1',
      imgValue: '',
      imgBox: false,
      cover: [],
      selectId: '',
      imgList: []
    }
    picmanagementRef.value.showCustom = false
  }
  batchData.value = []
  batchList.value = []
  taskLoading.value = false
  loading.value = false
}
// 更新任务状态
function handleStateUpdeta (row, index) {
  let id = row.id
  requests.get_detail({ id }).then(res => {
    if (res.code == 0) {
      gridData.value[index].task_state = res.data.comment_task.task_state
      ElMessage({
        message: '更新任务状态成功',
        type: 'success'
      })
    } else {
      ElMessage({
        message: '更新任务状态失败',
        type: 'error'
      })
    }

  })
}

// 提交 检测敏感词
function submitForm (formName) {
  getNowTime()
  // 必填效验
  ruleFormRef.value.validate(valid => {
    if (valid && weditorRef.value.editorRef.getText().length > 0) {
      // loading 节流控制
      !batchTasks.value ? loading.value = true : taskLoading.value = true
      // 敏感词接口
      requests.checkSensitiveWord({
        comment_content: ruleForm.value.comment_content.replace(/<[^>]+>/g, '')
      }).then(res => {
        // 校验
        if (res.code == 0) {
          // 存在敏感词
          if (res.data.result.length) {
            // 提取敏感词数组
            let detectResult = res.data.result
            let arr = []
            for (let i = 0; i < detectResult.length; i++) {
              arr.push(detectResult[i].content)
            }
            const sensitiveText = arr.toString()
            sensitive.value = sensitiveText
            // 如果有敏感词
            ElMessageBox.confirm(
              `发现<b style="color:red;">${sensitiveText}</b>为敏感词，是否继续？`,
              '提示', {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'Warning',
                dangerouslyUseHTMLString: true,
                center: true
              }
            )
              .then(() => {
                // 忽略敏感词 继续提交
                submitInit()
                sensitive.value = ''

              })
              .catch(() => {
                taskLoading.value = false
                loading.value = false
                ElMessage({
                  type: 'info',
                  message: '已取消！'
                })
              })
          } else {
            // 没有敏感词
            console.log('没有敏感词')
            submitInit()
          }
        } else {
          taskLoading.value = false
          loading.value = false
          ElMessage({
            type: 'error',
            message: '敏感词校验接口失败！'
          })
        }
      })
        .catch(error => {
          taskLoading.value = false
          loading.value = false
        })

    } else {
      if (valid) {
        ElMessage({
          message: '请输入评论内容',
          type: 'error'
        })
        return false
      }

    }
  })
}

// 批量保存任务
const hanldeBatchTasks = () => {
  loading.value = true

  // 批量评论不为空
  if (batchData.value.length > 0) {
    batchList.value = batchList.value.map(item => {
      const cleanedContent = removeTagsExceptA(item.comment_content)
      return { ...item, comment_content: cleanedContent }
    })
    // 判断是否有空评论时间
    const isComment_at = batchData.value.every(item => { return item.comment_at != '请选择时间' })

    if (isComment_at) {
      requests.batch_commit({ batch_list: JSON.stringify(batchList.value) }).then(res => {
        if (res.code == 0) {
          loading.value = false
          ElMessage({
            message: '批量评论任务提交成功',
            type: 'success'
          })
          batchData.value = []
          batchList.value = []
        }
      }).catch(() => {
        loading.value = false
      })
    } else {
      loading.value = false
      ElMessage({
        message: '还有评论任务未选时间',
        type: 'error'
      })
    }
  } else {
    loading.value = false
    ElMessage({
      message: '暂无可提交评论任务',
      type: 'error'
    })
  }

}
// 批量评论 删除
const handleCommentDelete = (row, index) => {
  ElMessageBox.confirm(
    `确定删除第 ${index + 1} 条评论任务，删除后内容无法恢复。`,
    '删除',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      batchData.value.splice(index, 1)
      batchList.value.splice(index, 1)
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}
// 批量评论 时间修改
const handleChangeTime = (row, index) => {
  timeChange.value = true
  batchIndex.value = index + 1
  changeComment_at.value = row.comment_at
  console.log(row.comment_at, 'comment_at')

}
// 批量评论 时间修改确认
const handleBatchTime = () => {
  batchData.value[batchIndex.value - 1].comment_at = changeComment_at.value
  batchList.value[batchIndex.value - 1].comment_at = changeComment_at.value
  timeChange.value = false
  changeComment_at.value = ''

}
function nowDate (now) {
  let year = now.getFullYear() // 年份
  let month = now.getMonth() + 1 // 月份（0-11）
  let date = now.getDate() // 天数（1到31）
  let hour = now.getHours() // 小时数（0到23）
  let minute = now.getMinutes() // 分钟数（0到59）
  let second = now.getSeconds() // 秒数（0到59）
  month < 10 ? month = '0' + month : month
  date < 10 ? date = '0' + date : date
  hour < 10 ? hour = '0' + hour : hour
  minute < 10 ? minute = '0' + minute : minute
  second < 10 ? second = '0' + second : second
  return (
    year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
  )
}
function getNowTime () {
  // 效验评论时间
  if (ruleForm.value.task_type == 1) {
    const a = new Date().getTime() // 获取到当前时间戳
    const b = new Date(a)
    ruleForm.value.comment_at = nowDate(b)
  }
}
function submitInit () {

  // 马甲号插入
  if (!commentSwitch.value) {

    ruleForm.value.comment_user_id = myCollectionRef.value.commentInfo.comment_user_id
    ruleForm.value.comment_user_name = myCollectionRef.value.commentInfo.comment_user_name
    ruleForm.value.comment_user_portrait = myCollectionRef.value.commentInfo.comment_user_portrait
    ruleForm.value.comment_user_location = myCollectionRef.value.commentInfo.comment_user_location
  } else {
    ruleForm.value.comment_user_id = commentatorRef.value.commentInfo.comment_user_id
    ruleForm.value.comment_user_name = commentatorRef.value.commentInfo.comment_user_name
    ruleForm.value.comment_user_portrait = commentatorRef.value.commentInfo.comment_user_portrait
    ruleForm.value.comment_user_location = commentatorRef.value.commentInfo.comment_user_location
  }

  if (batchTasks.value) { // 判断是否批量任务
    // 判断引用组件是否被限制
    let useData = batchList.value.filter(v => {
      if (v.project_id) {
        return v.project_id === ruleForm.value.project_id
      }
    })
    if (useData.length) {
      ElMessage({
        message: '组件已被使用，请重新添加',
        type: 'error'
      })
      taskLoading.value = false
      return
    }
    if (ruleForm.value.comment_user_id && ruleForm.value.comment_content) { // 是否有评论人
      ElMessage({
        message: '添加任务成功',
        type: 'success'
      })
      pushData()
      commentatorRef.value.handleSwitch()
      taskLoading.value = false
    } else {
      taskLoading.value = false
      ElMessage({
        message: '请选择评论人',
        type: 'error'
      })
    }

  } else {
    if (ruleForm.value.comment_at && !isEdit.value) {
      ruleForm.value.comment_content = removeTagsExceptA(ruleForm.value.comment_content)
      // 新增
      requests.taskSave(ruleForm.value).then(res => {
        if (res.code == 0) {
          loading.value = false
          ruleForm.value.task_id = res.data.comment_task.id
          ElMessage({
            message: '添加任务成功',
            type: 'success'
          })
          pushData()
          commentatorRef.value.handleSwitch()
        }
      }).catch(() => {
        loading.value = false
      })
    } else {
      // 更新
      ruleForm.value.comment_content = removeTagsExceptA(ruleForm.value.comment_content)
      requests.taskUpdate(ruleForm.value).then(res => {
        if (res.code == 0) {
          loading.value = false
          ElMessage({
            message: '更新任务成功',
            type: 'success'
          })
          emit('success')
        }
      }).catch(() => {
        loading.value = false
      })
    }
  }

}
// 去除html标签 A除外
function removeTagsExceptA(html) {

  // 匹配所有<p>标签对
  const pTagRegex = /<p[^>]*>[^]*?<\/p>/gi
  const pTags = html.match(pTagRegex) || []
  // 如果只有一个<p>标签对，删除所有<p>标签对
  if (pTags.length === 1) {
    return html.replace(pTagRegex, match => match.replace(/<\/?p[^>]*>/g, ''))
  }
  // 正则表达式匹配<p>和<a>标签对及其内容
  const regex = /<(p|a)(\s[^>]*)?>[^]*?<\/\1>|<(\/?)(p|a)(\s[^>]*)?>/gi
  let result = html.replace(regex, (match, p1, p2, p3, p4, p5) => {
    // 如果是<p>或<a>标签，保留
    return match
  })
  // 删除其他所有标签
  result = result.replace(/<\/?(?!p|a)\b[^>]*>/gi, '')
  return result

}

// 成功后push到临时表单
function pushData () {
  const newValue = Object.assign({}, ruleForm.value)
  newValue.task_type = 2
  newValue.comment_at = ''
  batchList.value.unshift(newValue)

  let newArr = {
    id: ruleForm.value.task_id,
    comment_content: removeTagsExceptA(ruleForm.value.comment_content),
    task_state: 2,
    comment_at: ruleForm.value.comment_at,
    comment_user_name: ruleForm.value.comment_user_name,
    expression_url: ruleForm.value.expression_url
  }

  let batchArr = {
    id: ruleForm.value.task_id,
    comment_content: removeTagsExceptA(ruleForm.value.comment_content),
    task_state: 2,
    task_type: 2,
    comment_at: '请选择时间',
    comment_user_name: ruleForm.value.comment_user_name,
    expression_url: ruleForm.value.expression_url
  }
  batchTasks.value ? batchData.value.unshift(batchArr) : gridData.value.unshift(newArr)

  // 还原部分数据
  ruleFormRef.value.resetFields()
  commentatorRef.value.vest_list_top.forEach(v => { v.isClick = false })
  commentatorRef.value.dhtVal = []
  commentatorRef.value.addressVal = ''
  commentatorRef.value.vestSearch.nick_name = ''
  commentatorRef.value.vestSearch.location = ''
  ruleForm.value.comment_content = ''
  ruleForm.value.comment_at = '',
  ruleForm.value.task_type = 1
  ruleForm.value.task_id = ''
  sensitive.value = ''
  showEmojis.value = false
  module.value.moduleBox = false
  module.value.ifHeight = 120
  ruleForm.value.activity_url2 = ''
  ruleForm.value.expression_id = ''
  ruleForm.value.expression_url = ''
  ruleForm.value.component_type = ''
  ruleForm.value.project_id = ''
  picmanagementRef.value.presetData = {
    activeTab: 'tab1',
    imgValue: '',
    imgBox: false,
    cover: [],
    selectId: '',
    imgList: []
  }

}
defineExpose({
  drawer,
  isEdit,
  editForm,
  ruleForm,
  gridData,
  batchTasks
})
</script>
<style>
.w-e-text-container{
  padding:10px 0px !important;
}
.w-e-text-container p{
  margin:5px 0 !important
}
.el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 455px !important;
}

.emoji-mart {
  width: 400px !important;
  margin-top: 10px !important;
}
</style>
<style lang="scss" scoped>
.aiAction {
  float: right;
  height: 26px;
  line-height: 26px;
  background: linear-gradient(90deg, #b6e7ff 0%, #94b7ff 100%);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  cursor: pointer;
  margin-left: 10px;
  margin-top: 4px;

  .aiIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/AI.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 5px;
    margin-right: 5px;
  }
}

.commentMore {
  height: 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid #f8f8f8;

  span {
    float: left;
    width: 100px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid #f8f8f8;
  }

  .act {
    color: #fff;
    background-color: #0b82fd;
  }
}

.noComment {
  width: 100%;
  height: 30px;
  background-color: #f8f8f8;
  text-align: center;
  line-height: 30px;
  color: #bebebe;
  margin-bottom: 30px;
  font-size: 14px;
  margin-top: -10px;
}

.module {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  cursor: pointer;
  margin-right: 6px;

  .moduleIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/module.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 4px;
    margin-right: 5px;
  }
}

.emoji {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  margin-right: 5px;
  cursor: pointer;

  .emojiIcon {
    width: 15px;
    height: 15px;
    background: url("@/assets/images/emoji.png") no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 7px;
    margin-right: 5px;
  }
}

.line {
  height: 35px;
  line-height: 35px;
  position: relative;
  margin-bottom: 15px;
  border-radius: 2px;

  i {
    position: absolute;
    left: 0;
    top: 10px;
    width: 5px;
    height: 16px;
    background-color: #46a5e2;
  }

  span {
    font-size: 14px;
    position: absolute;
    left: 15px;
  }
}

.AI {
  position: absolute;
  top: 187px;
  right: 0;
}

.comment-content {
  display: flex;
  align-items: center;
}

.comment-image {
  width: 30px;
  height: 30px;
}

.comment-text {
  flex: 1;
}

.commentBox {
  height: 40px;
  position: relative;
  overflow: hidden;
  margin-bottom: 10px;

  .tabLine {
    position: absolute;
    right: 0px;
    top: 0;
    overflow: hidden;

    span {
      cursor: pointer;
      float: left;
      height: 26px;
      line-height: 26px;
      display: block;
      text-align: center;
      font-size: 12px;
      border-radius: 13px;
      padding: 0 15px;
    }

    i {
      float: left;
      width: 15px;
      height: 14px;
      margin: 6px 5px 0 0;
    }

    .def {
      background: rgba(11, 130, 253, 0.05);
      color: #0b82fd;

      i {
        background: url("@/assets/images/comment_icon.png") no-repeat;
        background-size: 15px auto;
      }
    }

    .sel {
      background: rgba(11, 130, 253, 0.05);
      color: #0b82fd;

      i {
        background: url("@/assets/images/collection_icon.png") no-repeat;
        background-size: 15px auto;
      }
    }
  }
}

.emoji-mart-category-label {
  display: none;
}

.icon {
  position: absolute;
  right: 20px;
  top: 120px;
  cursor: pointer;
}

.comment {
  .header {
    min-height: 80px;
    margin-bottom: 25px;
    position: relative;

    img {
      width: 100px;
      height: 80px;
      float: left;
      margin-right: 20px;
      object-fit: cover;
      flex: 1;
      border-radius: 3px;
    }

    .title {
      float: left;
      width: 580px;
      height: 30px;
      font-size: 14px;
      margin-top: 10px;
      margin-bottom: 15px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      a {
        color: #606266;
        text-decoration: none;
      }
    }

    .info {
      width: 430px;
      font-size: 12px;
      float: left;

      i {
        font-style: normal;
        padding: 2px 5px;
        text-align: center;
        background-color: #409eff;
        color: #fff;
      }

      span {
        margin-left: 5px;
        color: #666;
      }
    }
  }

  .stateStyle {
    cursor: pointer;

    b {
      font-weight: 400;
      float: right;
      margin-top: -2px;
    }
  }
}

.comment-center {
  padding: 20px 24px;
  overflow-y: auto;
  font-size: 16px;
  line-height: 30px;
  height: 500px;

  .child:hover {
    background: #c6e2ff;
    border-radius: 3px;
    cursor: pointer;
  }
}

.moduleBox {
  clear: both;
  width: 375px;
  position: relative;
  margin-top: 20px;

  .icon {
    position: absolute;
    right: -50px;
    top: 5px;
    z-index: 99;
    cursor: pointer;
  }

  .icon1 {
    position: absolute;
    right: -30px;
    top: 5px;
    z-index: 99;
    cursor: pointer;
  }

  iframe {
    width: 375px;
    position: relative;
    left: 0;
    top: 0;
    z-index: 1;
  }
}

.small{
  width: 79px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 4px;
  text-align: center;
  line-height: 32px;
  border: 1px solid rgba(0,0,0,0.2);
  margin: 5px;
  float: left;
  cursor: pointer;
}
.smallHover{
  color:#0B82FD;
  width: 79px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  background: rgba(11,130,253,0.2);
  border-radius: 4px;
  margin: 5px;
  border: 1px solid #0B82FD;
  float: left;
  cursor: pointer;
}
.timeList{
  width: 320px;
  float: right;
  margin-top: -55px;
  z-index: 999;
  position: relative;
  button{
    width: 70px;
    float: left !important;
    margin:5px 3px  !important
  }
}
</style>
