<template>
  <div class="head-box">
    <div class="left">
      <el-button plain type="primary" @click="back">
        <el-icon><Back /></el-icon>
        <span style="margin-left:5px;">返回</span>
      </el-button>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->
      <el-input
        v-model="searchData.comment_content"
        placeholder="请输入关键词"
        style="margin-right: 10px;"
        class="input-with-select"
        clearable @keyup.enter="handSearch"
      >
        <template #prepend>
          <el-select
            v-model="searchData.comment_search_type"

            placeholder="请选择"
            style="width:100px;"
            @change="typeChange"
          >
            <el-option v-for="item of commentSearchType" :key="item.key" :label="item.label" :value="item.key">
            </el-option>
          </el-select>
        </template>
        <template #append>
          <el-button

            type="primary" :icon="Search"
            style="display: flex;align-items: center;"
            @click="handSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose } from 'vue'

// 组件接收事件
const emit = defineEmits(['search', 'back'])

// 稿件标题时的关键字
const title_search_data = ref(null)
// 搜索类型
const commentSearchType = ref([
  {
    key: 'article_title',
    label: '评论内容'
  }
])
const loading = ref(false)
// 搜索参数
const searchData = ref({
  comment_search_type: 'article_title',
  comment_content: '' // 关键词
})

function back() {
  searchData.value.comment_content = ''
  emit('back')
}

// 点击搜索 每次当前页码变为1
const handSearch = () => {
  let newData = {
    comment_content: searchData.value.comment_content
  }
  emit('search', newData)
}

defineExpose({
  searchData,
  title_search_data
})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
    width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
    white-space: pre-wrap;
    height: auto;
    line-height: 24px;
    padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
    background-color: #ebebeb;
}

.head-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .left {
        width: 100px;
    }

    .right {
        display: flex;
        align-items: center;

        & > .el-select {
            width: 150px;
            margin-right: 10px;
        }

        .el-input-group {
            width: 400px;
        }
    }
}
</style>
<style>
.input-with-select .el-input-group__append {
    background-color: #1890ff;
    color: #fff;
}

.el-cascader {
    margin-right: 12px;
}
</style>
