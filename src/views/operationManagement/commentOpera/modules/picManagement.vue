<template>
  <div class="picture" @click="handlePicture"><span class="pictureIcon"></span>表情包</div>

  <!-- <el-upload
    ref="upload"
    :on-remove="handleRemove"
    :before-upload="beforeUpload"
    :limit="1"
    :accept="'.jpg,.jpeg,.png,.gif'"
    style="margin-top: -4px"
    @click="presetData.imgBox = false"
  >
    <div class="locality"><span class="localityIcon"></span>插入图片</div>
  </el-upload> -->
  <div class="locality" @click="openCustom"><span class="localityIcon"></span>插入图片</div>
  <div v-show="presetData.imgValue" class="imgShow">
    <img :src="presetData.imgValue" class="imgBlock" />
    <div class="imgClose" @click="handleImgClose"></div>
  </div>

  <div v-show="presetData.imgBox">
    <el-tabs v-model="presetData.activeTab" class="tabCont" @tab-click="handleClickTab">
      <div v-for="(item, index) of presetData.cover" :key="index">
        <el-tab-pane :name="'tab' + (parseInt(index) + 1)">
          <template #label>
            <img :src="item.url" class="headerImg" />
          </template>
          <div class="imgUl">
            <div v-for="item of presetData.imgList" :key="item.id" class="imgBox">
              <img class="imgList" :src="item.url" @click="handleOpenImg(item.url, item.id)" />
              <span>{{ item.name }}</span>
            </div>
          </div>
        </el-tab-pane>
      </div>
    </el-tabs>
  </div>
  <!-- <cropper
    ref="cropperRef"
    :img-obj="addImg"
    @saveData="handleSaveData"
    @fixedNumber="handleFixed"
  ></cropper> -->
  <div v-show="showCustom">
    <CustomPic  ref="customPic"  @onSelect="onSave" />
  </div>

</template>

<script setup name="picManagement">
import { ref, onMounted, watch, defineExpose } from 'vue'
import { requests } from '@/api/business/emote'
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomPic from '@/components/CustomPic/index.vue'
const emit = defineEmits(['succ', 'onOpen'])
import cropper from '@/components/cropper/index.vue'
const presetData = ref({
  activeTab: 'tab1',
  expression_id: '',
  imgValue: '',
  imgBox: false,
  cover: [],
  selectId: '',
  imgList: []
})
const addImg = ref({
  url: ''
})
const customPic = ref(null)
const upload = ref(null)
const cropperRef = ref(null)
const beforeImg = ref('')
const scale = ref('')
const showCustom = ref(false)
// 取比例
const handleFixed = v => {
  scale.value = v
}
function openCustom() {
  presetData.value.imgValue = ''
  presetData.value.imgBox = false
  showCustom.value = !showCustom.value
  customPic.value.getList()
   emit('onOpen')
   emit('succ', '', '')
}
// 上传前的验证
// const beforeUpload = file => {
//   const isJpgOrPng =
//     file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
//   const isLt10K = file.size < 500 * 1024

//   if (isJpgOrPng && isLt10K) {
//     const formData = new FormData()
//     formData.append('file', file) // 将文件添加到表单中
//     console.log(formData, 'formData')
//     requests.upload_expression(formData).then(res => {
//       if (res.code === 0) {
//         addImg.value.url = res.data.url

//         if (file.type === 'image/jpeg' || file.type === 'image/png') {
//           cropperRef.value.dialogVisible = true
//         } else {
//           handleSaveData(formData)
//         }
//       } else {
//         ElMessage.error('图片上传失败')
//       }
//     })
//     return false
//   }

//   if (!isJpgOrPng) {
//     ElMessage.error('只能上传 JPG、PNG、gif格式的图片')
//   }
//   if (!isLt10K) {
//     ElMessage.error('图片大小不能超过 500KB')
//   }
//   return isJpgOrPng && isLt10K
// }
// 传截图数据
function onSave(val,scale) {
  presetData.value.imgValue = `${val.image_url}?scale=${scale}`
  emit('succ', presetData.value.imgValue, '')
  showCustom.value = false
}
const handleSaveData = formData => {
  requests.upload_expression(formData).then(res => {
    if (res.code === 0) {
      beforeImg.value = res.data.url
      ElMessage.warning({
        message: '等待图片审核中',
        duration: 0 // 将持续时间设置为 0
      })
      requests.audit_sync({ image_url: beforeImg.value }).then(v => {
        if (v.data.resultCode === 0) {
          ElMessage.closeAll()
          ElMessage.success(v.data.resultMsg)
          cropperRef.value.dialogVisible = false
          presetData.value.imgValue = `${beforeImg.value}?scale=${scale.value}`
          emit('succ', presetData.value.imgValue, '')
        } else {
          ElMessageBox.confirm(
            '<span style="color: red;">' + v.data.resultMsg + '</span> 是否确认上传?',
            {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          )
            .then(() => {
              ElMessage.closeAll()
              cropperRef.value.dialogVisible = false
              presetData.value.imgValue = `${beforeImg.value}?scale=${scale.value}`
              emit('succ', presetData.value.imgValue, '')
            })
            .catch(() => {
              ElMessage.closeAll()
            })
        }
      })
    } else {
      ElMessage.error('图片上传失败')
    }
  })
}
// 预设图片
const handlePicture = () => {
  // presetData.value.imgValue = '';
  presetData.value.imgValue = ''
  presetData.value.activeTab = 'tab1'
  presetData.value.imgBox = !presetData.value.imgBox
  showCustom.value = false
  init()
  emit('onOpen')
  emit('succ', '', '')
}
// 重选预设图片
const handleImgClose = () => {
  presetData.value.imgValue = ''
  emit('succ', '', '')
}
onMounted(() => {
  // 取表情包封面
  init()
})
const init = () => {
  requests.list().then(res => {
    if (res.code === 0) {
      const filteredExpressions = res.data.list.filter(expression => expression.enable === 1)
      presetData.value.cover = filteredExpressions
      presetData.value.selectId = filteredExpressions[0].id
      requestIdImg(presetData.value.selectId)
    }
  })
}
// 根据id取表情
const requestIdImg = id => {
  requests.query_by_package({ expression_package_id: id }).then(res => {
    if (res.code === 0) {
      presetData.value.imgList = res.data.list
    }
  })
}

// 选项卡切换取数据
const handleClickTab = (tab, event) => {
  console.log(tab.index)
  const id = presetData.value.cover[tab.index].id
  requestIdImg(id)
}
// 选择表情图
const handleOpenImg = (url, id) => {
  presetData.value.imgValue = url
  presetData.value.expression_id = id
  emit('succ', url, id)
  presetData.value.imgBox = false
}
defineExpose({
  presetData,
  showCustom
})
</script>
<style>
.tabCont .el-tabs__active-bar {
  left: 5px;
}
.tabCont .el-tabs__item {
  padding: 0 5px !important;
}
.tabCont .el-tabs__nav-wrap::after {
  height: 1px !important;
}
</style>
<style lang="scss" scoped>
.tabCont {
  clear: both;
  width: 320px;
  border: 1px solid #efefef;
  overflow: hidden;
  margin-top: 20px;
}

.headerImg {
  width: 26px;
  height: 26px;
  border-radius: 4px 4px 0px 0px;
  margin-left: 10px;
}
.imgUl {
  width: 320px;
  overflow: hidden;

  padding: 0px 0 10px 3px;
  .imgBox {
    width: 50px;
    height: 70px;
    float: left;
    position: relative;
    margin: 6px;
    cursor: pointer;
    img {
      width: 50px;
      height: 50px;
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 4px 4px 0px 0px;
    }
    span {
      width: 50px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #969696;
      font-size: 10px;
      position: absolute;
      left: 0;
      top: 50px;
    }
  }
}
.picture {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  margin-right: 5px;
  cursor: pointer;
  .pictureIcon {
    width: 15px;
    height: 15px;
    background: url('@/assets/images/picture.png') no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 4px;
  }
}
.locality {
  float: left;
  height: 26px;
  line-height: 26px;
  background: rgba(11, 130, 253, 0.05);
  border-radius: 4px;
  font-size: 12px;
  padding: 0 10px;
  margin-right: 5px;
  cursor: pointer;
  .localityIcon {
    width: 15px;
    height: 15px;
    background: url('@/assets/images/locality.png') no-repeat;
    background-size: 15px auto;
    float: left;
    margin-top: 4px;
    margin-right: 5px;
    position: relative;
    z-index: 100;
  }
}
.imgShow {
  clear: both;
  float: left;
  width: 321px;
  height: 80px;
  background: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #ebebeb;
  margin-top: 10px;
  position: relative;
  .imgBlock {
    width: 60px;
    height: 63px;
    position: absolute;
    left: 10px;
    top: 8px;
    overflow: hidden;
    object-fit: cover;
  }
  .imgClose {
    cursor: pointer;
    width: 13px;
    height: 13px;
    position: absolute;
    left: 63px;
    top: 3px;
    background: url('@/assets/images/imgClose.png') no-repeat;
    background-size: 13px auto;
  }
}
</style>
