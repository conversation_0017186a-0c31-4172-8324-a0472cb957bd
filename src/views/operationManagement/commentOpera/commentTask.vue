/* stylelint-disable selector-anb-no-unmatchable */
<template>
  <el-tabs
    v-model="data.activeName"
    class="demo-tabs"
    @tab-click="handleClickTab"
  >
    <el-tab-pane
      :label="'全部 ' + data.state.all_number"
      name="0"
    ></el-tab-pane>
    <el-tab-pane
      :label="'执行中 ' + data.state.zhixingzhong_number"
      name="2"
    ></el-tab-pane>
    <el-tab-pane
      :label="'已完成 '+ data.state.yiwancheng_number"
      name="1"
    ></el-tab-pane>
    <el-tab-pane
      :label="'已停止 ' + data.state.yitingzhi_number"
      name="3"
    ></el-tab-pane>
    <el-tab-pane
      :label="'异常 ' + data.state.yichang_number"
      name="4"
    ></el-tab-pane>
  </el-tabs>
  <headModel
    ref="search"
    @back="back"
    @search="handleSearch"
  />
  <el-table
    v-loading="loading"
    :data="data.dataList"
  >
    <el-table-column
      v-slot="scope"
      prop="comment_content"
      label="评论内容"
      min-width="400"
    >
      <span
        v-if="scope.row.comment_level > 1"
        class="answer"
      >回复</span>
      <span v-html="scope.row.comment_content"></span>
    </el-table-column>
    <el-table-column
      v-slot="scope"
      label="稿件标题"
      width="400"
      :show-overflow-tooltip="true"
    >
      <a
        class="elemLink"
        target="_blank"
        :href="scope.row.article_url"
      >{{ scope.row.article_title }}</a>
    </el-table-column>
    <el-table-column
      prop="article_id"
      label="稿件ID"
      width="100"
      :show-overflow-tooltip="true"
    />
    <el-table-column
      prop="channel_name"
      label="来源频道"
      width="100"
    />
    <el-table-column
      v-slot="scope"
      label="任务状态"
      width="100"
    >
      <span
        v-if="scope.row.task_state == 2"
        style="color:#409EFF"
      >
        <el-icon>
          <InfoFilled />
        </el-icon> 执行中
      </span>
      <span
        v-else-if="scope.row.task_state == 1"
        style="color:#67C23A"
      >
        <el-icon>
          <SuccessFilled />
        </el-icon> 已完成
      </span>
      <span
        v-else-if="scope.row.task_state == 3"
        style="color:#E6A23C"
      >
        <el-icon>
          <CircleCloseFilled />
        </el-icon> 已停止
      </span>
      <span
        v-else-if="scope.row.task_state == 4"
        style="color:#F56C6C"
      >
        <el-icon>
          <WarningFilled />
        </el-icon> 异常
      </span>
    </el-table-column>
    <el-table-column
      prop="comment_user_name"
      label="评论人"
      width="150"
    />
    <el-table-column
      v-slot="scope"
      prop="comment_at"
      label="评论时间"
      width="200"
    >
      {{ handleTime(scope.row.comment_at) }}
    </el-table-column>
    <el-table-column
      prop="task_create_name"
      label="创建人"
      width="150"
    />
    <el-table-column
      v-slot="scope"
      prop="task_create_at"
      label="创建时间"
      width="200"
    >
      {{ handleTime(scope.row.task_create_at) }}
    </el-table-column>

    <el-table-column
      v-slot="scope"
      label="操作"
      fixed="right"
      width="180"
    >
      <el-button
        v-if="scope.row.task_state === 2"
        v-auth="'comment_operation_task:enable'"
        size="small"
        text
        style="margin-left:0"
        @click="stop(scope.row.id)"
      >
        <span style="color:#0B82FD;">停止</span>
      </el-button>
      <el-button
        v-if="scope.row.task_state === 3"
        v-auth="'comment_operation_task:enable'"
        size="small"
        text
        style="margin-left:0"
        @click="stop(scope.row.id)"
      >
        <span style="color:#0B82FD;">执行</span>
      </el-button>
      <el-button
        v-if="scope.row.task_state === 3 ||scope.row.task_state === 4"
        v-auth="'comment_operation_task:update'"
        size="small"
        text
        style="margin-left:0"
        @click="handleEdit(scope.row.id)"
      >
        <span style="color:#0B82FD;">编辑</span>
      </el-button>
      <el-button
        v-auth="'task_operator_log:list'"
        text
        style="margin-left:0"
        type="primary"
        @click="showHistory(scope.row)"
      >
        <el-icon>
          <Document />
        </el-icon>
      </el-button>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <div class="page">
    <el-pagination
      background
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
  <History ref="history" />
  <Add
    ref="add"
    @success="updateSuccess"
  />
</template>

<script setup name="cardList">
import headModel from './modules/headModule2.vue'
import { InfoFilled, SuccessFilled, CircleCloseFilled, WarningFilled } from '@element-plus/icons'
import { computed, nextTick, ref, defineEmits } from 'vue'
import { Document } from '@element-plus/icons'
import History from './modules/history.vue'
import { requests } from '@/api/business/commentOpera'
import Add from './modules/add.vue'
const emit = defineEmits(['search', 'back'])
const data = ref({
  dataList: [{
    article_id: 1
  }],
  state: {
    all_number: 0,
    zhixingzhong_number: 0,
    yiwancheng_number: 0,
    yitingzhi_number: 0,
    yichang_number: 0
  },
  total: 0,
  size: 100,
  current: 1,
  channel_id: '',
  article_title: '',
  article_id: '',
  task_create_name: '',
  start_create_date: '',
  end_create_date: '',
  activeName: '0'
})
const add = ref(null)
// 日志
const history = ref(null)
// 搜索
const search = ref(null)
const loading = ref(false)

onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementsByClassName(
      'el-table__header-wrapper'
    )[0].offsetHeight
    document.getElementsByClassName(
      'el-table__body-wrapper'
    )[0].style.height = `calc(100% - ${height}px)`
  })
  //   getStateNum()
  //   init()
})
// 编辑评论任务
function handleEdit (id) {
  requests.get_detail({ id }).then(res => {
    if (res.code == 0) {
      add.value.drawer = true
      add.value.isEdit = true
      add.value.editForm = res.data.comment_task
      add.value.editForm.id = id
    }
  })
}
// 更新成功后处理
function updateSuccess () {
  add.value.sensitive = ''
  add.value.drawer = false
  init()
  getStateNum()
}

// 获取标签数量
function getStateNum (val = data.value.article_id) {
  let searchData = {
    channel_id: data.value.channel_id,
    article_title: data.value.article_title,
    article_id: data.value.article_id,
    start_create_date: data.value.start_create_date,
    end_create_date: data.value.end_create_date,
    task_create_name: data.value.task_create_name,
    params: 'suoyou,yiwancheng,zhixingzhong,yitingzhi,yichang'
  }
  requests.getStateNum(searchData).then(res => {
    data.value.state = res.data
  })
}
// 时间转换
function handleTime (time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s

}
// 初始化
function init () {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    channel_id: data.value.channel_id,
    article_title: data.value.article_title,
    article_id: data.value.article_id,
    task_state: data.value.activeName === '0' ? '' : data.value.activeName,
    start_create_date: data.value.start_create_date,
    end_create_date: data.value.end_create_date,
    task_create_name: data.value.task_create_name
  }
  loading.value = true
  requests.getTaskList(searchData).then(res => {
    data.value.total = res.data.comment_task_list.total > 10000 ? 10000 : res.data.comment_task_list.total
    data.value.dataList = res.data.comment_task_list.records
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}
// 标签切换
function handleClickTab (val) {
  data.value.current = 1
  data.value.activeName = val.paneName
  init()
}
// 停止/执行
function stop (id) {
  requests.enableTask({ id: id }).then(res => {
    init()
    getStateNum()
  })
}
// 获取操作日志
function showHistory (val) {
  history.value.drawer = true
  history.value.id = val.task_id
  history.value.title = val.comment_content
  history.value.init()
}
// 标题搜索
function searchTitle (val) {
  data.value.article_id = val
  search.value.searchData.searchword = val
  init()
}
function handleSearch (val) {
  data.value.channel_id = val.channel_id
  data.value.article_title = val.article_title
  data.value.article_id = val.article_id
  data.value.task_create_name = val.task_create_name
  data.value.start_create_date = val.start_create_date
  data.value.end_create_date = val.end_create_date
  init()
  getStateNum()
}
// 编辑评论任务
function handleAdd () {
  add.value.drawer = true
}
// 返回
function back () {
  data.value.channel_id = ''
  data.value.article_title = ''
  data.value.article_id = ''
  data.value.task_create_name = ''
  data.value.start_create_date = ''
  data.value.end_create_date = ''
  emit('back')
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
defineExpose({
  getStateNum,
  searchTitle,
  init
})
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #333;
}
a:hover,
a:visited,
a:link,
a:active {
  color: #333;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  :deep(.el-table) {
    height: 100%;
    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}
.answer {
  font-size: 12px;
  padding: 3px;
  background: #409eff;
  margin-right: 5px;
  color: #fff;
}
</style>
