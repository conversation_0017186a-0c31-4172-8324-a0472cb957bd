<template>
  <div class="myStyle">
    <div
      v-if="
        state === 'init' ||
        state === 'preparing' ||
        state === 'preparing-error' ||
        state === 'writing-error'
      "
      class="start"
    >
      <div v-if="state === 'init'" class="icon"></div>
      <div
        v-if="state === 'preparing' || state === 'preparing-error' || state === 'writing-error'"
        class="icon2"
      ></div>
      <div
        v-if="
          state === 'init' ||
          state === 'preparing' ||
          state === 'preparing-error' ||
          state === 'writing-error'
        "
        class="text"
      >
        大模型根据对内容的理解进行评论撰写，撰写出的评论不具备风格化，可以使用“我的风格”撰写带有个人风格的评论。若需设置大模型撰写评论条数及字数，请前往“
        <span @click="toRule"> 规则设置 </span>
        ”中设置。
      </div>
      <el-button
        v-if="state === 'init' || state === 'preparing-error'"
        type="primary"
        color="#0B82FD"
        @click="prepareAIWrite"
      >
        开始撰写
      </el-button>
      <el-button
        v-if="state === 'writing-error'"
        type="danger"
        color="#0B82FD"
        @click="prepareAIWrite"
      >
        撰写失败，重新撰写
      </el-button>
      <div v-if="state === 'preparing' || state === 'writing'" class="load-text">
        风格评论生成中...
      </div>
      <el-button v-if="state === 'preparing'" type="danger" color="#0B82FD" @click="stopPreparing">
        停止连接
      </el-button>
      <div v-if="state === 'preparing-error' || state === 'writing-error'" class="tips">
        抱歉未能完成任务，请稍后再试
      </div>
    </div>
    <div v-if="state === 'writing' || state === 'idle'" class="list">
      <div v-if="state === 'writing' || state === 'idle'" class="list-container">
        <div v-for="(item, index) in list" :key="index" class="child">
          {{ item }}
          <div class="check" @click="choise(item)"></div>
        </div>
      </div>
      <el-button v-if="state === 'idle'" type="primary" color="#0B82FD" @click="prepareAIWrite">
        不满意，重新撰写
      </el-button>
      <el-button v-if="state === 'writing'" type="danger" color="#0B82FD" @click="stopStreaming">
        停止撰写
      </el-button>
    </div>
  </div>
</template>
<script setup name="myStyle">
import { ref, watch } from 'vue'
import { requests } from '@/api/business/aiWrite'
import axios from 'axios'
import { ElMessage } from 'element-plus'

const props = defineProps({
  id: {
    type: String || Number
  }
})

const emit = defineEmits(['toRule', 'setComment'])
/**
 * init: 初始化状态
 * preparing: 准备中（建立Http连接）
 * writing: 撰写中
 * preparing-error: 准备失败
 * writing-error: 撰写失败
 * idle: 空闲(撰写完成)
 */
const state = ref('init')
const list = ref([])
let sseClient = null
let cancelTokenSource // 用于存储取消令牌源

// 选择评论
function choise(item) {
  emit('setComment', item)
}

// 前往规则设置
function toRule() {
  emit('toRule')
}

const stopPreparing = () => {
  if (sseClient) {
    sseClient.close()
    sseClient = null
  }
  if (cancelTokenSource) {
    cancelTokenSource.cancel('Request canceled by the user')
    cancelTokenSource = null
  }
  state.value = 'init'
}

const stopStreaming = () => {
  if (sseClient) {
    sseClient.close()
    sseClient = null
  }
  state.value = 'idle'
}


const prepareAIWrite = () => {
  requests.getStatus().then(res => {
    if (res.data.is_config) {
      getStreamList()
    }
  })
}

const getStreamList = () => {
  list.value = []
  state.value = 'preparing'
  const errorCallback = () => {
    state.value = 'preparing-error'
  }
  const eventCallback = (event, data) => {
    console.log('event===', event)
    console.log('data===', data)
    if (event === 'stream-start') {
      state.value = 'writing'
    } else if (event === 'stream-data') {
      try {
        const jsonData = JSON.parse(data)
        if (jsonData.list.length > 0) {
          list.value = jsonData.list
          state.value = 'writing'
        }
      } catch (e) {
        console.log('解析文本失败', e)
      }
    } else if (event === 'stream-end') {
      try {
        const jsonData = JSON.parse(data)
        const commentList = jsonData.list
        list.value = commentList
        if (commentList.length > 0) {
          state.value = 'idle'
        } else {
          state.value = 'writing-error'
        }
        console.log('最终结果是===', commentList)
      } catch (e) {
        state.value = 'writing-error'
        console.log('解析最终文本失败', e)
      }
    } else if (event === 'stream-error') {
      state.value = 'writing-error'
      try {
        const jsonData = JSON.parse(data)
        const message = jsonData.message
        ElMessage.error(message)
      } catch (e) {
        ElMessage.error('获取数据失败')
      }
      console.log('stream-error===', data)
    }
  }
  sseClient = requests.getMyStyleSSE({ article_id: props.id, platform_type: 'my_style_default' }, eventCallback, errorCallback)
}

</script>
<style lang="scss" scoped>
.myStyle {
  .start {
    padding-top: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon {
      width: 200px;
      height: 200px;
      background: url(@/assets/images/AI/myStyle.png) no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto;
    }

    .icon2 {
      width: 200px;
      height: 200px;
      background: url(@/assets/images/AI/load.png) no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto;
    }

    .text {
      width: 460px;
      margin: 0 auto;
      font-size: 16px;
      font-weight: 400;
      color: #6d6d6d;
      line-height: 30px;

      span {
        color: #0b82fd;
        cursor: pointer;
      }
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }

    .load-text {
      margin-top: 38px;
      font-size: 14px;
      text-align: center;
      font-weight: 400;
      color: #0b82fd;
      line-height: 24px;
    }

    .tips {
      margin: 10px auto;
      font-size: 14px;
      font-weight: 400;
      color: #ff2c0d;

      span {
        text-decoration: underline;
        color: #0b82fd;
        cursor: pointer;
      }
    }
  }

  .list {
    width: 100%;
    height: calc(100vh - 110px);
    overflow-y: auto;
    padding: 24px 20px;

    .list-container {
      width: 100%;
      overflow-y: auto;
      height: 80%;
      .child {
        width: calc(100% - 4px);
        padding: 10px 10px 30px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 28px;
        position: relative;
        background: #fffdf8;
        border-radius: 4px;
        margin-bottom: 15px;

        .check {
          width: 41px;
          height: 22px;
          background: url(@/assets/images/AI/check.png) no-repeat center center;
          background-size: 100% auto;
          position: absolute;
          bottom: 0;
          right: -4px;
          cursor: pointer;
        }
      }
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }
  }
}
</style>
