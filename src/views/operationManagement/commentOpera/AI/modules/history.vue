<template>
  <div class="history">
    <div v-if="showList" v-infinite-scroll="load" infinite-scroll-distance="50" :class="['history-list',type === 'small' ? 'list-small' : '']">
      <div v-for="(item, index) in list" :key="index" class="child">
        {{ item.comment_content }}
        <img :src="getIcon(item.platform_type)" alt="" width="20" height="20" class="platform-img" />
        <div class="check" @click="choise(item.comment_content)"></div>
      </div>
    </div>
    <div v-else class="start">
      <div class="icon"></div>
      <div class="text">暂无记录</div>
    </div>
    <div class="footer">
      <span @click="clean">一键清除记录</span>
    </div>
  </div>
</template>
<script setup name="history">
import { ref } from 'vue'
import { requests } from '@/api/business/aiWrite'
import { ElMessage } from 'element-plus'
import AIDefault from '@/assets/images/AI/ai_default.png'
import AIDouYin from '@/assets/images/AI/ai_douyin.png'
import AIShiPinHao from '@/assets/images/AI/ai_shipinhao.png'
import AIWeiBo from '@/assets/images/AI/ai_weibo.png'
import AIXiaoHongShu from '@/assets/images/AI/ai_xiaohongshu.png'
import AIMyStyle from '@/assets/images/AI/ai_mystyle_default.png'

const getIcon = type => {
  switch (type) {
    case 'douyin':
      return AIDouYin
    case 'shipinhao':
      return AIShiPinHao
    case 'weibo':
      return AIWeiBo
    case 'xiaohongshu':
      return AIXiaoHongShu
    case 'common_default':
      return AIDefault
    case 'my_style_default':
      return AIMyStyle
    default:
      return AIDefault
  }
}

const emit = defineEmits(['setComment'])
const props = defineProps({
  id: {
    type: String || Number
  },
  type: {
    type: String
  }
})
const showList = ref(true)
const hasMore = ref(true)
const current = ref(1)
const list = ref([])

// 选择评论
function choise(item) {
  emit('setComment', item)
}

function load() {
  console.log(hasMore.value)
  if (!hasMore.value) {
    return
  }
  requests
    .getHistory({ article_id: props.id, current: current.value })
    .then(res => {
      if (current.value === 1 && res.data.list.length === 0) {
        showList.value = false
        return
      }
      list.value = list.value.concat(res.data.list)
      hasMore.value = res.data.has_more
      current.value = current.value + 1
    })
    .catch(v => {
      hasMore.value = false
    })
}

function clean() {
  requests.clearHistory({ article_id: props.id }).then(res => {
    ElMessage({
      type: 'success',
      message: '历史记录删除成功！'
    })
    list.value = []
    showList.value = false
  })
}
</script>
<style lang="scss" scoped>
.history {
  .start {
    padding-top: 100px;

    .icon {
      width: 200px;
      height: 200px;
      background: url(@/assets/images/AI/history.png) no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto;
    }

    .text {
      width: 460px;
      margin: 0 auto;
      font-size: 14px;
      text-align: center;
      font-weight: 400;
      color: #666666;
      line-height: 14px;

      span {
        color: #0b82fd;
      }
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }

    .tips {
      margin: 6px auto;
      width: 400px;
      font-size: 14px;
      font-weight: 400;
      color: #ff2c0d;

      span {
        text-decoration: underline;
        color: #0b82fd;
        cursor: pointer;
      }
    }
  }

  .history-list {
    width: 100%;
    height: calc(100vh - 180px);
    overflow-y: auto;
    padding: 24px 20px;

    .child {
      width: 100%;
      padding: 10px 10px 30px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: 28px;
      position: relative;
      background: #fffdf8;
      border-radius: 4px;
      margin-bottom: 15px;

      .platform-img {
        width: 20px;
        height: 20px;
        position: absolute;
        bottom: 0;
        right: 44px;
      }

      .check {
        width: 41px;
        height: 22px;
        background: url(@/assets/images/AI/check.png) no-repeat center center;
        background-size: 100% auto;
        position: absolute;
        bottom: 0;
        right: -4px;
        cursor: pointer;
      }
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }
  }

  .list-small {
    height: calc(90vh - 140px);
  }

  .footer {
    background: #edf5f9;
    width: 100%;
    height: 70px;
    border-top: 1px solid #efefef;
    position: absolute;
    left: 0;
    bottom: 0;
    padding-left: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #0b82fd;
    display: flex;
    align-items: center;

    &::before {
      content: ' ';
      width: 16px;
      height: 16px;
      background: url(@/assets/images/AI/del.png) no-repeat center center;
      background-size: 100% auto;
      margin-right: 6px;
    }

    span {
      cursor: pointer;
    }
  }
}
</style>
