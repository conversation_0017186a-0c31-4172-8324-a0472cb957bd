<template>
  <div class="myStyle">
    <div
      v-if="
        state === 'init' ||
        state === 'preparing' ||
        state === 'preparing-error' ||
        state === 'writing-error'
      "
      class="start"
    >
      <div v-if="state === 'init'" class="icon"></div>
      <div
        v-if="state === 'preparing' || state === 'preparing-error' || state === 'writing-error'"
        class="icon2"
      ></div>
      <div
        v-if="
          state === 'init' ||
          state === 'preparing' ||
          state === 'preparing-error' ||
          state === 'writing-error'
        "
        class="text"
      >
        大模型根据对内容的理解进行评论撰写，撰写出的评论不具备风格化，可以使用“我的风格”撰写带有个人风格的评论。若需设置大模型撰写评论条数及字数，请前往“
        <span @click="toRule"> 规则设置 </span>
        ”中设置。
      </div>
      <div v-if="state === 'writing-error'" class="writing-error-container">
        <el-button
          type="danger"
          color="#0B82FD"
          @click="prepareAIWrite"
        >
          撰写失败，重新撰写
        </el-button>
        <div class="idle-line">
          <div v-if="currentPlatform !== 'common_default'" class="idle-line-item" @click="onClickPlatform('common_default')">
            <img :src="AIDefault" alt="" width="20px" height="20px">
            <span>默认</span>
          </div>
          <div v-if="currentPlatform !== 'douyin'" class="idle-line-item" @click="onClickPlatform('douyin')">
            <img :src="AIDouYin" alt="" width="20px" height="20px">
            <span>抖音</span>
          </div>
          <div v-if="currentPlatform !== 'xiaohongshu'" class="idle-line-item" @click="onClickPlatform('xiaohongshu')">
            <img :src="AIXiaoHongShu" alt="" width="20px" height="20px">
            <span>小红书</span>
          </div>
          <div v-if="currentPlatform !== 'weibo'" class="idle-line-item" @click="onClickPlatform('weibo')">
            <img :src="AIWeiBo" alt="" width="20px" height="20px">
            <span>微博</span>
          </div>
          <div  v-if="currentPlatform !== 'shipinhao'" class="idle-line-item" @click="onClickPlatform('shipinhao')">
            <img :src="AIShiPinHao" alt="" width="20px" height="20px">
            <span>视频号</span>
          </div>
        </div>
      </div>
      <div v-if="state === 'preparing' || state === 'writing'" class="load-text">
        风格评论生成中...
      </div>
      <div v-if="state === 'init' || state === 'preparing-error'" class="platform-style-container">
        <div class="platform-style-left">
          <div class="platform-style-left-line">
            <div class="platform-style-left-line-container" @click="onClickPlatform('douyin')">
              <img :src="AIDouYin" alt="" width="20" height="20" />
              <span>抖音风格</span>
            </div>
            <div class="platform-style-left-line-container" @click="onClickPlatform('xiaohongshu')">
              <img :src="AIXiaoHongShu" alt="" width="20" height="20" />
              <span>小红书风格</span>
            </div>
          </div>
          <div class="platform-style-left-line">
            <div class="platform-style-left-line-container" @click="onClickPlatform('weibo')">
              <img :src="AIWeiBo" alt="" width="20" height="20" />
              <span>微博风格</span>
            </div>
            <div class="platform-style-left-line-container" @click="onClickPlatform('shipinhao')">
              <img :src="AIShiPinHao" alt="" width="20" height="20" />
              <span>视频号风格</span>
            </div>
          </div>
        </div>
        <div class="platform-style-right" @click="onClickPlatform('common_default')">
          <img :src="AIDefault" alt="" width="20" height="20" />
          <span>默认风格</span>
        </div>
      </div>
      <el-button v-if="state === 'preparing'" type="danger" color="#0B82FD" @click="stopPreparing">
        停止连接
      </el-button>
      <div v-if="state === 'preparing-error' || state === 'writing-error'" class="tips">
        抱歉未能完成任务，请稍后再试
      </div>
    </div>
    <div v-if="state === 'writing' || state === 'idle'" class="list">
      <div class="list-container">
        <div v-for="(item, index) in list" :key="index" class="child">
          {{ item }}
          <div class="check" @click="choise(item)"></div>
        </div>
      </div>

      <div v-if="state === 'idle'" :class="['idle-container',type === 'small' ? 'idle-container-small' : '']">
        <el-button type="primary" color="#0B82FD" class="idle-button" @click="onClickPlatform(currentPlatform)">
          <img :src="currentPlatformIcon" ALT="" width="22px" height="22px" style="margin-right: 4px" />
          <span style="font-size: 14px">不满意，重新撰写</span>
        </el-button>
        <div class="idle-line">
          <div v-if="currentPlatform !== 'common_default'" class="idle-line-item" @click="onClickPlatform('common_default')">
            <img :src="AIDefault" alt="" width="20px" height="20px">
            <span>默认</span>
          </div>
          <div v-if="currentPlatform !== 'douyin'" class="idle-line-item" @click="onClickPlatform('douyin')">
            <img :src="AIDouYin" alt="" width="20px" height="20px">
            <span>抖音</span>
          </div>
          <div v-if="currentPlatform !== 'xiaohongshu'" class="idle-line-item" @click="onClickPlatform('xiaohongshu')">
            <img :src="AIXiaoHongShu" alt="" width="20px" height="20px">
            <span>小红书</span>
          </div>
          <div v-if="currentPlatform !== 'weibo'" class="idle-line-item" @click="onClickPlatform('weibo')">
            <img :src="AIWeiBo" alt="" width="20px" height="20px">
            <span>微博</span>
          </div>
          <div  v-if="currentPlatform !== 'shipinhao'" class="idle-line-item" @click="onClickPlatform('shipinhao')">
            <img :src="AIShiPinHao" alt="" width="20px" height="20px">
            <span>视频号</span>
          </div>
        </div>
      </div>

      <div v-if="state === 'writing'" :class="['stop-button', type === 'small' ? 'stop-button-small' : '']">
        <el-button type="danger" color="#0B82FD" @click="stopStreaming">
          停止撰写
        </el-button>
      </div>

    </div>
  </div>
</template>
<script setup name="myStyle">
import { computed, onMounted, ref, watch } from 'vue'
import { requests } from '@/api/business/aiWrite'
import 'md-editor-v3/lib/preview.css'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import AIDefault from '@/assets/images/AI/ai_default.png'
import AIDouYin from '@/assets/images/AI/ai_douyin.png'
import AIShiPinHao from '@/assets/images/AI/ai_shipinhao.png'
import AIWeiBo from '@/assets/images/AI/ai_weibo.png'
import AIXiaoHongShu from '@/assets/images/AI/ai_xiaohongshu.png'

const props = defineProps({
  id: {
    type: String || Number
  },
  type: {
    type: String
  }
})
const emit = defineEmits(['toRule', 'setComment'])
/**
 * init: 初始化状态
 * preparing: 准备中（建立Http连接）
 * writing: 撰写中
 * preparing-error: 准备失败
 * writing-error: 撰写失败
 * idle: 空闲(撰写完成)
 */
const state = ref('init')
const list = ref([])
let sseClient = null
let cancelTokenSource // 用于存储取消令牌源
const currentPlatform = ref('common_default')
const currentPlatformIcon = computed(() => {
  if (currentPlatform.value === 'douyin') {
    return AIDouYin
  } else if (currentPlatform.value === 'xiaohongshu') {
    return AIXiaoHongShu
  } else if (currentPlatform.value === 'weibo') {
    return AIWeiBo
  } else if (currentPlatform.value === 'shipinhao') {
    return AIShiPinHao
  } else {
    return AIDefault
  }
})
// 前往规则设置
function toRule() {
  emit('toRule')
}

// 选择评论
function choise(item) {
  emit('setComment', item)
}

const stopPreparing = () => {
  if (sseClient) {
    sseClient.close()
    sseClient = null
  }
  if (cancelTokenSource) {
    cancelTokenSource.cancel('Request canceled by the user')
    cancelTokenSource = null
  }
  state.value = 'init'
}

const stopStreaming = () => {
  if (sseClient) {
    sseClient.close()
    sseClient = null
  }
  state.value = 'idle'
}

const prepareAIWrite = () => {
  getStreamList()
}

const onClickPlatform = (platformParam = '') => {
  console.log('platform===', platformParam)
  currentPlatform.value = platformParam
  prepareAIWrite()
}

const getStreamList = () => {
  list.value = []
  state.value = 'preparing'
  const errorCallback = () => {
    state.value = 'preparing-error'
  }
  const eventCallback = (event, data) => {
    console.log('event===', event)
    console.log('data===', data)
    if (event === 'stream-start') {
      state.value = 'writing'
    } else if (event === 'stream-data') {
      try {
        const jsonData = JSON.parse(data)
        if (jsonData.list.length > 0) {
          list.value = jsonData.list
          state.value = 'writing'
        }
      } catch (e) {
        console.log('解析文本失败', e)
      }
    } else if (event === 'stream-end') {
      try {
        const jsonData = JSON.parse(data)
        const commentList = jsonData.list
        list.value = commentList
        if (commentList.length > 0) {
          state.value = 'idle'
        } else {
          state.value = 'writing-error'
        }
        console.log('最终结果是===', commentList)
      } catch (e) {
        state.value = 'writing-error'
        console.log('解析最终文本失败', e)
      }
    } else if (event === 'stream-error') {
      state.value = 'writing-error'
      try {
        const jsonData = JSON.parse(data)
        const message = jsonData.message
        ElMessage.error(message)
      } catch (e) {
        ElMessage.error('获取数据失败')
      }
      console.log('stream-error===', data)
    }
  }
  sseClient = requests.getStyleSSE(
    { article_id: props.id, platform_type: currentPlatform.value },
    eventCallback,
    errorCallback
  )
}
</script>
<style lang="scss" scoped>
.myStyle {
  .start {
    padding-top: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon {
      width: 200px;
      height: 200px;
      background: url(@/assets/images/AI/style.png) no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto;
    }

    .icon2 {
      width: 200px;
      height: 200px;
      background: url(@/assets/images/AI/load.png) no-repeat center center;
      background-size: 100% auto;
      margin: 0 auto;
    }

    .text {
      width: 460px;
      margin: 0 auto;
      font-size: 16px;
      font-weight: 400;
      color: #6d6d6d;
      line-height: 30px;

      span {
        color: #0b82fd;
        cursor: pointer;
      }
    }

    .writing-error-container {
      display: flex;
      flex-direction: column;
    }

    .load-text {
      margin-top: 38px;
      font-size: 14px;
      text-align: center;
      font-weight: 400;
      color: #0b82fd;
      line-height: 24px;
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }

    .tips {
      font-size: 14px;
      font-weight: 400;
      color: #ff2c0d;
      margin-top: 10px;

      span {
        text-decoration: underline;
        color: #0b82fd;
        cursor: pointer;
      }
    }

    .platform-style-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      width: 400px;
      margin-top: 30px;
      gap: 10px;
      font-size: 14px;
      color: #333333;

      .platform-style-left {
        display: flex;
        flex-direction: column;
        gap: 14px;

        .platform-style-left-line {
          display: flex;
          flex-direction: row;
          gap: 14px;

          .platform-style-left-line-container {
            width: 136px;
            height: 40px;
            border-radius: 4px;
            background: linear-gradient(to right, #DCDDF5, #CCE4FA);
            justify-content: center;
            align-items: center;
            display: flex;
            gap: 2px;
            cursor: pointer;
          }
        }
      }

      .platform-style-right {
        width: 136px;
        height: 94px;
        background: #e6e6e6;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: linear-gradient(to right, #CFE3F9, #CAF1FA);
        cursor: pointer;
        gap: 4px;
      }
    }
  }

  .list {
    width: 100%;
    height: calc(100vh - 110px);
    display: flex;
    padding: 24px 20px;
    flex-direction: column;
    align-items: center;

    .list-container {
      width: 100%;
      flex: 1;
      overflow-y: auto;
      .child {
        width: calc(100% - 4px);
        padding: 10px 10px 30px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 28px;
        position: relative;
        background: #fffdf8;
        border-radius: 4px;
        margin-bottom: 15px;

        .check {
          width: 41px;
          height: 22px;
          background: url(@/assets/images/AI/check.png) no-repeat center center;
          background-size: 100% auto;
          position: absolute;
          bottom: 0;
          right: -4px;
          cursor: pointer;
        }
      }
    }

    .el-button {
      display: block;
      margin: 30px auto 0;
      width: 400px;
      height: 40px;
    }
  }

  .idle-container {
    display: flex;
    flex-direction: column;
    width: 400px;
    height: 150px;

    .idle-button {
      background-color: #0B82FD;
      height: 40px;
    }

  }

  .idle-container-small {
    height: 210px;
  }

  .stop-button {
    display: flex;
    flex-direction: column;
    height: 150px;
  }

  .stop-button-small {
    display: flex;
    flex-direction: column;
    height: 210px;
  }
}

.idle-line {
  display: flex;
  flex-direction: row;
  gap: 6px;
  margin-top: 10px;

  .idle-line-item {
    flex: 1;
    height: 40px;
    background: linear-gradient(to right, #DCDDF5, #CCE4FA);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
  }
}

</style>
