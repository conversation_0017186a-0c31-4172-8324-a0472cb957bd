<template>
  <div>
    <el-empty v-if="streamData.length === 0" description="正在思考中...">
      <template #image>
        <i class="el-icon-loading"></i>
      </template>
    </el-empty>
    <md-preview v-else :model-value="streamData" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/preview.css'
import { useStore } from 'vuex'
import 'element-plus/theme-chalk/el-empty.css'

const store = useStore()
const streamData = computed(() => store.getters['streamData/streamData'])
</script>
