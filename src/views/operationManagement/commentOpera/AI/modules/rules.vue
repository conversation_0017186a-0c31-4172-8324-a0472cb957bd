<template>
  <!-- 规则设置 -->
    <div class="numberRule">
      <span>单次撰写</span>
      <el-input-number v-model="singleNum.res_count"
                       :min="1"
                       :max="10"
                       style="width: 100px;margin-right: 6px;"
                       @change="onChange" />
      <span>条</span>
      <el-divider direction="vertical" />
      <span>字数限制</span>
      <el-input-number v-model="singleNum.res_text_min"
                       :min="5"
                       :max="255"
                       style="width: 110px;margin-right: 6px;"
                       @change="onChange2" />
      <span>至</span>
      <el-input-number v-model="singleNum.res_text_max"
                       :min="singleNum.res_text_min + 1 > 255 ? 255 : singleNum.res_text_min + 1"
                       :max="255"
                       style="width: 110px;margin-right: 6px;"
                       @change="onChange2" />
      <span>字</span>
    </div>
    <el-input v-model="guide_examples[0]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[1]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[2]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[3]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[4]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[5]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[6]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <el-input v-model="guide_examples[7]"
              :rows="3"
              :maxlength="255"
              type="textarea"
              show-word-limit
              placeholder="风格评论示例数据录入"
              style="margin-bottom: 15px"
              @input="onChange" />
    <div class="footer">
      <el-button type="primary"
                 color="#0B82FD"
                 :icon="Box"
                 :disabled="disabled"
                 @click="save">
        保存规则
      </el-button>
    </div>
</template>
<script setup name="AI">
import { Box } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import { requests } from '@/api/business/aiWrite'
import { ElMessage, ElMessageBox } from 'element-plus'

const singleNum = ref({
  res_count: 3,
  res_text_min: 10,
  res_text_max: 20
})
const guide_examples = ref([
  '', '', '', '', '', '', '', ''
])
const disabled = ref(true)
const loading = ref(false)
function onChange () {
  disabled.value = false
}
function onChange2 () {
  if (singleNum.value.res_text_min > singleNum.value.res_text_max) {
    singleNum.value.res_text_max = singleNum.value.res_text_min
  }
  disabled.value = false
}
function getRule () {
  if (!disabled.value) return
  requests.getRule({}).then(res => {
    singleNum.value.res_count = res.data.res_count
    singleNum.value.res_text_min = res.data.res_text_min
    singleNum.value.res_text_max = res.data.res_text_max
    guide_examples.value = JSON.parse(res.data.guide_examples)
  })
}

function save () {
  if (loading.value) return
  loading.value = true
  requests.saveRule({ guide_examples: JSON.stringify(guide_examples.value), ...singleNum.value }).then(res => {
    ElMessage({
      message: '保存成功',
      type: 'success'
    })
    loading.value = false
    disabled.value = true
  }).catch(v => {
    loading.value = false
  })
}
defineExpose({
  getRule
})
</script>
<style lang="scss" scoped>
.rules {
  height: calc(100vh - 110px);
  overflow-y: auto;
  padding: 30px 20px 70px;
  .numberRule {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 30px;
    span {
      margin-right: 6px;
    }
  }
}
.footer {
  background: #edf5f9;
  width: 100%;
  height: 70px;
  border-top: 1px solid #efefef;
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .el-button {
    width: 400px;
    height: 40px;
  }
}
</style>
