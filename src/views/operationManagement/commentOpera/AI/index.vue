<template>
  <div :class="isCircle ? 'aiStyle' : ''">
    <div v-if="showCard"
         :class="calssName">
      <div class="header">
        <div class="back"
             @click="hide">
          <svg-icon name="icon-retract" /> <span>收起</span>
        </div>
      </div>
      <div class="tab">
        <div :class="active === 1 ? 'child act':'child'"
             @click="tabClick(1)">
          规则设置
        </div>
        <div :class="active === 2 ? 'child act':'child'"
             @click="tabClick(2)">
          我的风格
        </div>
        <div :class="active === 3 ? 'child act':'child'"
             @click="tabClick(3)">
          通用风格
        </div>
        <div :class="active === 4 ? 'child act':'child'"
             @click="tabClick(4)">
          历史记录
        </div>
      </div>
      <!-- 规则设置 -->
      <div v-show="active === 1">
        <div class="rules">
          <Rules ref="rules" />
        </div>
      </div>
      <div v-show="active === 2">
        <MyStyle :id="props.id"
                 @toRule="tabClick(1)"
                 @setComment="setComment" />
      </div>
      <div v-show="active === 3">
        <Style :id="props.id"
               :type="isCircle ? 'small' : ''"
               @toRule="tabClick(1)"
               @setComment="setComment" />
      </div>
      <div v-if="active === 4">
        <History
                :id="props.id"
                :type="isCircle ? 'small' : ''"
                 @setComment="setComment" />
      </div>
    </div>
  </div>
</template>
<script setup name="AI">
import { Box } from '@element-plus/icons-vue'
import Rules from './modules/rules.vue'
import MyStyle from './modules/myStyle.vue'
import Style from './modules/style.vue'
import History from './modules/history.vue'
import { ref } from 'vue'

const emit = defineEmits(['setComment'])
const showCard = ref(false)
const calssName = ref('Ai')
const active = ref(2)
const rules = ref(null)
const props = defineProps({
  id: {
    type: String || Number,
    default: ''
  },
  isCircle: {
    type: Boolean
  }
})

// 选择评论
function setComment (item) {
  emit('setComment', item)
}
function tabClick (val) {
  console.log(props.id)
  if (active.value === val) return
  if (val === 1) {
    rules.value.getRule()
  }
  active.value = val
}

function hide () {
  calssName.value = 'Ai hide'
  setTimeout(() => {
    showCard.value = false
    active.value = 2
  }, 300)
}
function show () {
  calssName.value = 'Ai'
  showCard.value = true
}
defineExpose({
  show,
  hide,
  showCard
})
</script>
<style lang="scss" scoped>
.aiStyle{
  .Ai{
    height:91.5vh;
    top: 6.5vh;
    right: 770px;
    border-radius: 6px;
    box-shadow: rgba(141, 141, 141, 0.5) 0px 0px 8px 0px;
  }
  .rules{
      height: calc(90vh - 110px) !important;
  }

}
.rules {
  height: calc(100vh - 110px);
  overflow-y: auto;
  padding: 30px 20px 70px;
}
.Ai.hide {
  animation: hide 0.3s linear both;
}
.Ai {
  width: 600px;
  height: 100vh;
  position: fixed;
  right: 800px;
  top: 0;
  background: url(@/assets/images/AI/ai-bg.png) no-repeat center top;
  background-size: cover;
  padding-bottom: 70px;
  animation: show 0.3s linear both;
  .header {
    width: 100%;
    height: 70px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    .back {
      width: 62px;
      height: 26px;
      border-radius: 4px;
      border: 1px solid #dbdbdb;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: 300;
      color: #333333;
      cursor: pointer;
      .svg-icon {
        width: 15px;
        height: 15px;
        margin-right: 4px;
      }
    }
  }
  .tab {
    padding: 0 6px;
    margin: 0 20px;
    width: 560px;
    height: 40px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .child {
      cursor: pointer;
      width: 133px;
      height: 28px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: 28px;
      text-align: center;
    }
    .child.act {
      background: #e6f2fe;
      border-radius: 2px;
      color: #0b82fd;
    }
  }
}
@keyframes show {
  0% {
    opacity: 0;
    transform: translateX(600px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes hide {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(600px);
  }
}
</style>
