<template>
  <div v-if="props.detail.doc_title" class="detail">
    <div class="title">
      {{ props.detail.doc_title }}
    </div>
    <div v-if="props.detail.doc_subtitle" class="title" style="font-size: 12px">
      {{ props.detail.doc_subtitle }}
    </div>
    <span v-if="props.detail.source" class="secondText">{{ props.detail.source }}</span>
    <span v-if="props.detail.author" class="secondText">{{ props.detail.author }}</span>
    <br v-if="props.detail.source || props.detail.author" />
    <span v-if="props.detail.read_total" class="secondText">稿件阅读数 {{ props.detail.read_total }}</span>
    <span v-if="props.detail.propagation_read_count" class="secondText">全网传播量 {{ props.detail.propagation_read_count }}</span>
    <br v-if="props.detail.propagation_read_count || props.detail.read_total" />
    <p v-if="props.detail.post_timeline" class="secondText">{{ props.detail.post_timeline }}</p>
    <div v-if="props.detail.doc_type === 9 || props.detail.doc_type === 10" class="vedio">
      <video :src="props.detail.video_url_with_watermark" controls />
    </div>
    <div v-if="props.detail.doc_type === 8" class="live">
      <img :src="props.detail.list_pics[0]" alt="" />
      <div class="fix">直播稿暂不支持播放</div>
      <!-- <video :src="props.detail.live_url" controls /> -->
    </div>
    <div v-if="props.detail.isRich" class="text circle" v-html="props.detail.content"></div>
    <div v-else>
      <div class="text" v-html="props.detail.content"></div>
      <!-- 多图 -->
      <div
        v-if="
          props.detail.list_pics &&
            props.detail.list_pics.length > 0 &&
            props.detail.list_pics.length > 1 &&
            (props.detail.doc_type === 2 || props.detail.doc_type === 12)
        "
        class="pics"
      >
        <div v-for="(item, index) in props.detail.list_pics" :key="item" class="child">
          <el-image
            style="width: 110px; height: 110px"
            :src="item"
            :zoom-rate="1"
            :preview-src-list="props.detail.list_pics"
            :initial-index="index"
            fit="cover"
          />
        </div>
      </div>
      <!-- 普通稿件 -->
      <div
        v-if="
          props.detail.list_pics &&
            props.detail.list_pics.length > 0 &&
            props.detail.list_pics.length === 1 &&
            props.detail.doc_type === 2
        "
        class="pic"
      >
        <el-image
          style="width: 350px"
          :src="props.detail.list_pics[0]"
          :zoom-rate="1"
          :preview-src-list="props.detail.list_pics"
          :initial-index="index"
          fit="cover"
        />
      </div>
      <!-- 图集 -->
      <div
        v-if="
          props.detail.album_image_list &&
            props.detail.album_image_list.length > 1 &&
            props.detail.doc_type === 4
        "
        class="pics"
      >
        <div v-for="(item, index) in props.detail.album_image_list" :key="item" class="child">
          <el-image
            style="width: 110px; height: 110px"
            :src="item"
            :zoom-rate="1"
            :preview-src-list="props.detail.album_image_list"
            :initial-index="index"
            fit="cover"
          />
        </div>
      </div>
      <!-- 短图文 -->
      <div
        v-if="
          props.detail.list_pics &&
            props.detail.list_pics.length > 0 &&
            props.detail.list_pics.length === 1 &&
            props.detail.doc_type === 12
        "
        class="pic"
      >
        <el-image
          style="width: 350px"
          :src="props.detail.list_pics[0]"
          :zoom-rate="1"
          :preview-src-list="props.detail.list_pics"
          :initial-index="index"
          fit="cover"
        />
      </div>
      <!-- 链接 -->
      <div v-if="props.detail.doc_type === 3" class="src">
        <iframe :src="props.detail.web_link" frameborder="0" width="350" height="615"></iframe>
      </div>
    </div>
  </div>
  <div v-else class="detail null">
    <img src="@/assets/images/circle/noDetail.png" alt="" />
    <div class="font">暂无内容</div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
</script>
<style></style>
<style lang="scss" scoped>
img {
  width: 100%;
  height: 100%;
  display: block;
}

.detail {
  width: 390px;
  height: 100%;
  background: #f9f9f9;
  overflow-y: auto;
  padding: 20px 20px 40px;
  overflow-x: hidden;

  .top {
    display: flex;
    width: 350px;

    .left {
      width: 42px;
      height: 42px;
      margin-right: 6px;
      border-radius: 50%;
      overflow: hidden;
    }

    .right {
      width: 302px;

      .name {
        font-size: 14px;
        color: #333333;
        line-height: 14px;
      }

      .des {
        margin-top: 6px;
        font-size: 12px;
        color: #8d8d8d;
      }
    }
  }

  .title {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    margin-bottom: 10px;
  }

  .secondText {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    margin-right: 15px;
    margin-bottom: 6px;
  }

  .text {
    width: 350px;
    margin-top: 15px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
    word-wrap: break-word;

    img {
      width: 350px;
      height: auto;
    }
  }

  .pics {
    width: 350px;
    display: flex;
    margin-top: 15px;
    flex-wrap: wrap;

    .child {
      width: 110px;
      height: 110px;
      background: #d8d8d8;
      margin-right: 6px;
      margin-bottom: 6px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .child:nth-child(3n) {
      margin-right: 0;
    }
  }

  .pic {
    width: 350px;
    border-radius: 6px;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      margin-bottom: 6px;
    }
  }

  .vedio {
    width: 350px;
    height: 468px;
    margin: 15px auto;
    border-radius: 6px;
    overflow: hidden;
    background: #000000;

    video {
      width: 100%;
      height: 100%;
    }
  }

  .live {
    margin-top: 10px;
    width: 351px;
    height: 196px;
    border-radius: 6px;
    overflow: hidden;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    .fix {
      width: 351px;
      height: 196px;
      position: absolute;
      left: 0;
      top: 0;
      background: rgba($color: #000000, $alpha: 0.6);
      font-size: 14px;
      color: #ffffff;
      text-align: center;
      padding-top: 168px;
    }
  }
}

.detail.null {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #9faabd;
  flex-direction: column;

  .font {
    margin-top: 10px;
    text-align: center;
  }

  img {
    width: 150px;
    height: 150px;
  }
}
</style>
<style>
.input-with-select2 .el-input-group__append {
  background-color: #0070f3;
  color: #fff;
  border-radius: 0px 6px 6px 0px;
}

.text.circle {
  img {
    width: 350px;
    height: auto;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 6px;
  }

  iframe {
    width: 350px;
    height: 615px;
  }

  video {
    width: 360px;
    height: 467px;
  }
}

.comment-box.inCircle {
  .el-divider--horizontal {
    margin: 15px 0;
  }
}
</style>
