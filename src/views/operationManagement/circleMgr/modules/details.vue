<template>
  <div v-if="props.detail.account_nick_name" class="detail">
    <div class="top">
      <div class="left">
        <img :src="props.detail.author_img" alt="" />
      </div>
      <div class="right">
        <div class="name">{{ props.detail.account_nick_name }}</div>
        <div class="des">{{ props.detail.cert_information }}</div>
        <div class="des">{{ props.detail.ip_address }}</div>
      </div>
    </div>
    <div v-if="props.detail.isRich" class="text circle" v-html="props.detail.content"></div>
    <div v-else>
      <div class="text" v-html="props.detail.content"></div>
      <div v-if="props.detail.list_pics.length > 0 && props.detail.list_pics.length > 1" class="pics">
        <div v-for="(item, index) in props.detail.list_pics" :key="item" class="child">
          <el-image
            style="width: 110px; height: 110px" :src="item" :zoom-rate="1"
            :preview-src-list="props.detail.list_pics" :initial-index="index" fit="cover"
          />
        </div>
      </div>
      <div
        v-if="props.detail.list_pics.length > 0 &&
          props.detail.list_pics.length === 1 &&
          props.detail.doc_type === 2
        " class="pic"
      >
        <el-image
          style="width: 350px" :src="props.detail.list_pics[0]" :zoom-rate="1"
          :preview-src-list="props.detail.list_pics" :initial-index="index" fit="cover"
        />
      </div>
      <div
        v-if="props.detail.list_pics.length > 0 &&
          props.detail.list_pics.length === 1 &&
          props.detail.doc_type === 12
        " class="pic"
      >
        <el-image
          style="width: 350px" :src="props.detail.list_pics[0]" :zoom-rate="1"
          :preview-src-list="props.detail.list_pics" :initial-index="index" fit="cover"
        />
      </div>
      <div v-if="props.detail.doc_type === 9 || props.detail.doc_type === 10" class="vedio">
        <video :src="props.detail.video_url_with_watermark" controls />
      </div>
    </div>
  </div>
  <div v-else class="detail null">
    <img src="@/assets/images/circle/noDetail.png" alt="" />
    <div class="font">暂无内容</div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return { }
    }
  }
})

</script>
<style>
.circleMain a:-webkit-any-link {
  color: #0b82fd;
  text-decoration: none;
}
</style>
<style lang="scss" scoped>
img {
  width: 100%;
  height: 100%;
  display: block;
}

.detail {
  width: 390px;
  height: 100%;
  background: #f9f9f9;
  overflow-y: auto;
  padding: 20px 20px 40px;
  overflow-x: hidden;

  .top {
    display: flex;
    width: 350px;

    .left {
      width: 42px;
      height: 42px;
      margin-right: 6px;
      border-radius: 50%;
      overflow: hidden;
    }

    .right {
      width: 302px;

      .name {
        font-size: 14px;
        color: #333333;
        line-height: 14px;
      }

      .des {
        margin-top: 6px;
        font-size: 12px;
        color: #8d8d8d;
      }
    }
  }

  .text {
    width: 350px;
    margin-top: 15px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
    word-wrap: break-word;

    img {
      width: 350px;
      height: auto;
    }
  }

  .pics {
    width: 350px;
    display: flex;
    margin-top: 15px;
    flex-wrap: wrap;

    .child {
      width: 110px;
      height: 110px;
      background: #d8d8d8;
      margin-right: 6px;
      margin-bottom: 6px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .child:nth-child(3n) {
      margin-right: 0;
    }
  }

  .pic {
    width: 350px;
    border-radius: 6px;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      margin-bottom: 6px;
    }
  }

  .vedio {
    width: 350px;
    height: 468px;
    margin: 15px auto;
    border-radius: 6px;
    overflow: hidden;
    background: #000000;

    video {
      width: 100%;
      height: 100%;
    }
  }
}

.detail.null {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #9faabd;
  flex-direction: column;

  .font {
    margin-top: 10px;
    text-align: center;
  }

  img {
    width: 150px;
    height: 150px;
  }
}
</style>
<style>
.input-with-select2 .el-input-group__append {
  background-color: #0070f3;
  color: #fff;
  border-radius: 0px 6px 6px 0px;
}

.text.circle {
  img {
    width: 350px;
    height: auto;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 6px;
  }

  iframe {
    width: 350px;
    height: 615px;
  }

  video {
    width: 360px;
    height: 467px;
  }
}

.comment-box.inCircle {
  .el-divider--horizontal {
    margin: 15px 0;
  }
}
</style>
