<template>
  <AbsoluteContainer>
    <page-main>
      <HeadModel :search-data="searchData" @search="handleSearch" @clear="handleClean" />
      <Table ref="table" :table="tableData" :search="searchData" />
    </page-main>
  </AbsoluteContainer>
</template>
<script setup name="Answer">
import Table from '@/components/Table/index.vue'
import { nextTick, ref } from 'vue'
import HeadModel from './modules/headModule.vue'
import { requests } from '@/api/system/user.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const route = useRoute(), router = useRouter()
const { proxy } = getCurrentInstance()
// 搜索参数声明
const searchData = ref({
  name: '',
  status: '',
  time: []
})
const tableData = ref({
  path: '/quizActivity/list',
  columns: [
    {
      label: '序号',
      name: 'auto_pk',
      width: 100
    },
    {
      label: '操作项',
      name: 'name'
    },
    {
      label: '操作人',
      name: 'name',
      width: '100'
    },
    {
      label: '操作时间',
      name: 'name',
      width: 200
    }
  ]

})
// 搜索
function handleSearch() {
  let search = Object.assign({}, searchData.value)
  if (search.time && search.time.length) {
    search.beginTime = search.time[0]
    search.endTime = search.time[1]
  }
  delete search.time
  proxy.$refs.table.handleSearch(search)
}
// 重置搜索条件
function handleClean() {
  searchData.value = {
    name: '',
    status: '',
    time: []
  }
}
// 上下线
function handleSwitch(row) {
  requests.update({
    id: row.id,
    status: row.status
  }).then(res => {
    ElMessage({
      message: res.message,
      type: 'success'
    })
    proxy.$refs.table.init()
  }).catch(res => {
    proxy.$refs.table.init()
  })
}
// 修改
function handleEdit(row) {
  router.push({ path: '/answer/add-answer', query: { id: row.id } })
}

</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
}
</style>
