<template>
  <div class="head-box">
    <div class="left">
    </div>
    <div class="right">
      <div class="moudle">
        <el-date-picker
          v-model="searchData.time"
          clearable
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="到"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </div>
      <div class="moudle">
        <el-input
          v-model="props.searchData.name"
          placeholder="请输入用户或姓名"
          class="input-with-select"
        >
          <template #append>
            <el-button type="primary">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>
<script setup name="headModel">
import { Search } from '@element-plus/icons'
const route = useRoute(), router = useRouter()

// 组件传参声明方式
const props = defineProps({
  searchData: Object
})
// 组件抛出事件
const emit = defineEmits(['search', 'add', 'clear'])
const selectList = ref({
  actStatusList: [
    {
      value: -1,
      label: '未开始'
    },
    {
      value: 1,
      label: '进行中'
    },
    {
      value: 0,
      label: '已结束'
    }
  ],
  upStatusList: [
    {
      value: 0,
      label: '下线'
    },
    {
      value: 1,
      label: '上线'
    }
  ]
})
function handlSearch() {
  emit('search')
}
function handlClear() {
  emit('clear')
}
function handleAdd() {
  router.push('/answer/add-answer')
}
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-end;
  .left {
    display: flex;
    flex-wrap: wrap;
    margin-right: 40px;
    min-width: 20%;
  }
  .right {
    display: flex;
    flex-wrap: wrap;
    .moudle {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;
      .label {
        flex-shrink: 0;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
