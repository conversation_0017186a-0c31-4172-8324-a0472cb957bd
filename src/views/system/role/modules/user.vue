<template>
  <div>
    <el-drawer
      v-model="drawer" direction="rtl" title="用户详情" :size="600" close-on-click-moda="false" @open="open" @close="close"
    >
      <el-table :data="data.list" style="width: 100%" size="small" height="95%">
        <el-table-column type="index" width="50" />
        <el-table-column prop="user_name" label="用户名" />
        <el-table-column prop="name" label="姓名" />
      </el-table>

      <el-pagination
        small background class="pagin" :current-page="data.current"
        :page-size="data.size" layout="total,prev, pager, next,jumper" :total="data.total"
        @current-change="handleCurrentChange"
      />
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { ElMessage } from 'element-plus'
import { requests } from '@/api/system/role.js'
import { ref } from 'vue'

const drawer = ref(false)
const data = ref({
  role_id: '',
  list: [],
  current: 1,
  total: 0,
  size: 50
})

function init () {
  requests.getUserList({
    role_id: data.value.role_id,
    size: data.value.size,
    current: data.value.current
  }).then(res => {
    data.value.total = res.data.page.total
    data.value.list = res.data.page.records
  })
}
// 打开弹窗
function open () {
  init()
}
// 关闭弹窗
function close () {
  data.value = {
    role_id: '',
    list: [],
    current: 1,
    total: 0,
    size: 50
  }
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
defineExpose({
  data,
  drawer
})

</script>
<style lang="scss" scope>
.pagin{
  margin: 10px  auto 0;
  display: flex;
  justify-content: center;
}
</style>
