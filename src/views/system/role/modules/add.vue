<template>
  <div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      :title="isEdit ?'编辑角色': '添加角色'"
      :size="600"
      close-on-click-moda="false"
      @close="close"
      @open="open"
      @closed="closed"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item
          label="角色名称"
          prop="name"
        >
          <el-input
            v-model.trim="ruleForm.name"
            maxlength="10"
            placeholder="请输入角色名称，最多10个字符"
            clearable
          />
        </el-form-item>
        <el-form-item
          v-if="!isEdit"
          label="类型"
          prop="type"
        >
          <el-radio-group v-model="ruleForm.type">
            <el-radio label="超管" />
            <el-radio label="产品租户管理员" />
            <el-radio label="普通角色" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input
            v-model.trim="ruleForm.desc"
            resize="none"
            :rows="3"
            type="textarea"
            maxlength="50"
            placeholder="请输入角色描述，最多50个字符"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button @click="close(ruleFormRef)">取消</el-button>
          <el-button
            type="primary"
            @click="sureClick(ruleFormRef)"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { ElMessage } from 'element-plus'
import {
  requests
} from '@/api/system/role.js'
import {
  ref
} from 'vue'
const emit = defineEmits(['init'])
const ruleFormRef = ref(null)
const ruleForm = ref({
  name: '',
  type: '',
  desc: ''
})
// 验证信息
const rules = ref({
  name: [{
    required: true,
    message: '请输入角色名称',
    trigger: 'blur'
  }],
  type: [{
    required: true,
    message: '请选择类型',
    trigger: 'change'
  }]
})
// 传入的数据
const props = defineProps({
  roleData: Object
})
const drawer = ref(false)
const isEdit = ref(false)
// 确定
const sureClick = formEl => {
  // 表单验证
  if (!formEl) return
  formEl.validate((valid, fields) => {
    if (valid) {
      //   编辑角色
      if (isEdit.value) {
        requests.updateRole({
          id: props.roleData.id,
          ...ruleForm.value
        })
          .then(res => {
            console.log('res', res)
            ElMessage({
              message: '更改成功！',
              type: 'success'
            })
            drawer.value = false
            emit('init')

          })
          .catch(error => {
            console.log(error)
          })
        //   添加角色
      } else {
        //   文字转换为数字
        switch (ruleForm.value.type) {
          case '超管':
            ruleForm.value.type = 1
            break
          case '产品租户管理员':
            ruleForm.value.type = 2
            break
          case '普通角色':
            ruleForm.value.type = 3
            break
        }
        requests.createRole(ruleForm.value)
          .then(res => {
            ElMessage({
              message: '添加成功！',
              type: 'success'
            })
            drawer.value = false
            emit('init')
          })
          .catch(error => {
            console.log(error)
          })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 打开弹窗时
const open = () => {
  console.log(isEdit.value)
  //   点了编辑
  if (isEdit.value == true) {
    console.log(isEdit.value, props.roleData.name)

    ruleForm.value.name = props.roleData.name
    ruleForm.value.desc = props.roleData.desc

  }
}
// 取消弹窗时
const close = formEl => {
  ruleForm.value = {
    name: '',
    type: '',
    desc: ''
  }
  if (!formEl) return
  formEl.resetFields()
  drawer.value = false
  isEdit.value = false
}
// 弹窗动画结束时
const closed = () => {
  isEdit.value = false
}
defineExpose({
  drawer,
  isEdit
})

</script>
