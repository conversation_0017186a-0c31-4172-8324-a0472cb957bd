<template>
  <el-drawer v-model="drawer" direction="rtl" title="关联权限" size="50%">
    <!-- <el-input v-model="input" placeholder="输入关键词" /> -->
    <div class="main">
      <el-tabs type="border-card">
        <el-tab-pane label="操作权限">
          <div v-for="(item, index) in data.permission_list" :key="index" class="box">
            <h3>
              <el-checkbox-group v-model="data.permission_id">
                <el-checkbox :label="item.id" size="large" @change="secondChange(item)"
                  >{{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </h3>
            <div v-for="(item1, index1) in item.children" :key="index1" class="body">
              <h5>
                <el-checkbox-group v-model="data.permission_id">
                  <el-checkbox :label="item1.id" @change="secondChange(item1)"
                    >{{ item1.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </h5>
              <div>
                <el-checkbox-group v-model="data.permission_id">
                  <el-checkbox
                    v-for="(item2, index2) in item1.children"
                    :key="index2"
                    :label="item2.id"
                    @change="secondChange(item2)"
                  >
                    {{ item2.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="频道权限" id="load">
          <el-checkbox size="large" v-model="checkedVal" border @change="allClick(checkedVal)"
            >全选
          </el-checkbox>
          <div v-for="(item, index) in frequencyData.category_list" :key="index" class="box box1">
            <h3>
              <el-checkbox-group v-model="frequencyData.categorys">
                <el-checkbox
                  :label="item.category_id"
                  size="large"
                  @change="frequencyChange(0, 0, item)"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </h3>
            <div v-for="(item1, index1) in item.children" :key="index1" class="body">
              <h5>
                <el-checkbox-group v-model="frequencyData.categorys">
                  <el-checkbox
                    :label="item1.category_id"
                    @change="frequencyChange(0, item.category_id, item1)"
                  >
                    {{ item1.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </h5>
              <div class="bodyBox">
                <el-checkbox-group v-model="frequencyData.categorys">
                  <el-checkbox
                    v-for="(item2, index2) in item1.children"
                    :key="index2"
                    :label="item2.category_id"
                    @change="frequencyChange(item.category_id, item1.category_id, item2)"
                  >
                    {{ item2.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="圈子权限" id="load">
          <el-checkbox size="large" v-model="checkedVal2" border @change="checkAllCircle"
            >全选
          </el-checkbox>
          <div class="circleBox">
            <el-checkbox
              v-model="item.check"
              :label="item.name"
              size="large"
              v-for="item in circleData.list"
              :key="item.id"
              @change="checkCircel(item)"
              style="margin: 5px 0"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>
<script setup name="edit">
import { requests } from '@/api/system/role.js'
import { ref, onMounted } from 'vue'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
const drawer = ref(false)
const loading = ref(false)

const roleId = ref(null)
// 传入的数据
const props = defineProps({
  roleData: Object
})
// 全选状态
const checkedVal = ref(false)
// 操作权限数据
const data = ref({
  permission_list: null,
  permission_id: null
})
// 频道权限数据
const frequencyData = ref({
  categorys: [],
  category_list: []
})
const checkedVal2 = ref(false)
const circleData = ref({
  list: [],
  choise: []
})
// 角色权限列表
function rolePermissionList(id) {
  // 存一下角色id
  roleId.value = id
  requests
    .rolePermissionList({
      role_id: id
    })
    .then(res => {
      let list = []
      data.value = res.data
      data.value.permission_id.forEach((val, index, arr) => {
        list.push(val)
      })
      data.value.permission_id = list
    })
    .catch(error => {
      console.log(error)
    })
}
// 频道权限列表 接口
function roleListCategoryTree(id) {
  frequencyData.value.categorys = []
  // frequencyData.value.category_list=[]
  // 存一下角色id
  roleId.value = id
  requests
    .roleListCategoryTree({
      role_id: id
    })
    .then(res => {
      frequencyData.value.category_list = res.data.role_category_list
      isCheckAll()
      for (let i = 0; i < frequencyData.value.category_list.length; i++) {
        if (frequencyData.value.category_list[i].permission == 1) {
          frequencyData.value.categorys.push(frequencyData.value.category_list[i].category_id)
        }
        for (let k = 0; k < frequencyData.value.category_list[i].children.length; k++) {
          if (frequencyData.value.category_list[i].children[k].permission == 1) {
            frequencyData.value.categorys.push(
              frequencyData.value.category_list[i].children[k].category_id
            )
          }

          for (
            let a = 0;
            a < frequencyData.value.category_list[i].children[k].children.length;
            a++
          ) {
            if (frequencyData.value.category_list[i].children[k].children[a].permission == 1) {
              frequencyData.value.categorys.push(
                frequencyData.value.category_list[i].children[k].children[a].category_id
              )
            }
          }
        }
      }
    })
    .catch(error => {
      console.log(error)
    })
}
// 判断是否全选
function isCheckAll() {
  let number = frequencyData.value.category_list.length
  let num = 0
  frequencyData.value.category_list.forEach(v => {
    if (v.permission === 1) {
      num++
    }
  })
  if (num === number) {
    checkedVal.value = true
  } else {
    checkedVal.value = false
  }
}
// 角色权限选中
function secondChange(row) {
  let existence = data.value.permission_id.indexOf(row.id)
  let select_flag
  // 取消
  if (existence < 0) {
    select_flag = false
    // 选中
  } else {
    select_flag = true
  }
  requests
    .rolePermissionSelect({
      role_id: roleId.value,
      select_flag: select_flag,
      permission_id: row.id
    })
    .then(res => {
      ElMessage({
        type: 'success',
        message: '操作成功！'
      })
      rolePermissionList(roleId.value)
    })
    .catch(error => {
      ElMessage({
        type: 'error',
        message: '关联失败！'
      })
      rolePermissionList(roleId.value)

      console.log(error)
    })
}
//频道权限 全选
function allClick(val) {
  if (val == true) {
    for (let i = 0; i < frequencyData.value.category_list.length; i++) {
      frequencyData.value.categorys.push(frequencyData.value.category_list[i].category_id)
      for (let k = 0; k < frequencyData.value.category_list[i].children.length; k++) {
        frequencyData.value.categorys.push(
          frequencyData.value.category_list[i].children[k].category_id
        )
        for (let a = 0; a < frequencyData.value.category_list[i].children[k].children.length; a++) {
          frequencyData.value.categorys.push(
            frequencyData.value.category_list[i].children[k].children[a].category_id
          )
        }
      }
    }
    // 处理一下数据
    let categorys = frequencyData.value.categorys.toString()
    setTimeout(() => {
      updateRoleCategory(categorys)
    })
  } else {
    frequencyData.value.categorys = []
    // 处理一下数据
    let categorys = frequencyData.value.categorys.toString()
    setTimeout(() => {
      updateRoleCategory(categorys)
    })
  }
}
// 频道权限选中时
function frequencyChange(val, val1, row) {
  // ElLoading.service(document.querySelector('#load'))
  // 判断存不存在已选
  let existence = frequencyData.value.categorys.indexOf(row.category_id)
  // 取消 当前及下一级都取消
  if (existence < 0) {
    for (let k = 0; k < row.children.length; k++) {
      if (frequencyData.value.categorys.indexOf(row.children[k].category_id) > -1) {
        frequencyData.value.categorys.splice(
          frequencyData.value.categorys.indexOf(row.children[k].category_id),
          1
        )
      }
      for (let a = 0; a < row.children[k].children.length; a++) {
        if (frequencyData.value.categorys.indexOf(row.children[k].children[a].category_id) > -1) {
          frequencyData.value.categorys.splice(
            frequencyData.value.categorys.indexOf(row.children[k].children[a].category_id),
            1
          )
        }
      }
    }
    // 处理一下数据
    let categorys = frequencyData.value.categorys.toString()
    setTimeout(() => {
      updateRoleCategory(categorys)
    })

    // 选中 当前及下一级都选中
  } else {
    for (let k = 0; k < row.children.length; k++) {
      if (frequencyData.value.categorys.indexOf(row.children[k].category_id) < 0) {
        frequencyData.value.categorys.push(row.children[k].category_id)
      }
      for (let a = 0; a < row.children[k].children.length; a++) {
        if (frequencyData.value.categorys.indexOf(row.children[k].children[a].category_id) < 0) {
          frequencyData.value.categorys.push(row.children[k].children[a].category_id)
        }
      }
    }
    // 处理一下数据
    let categorys = frequencyData.value.categorys.toString()
    if (val != 0) {
      categorys += `, ${val}`
      frequencyData.value.categorys.push(val)
    }
    console.log(val, val1)

    if (val1 != 0) {
      categorys += `, ${val1}`
      frequencyData.value.categorys.push(val1)
    }
    console.log(categorys)
    setTimeout(() => {
      updateRoleCategory(categorys)
    })
  }
}
// 频道权限提交
function updateRoleCategory(categorys) {
  requests
    .updateRoleCategory({
      role_id: roleId.value,
      categorys: categorys
    })
    .then(res => {
      if (res.code == 0) {
        ElMessage({
          type: 'success',
          message: '操作成功！'
        })
        roleListCategoryTree(roleId.value)
      } else {
        roleListCategoryTree(roleId.value)
      }
    })
    .catch(error => {
      ElMessage({
        type: 'error',
        message: '关联失败！'
      })
      roleListCategoryTree(roleId.value)

      console.log(error)
    })
}
// 获取圈子列表
function getCircleList() {
  requests
    .getCircleList({ role_id: roleId.value, size: 1000 })
    .then(res => {
      let num = 0
      res.data.circle_list.forEach(v => {
        if (v.permission === 1) {
          num++
          v.check = true
        } else {
          v.check = false
        }
      })
      if (num === res.data.circle_list.length) {
        checkedVal2.value = true
      } else {
        checkedVal2.value = false
      }
      circleData.value.list = res.data.circle_list
    })
    .catch(v => {
      console.log(v)
    })
}
// 选择圈子
function checkCircel(val) {
  let data = []
  circleData.value.list.forEach(v => {
    if (v.check) {
      data.push(v.id)
    }
  })
  requests.updateCircle({ role_id: roleId.value, circles: data.join(',') }).then(res => {
    getCircleList()
  })
}
// 圈子全选
function checkAllCircle(val) {
  let data = []
  if (val) {
    circleData.value.list.forEach(v => {
      data.push(v.id)
    })
  }
  requests.updateCircle({ role_id: roleId.value, circles: data.join(',') }).then(res => {
    getCircleList()
  })
}
defineExpose({
  drawer,
  roleId,
  rolePermissionList,
  roleListCategoryTree,
  getCircleList
})
</script>
<style lang="scss" scoped>
.main {
  height: calc(100vh - 150px);
  overflow-y: auto;
  .circleBox {
    width: 100px;
    margin-top: 10px;
  }
  .box {
    padding-bottom: 50px;

    &.box1 {
      padding-bottom: 0;

      h3 {
        color: #409eff;
      }

      h5 {
        padding: 0;
        margin: 0 0 10px;
      }

      .body {
        width: 100%;
        min-height: 50px;
        border: 1px solid transparent;
        padding: 0 30px;
        margin-top: 10px;

        .bodyBox {
          padding-left: 60px;
        }
      }
    }

    h3 {
      color: #409eff;
    }

    h5 {
      padding: 0;
      margin: 0 0 10px;
    }

    .body {
      width: 100%;
      min-height: 50px;
      border: 1px solid #d9d9d9;
      padding: 10px;
      margin-top: 10px;
    }
  }
}
</style>
