<template>
  <AbsoluteContainer>
    <page-main>
      <HeadModel
        :search-data="searchData"
        @search="handleSearch"
        @add="handleAdd"
      />
      <Table ref="table" :table="tableData" :search="searchData" />
    </page-main>
    <Edit ref="edit" :role-data="roleData" />
    <Add ref="add" :role-data="roleData" @init="init" />
    <User ref="user" />
  </AbsoluteContainer>
</template>
<script setup name="Role">
import User from './modules/user.vue'
import Table from '@/components/Table/index.vue'
import { Delete } from '@element-plus/icons-vue'
import { nextTick, ref } from 'vue'
import HeadModel from './modules/headModule.vue'
import Edit from './modules/edit.vue'
import Add from './modules/add.vue'
import { requests } from '@/api/system/role.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const route = useRoute(), router = useRouter()
const { proxy } = getCurrentInstance()
// 搜索参数声明
const searchData = ref({
  name: ''
})
// 传给添加角色页面的数据
const roleData = ref(null)
const tableData = ref({
  path: 'admin_role/role_list',
  backName: 'role_list',

  columns: [
    {
      label: '序号',
      name: 'auto_pk'
    },
    {
      label: '角色名称',
      name: 'name'
    },
    {
      label: '描述',
      name: 'desc'
    },
    {
      label: '绑定数',
      name: 'bind_count',
      link: true,
      func: showUser
    }
  ],
  operationWidth: 300,
  operation: [
    {
      label: '编辑',
      fun: handleEdit,
      auth: 'admin_role:update_role'
    },
    {
      label: '设置权限',
      fun: handleSetUp,
      auth: 'admin_role:role_permission_list'
    },
    {
      label: '删除',
      fun: handleDel,
      auth: 'admin_role:delete_role'
    }
  ]
})

const add = ref(null)
const edit = ref(null)
const user = ref(null)
onMounted(() => {})
// 搜索
function handleSearch () {
  let search = Object.assign({}, searchData.value)
  if (search.time && search.time.length) {
    search.beginTime = search.time[0]
    search.endTime = search.time[1]
  }
  delete search.time
  proxy.$refs.table.handleSearch(search)
}
// 查看绑定用户
function showUser (val) {
  user.value.data.role_id = val.id
  user.value.drawer = true
}
// 设置权限
function handleSetUp (row) {
  console.log('role_id', row.id.toString())
  roleData.value = row
  edit.value.drawer = true
  edit.value.roleId = row.id
  edit.value.rolePermissionList(row.id)
  edit.value.roleListCategoryTree(row.id)
  edit.value.getCircleList(row.id)
}
// 编辑
function handleEdit (row) {
  console.log(row)
  add.value.drawer = true
  //   如果是编辑 isEdit=true
  roleData.value = row
  add.value.isEdit = true
}
// 删除
function handleDel (row) {
  ElMessageBox.confirm('确定要删除该角色吗？', '注意', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  })
    .then(() => {
      requests
        .deleteRole({
          role_id: row.id
        })
        .then(res => {
          proxy.$refs.table.init()
          ElMessage({
            type: 'success',
            message: '删除成功！'
          })
        })
        .catch(error => {
          console.log(error)
        })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 新增
function handleAdd () {
  add.value.drawer = true
}

function init () {
  proxy.$refs.table.init()
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
}
</style>
