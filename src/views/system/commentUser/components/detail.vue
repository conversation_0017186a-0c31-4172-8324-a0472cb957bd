<template>
  <div class="comment-detail" v-loading="loading" shadow="hover">
    <div class="card-header">
        <h2 class="card-title">详情</h2>
        <el-button class="close-button" icon="Close" @click="closeDrawer" />
      </div>
    <div v-if="detailInfo">
      <section class="info-section">
        <h3 class="section-title">稿件信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="稿件ID" label-class-name="my-label">{{ detailInfo.article_id }}</el-descriptions-item>
          <el-descriptions-item label="稿件标题" label-class-name="my-label">{{ detailInfo.article_title }}</el-descriptions-item>
        </el-descriptions>
      </section>

      <section class="info-section">
        <h3 class="section-title">评论信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="评论ID" label-class-name="my-label">{{ detailInfo.comment_id }}</el-descriptions-item>
          <el-descriptions-item label="评论内容">
            <div v-html="detailInfo.content"></div>
            <div v-if="detailInfo.pic_url" class="image-container">
              <img
                class="comment-image"
                :src="detailInfo.pic_url"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="评论时间">{{ detailInfo.opt_at_str }}</el-descriptions-item>
        </el-descriptions>
      </section>

      <section class="info-section">
        <h3 class="section-title">评论任务</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="评论人" label-class-name="my-label">{{ detailInfo.comment_user_name }}</el-descriptions-item>
          <el-descriptions-item label="IP" label-class-name="my-label-small">{{ detailInfo.ip }}</el-descriptions-item>
          <el-descriptions-item label="设备号" label-class-name="my-label">{{ detailInfo.device_mode }}</el-descriptions-item>
          <el-descriptions-item label="版本" label-class-name="my-label-small">{{ detailInfo.app_version }}</el-descriptions-item>
          <el-descriptions-item label="发布平台" label-class-name="my-label">{{ detailInfo.platform }}</el-descriptions-item>
        </el-descriptions>
      </section>
    </div>
  </div>
</template>

<script setup>
import { Close } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { requests } from '@/api/business/auditManagement'
import { useDrawer } from '@/hooks/useDrawer'

const { closeDrawer } = useDrawer()

const props = defineProps({
  commentId: {
    type: String,
    required: true
  }
})

const loading = ref(false)
const detailInfo = ref(null)

const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString()
}

const fetchDetailInfo = async () => {
  loading.value = true
  try {
    const response = await requests.getCommentUserDetail({ comment_id: props.commentId })
    if (response.code === 0) {
      detailInfo.value = response.data.commentUserInfo
    } else {
      ElMessage.error('获取评论用户详情失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取评论用户详情失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchDetailInfo()
})
</script>

<style scoped lang="scss">
.comment-detail {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    padding-left: 20px;
  }

  .card-title {
    margin: 0;
    font-size: 20px;
    color: #303133;
  }

  .close-button {
    padding: 8px;
    font-size: 20px;
    color: #333333;
    border: none; /* 移除边框 */
    background: none; /* 移除背景 */
    cursor: pointer;
  }

  .info-section {
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 40px;
    .section-title {
      font-size: 18px;
      color: #72767b;
      margin-bottom: 16px;
      border-left: 4px solid #46a5e2;
      padding-left: 10px;
    }
  }

  :deep(.el-descriptions) {
    .el-descriptions__label {
      font-weight: bold;
    }

    .my-label {
      white-space: nowrap;
      min-width: 90px;
      width: 90px;
    }

    .my-label-small {
      min-width: 60px;
      width: 60px;
    }

    .el-descriptions__content {
      min-width: 150px;
    }
  }

  .image-container {
    padding: 10px 0;
    text-align: left;
  }

  .comment-image {
    height: 200px;
    width: auto;
  }
}
</style>
