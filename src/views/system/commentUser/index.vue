<template>
  <AbsoluteContainer>
    <page-main>
      <div class="comment-user-management">
        <div class="head-box">
          <div class="left">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :editable="false"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime"
              style="width: 380px; margin-right: 10px"
            />
          </div>
          <div class="right">
            <el-input
              v-model="searchData.comment_id"
              placeholder="请输入评论ID"
              style="width: 300px"
            >
              <template #append>
                <el-button :icon="Search" @click="handSearch">搜索</el-button>
              </template>
            </el-input>
          </div>
        </div>

        <div class="table-container">
          <el-table v-loading="loading" :data="data.records" size="default" :height="data.height">
            <el-table-column type="index" label="序号" width="80" />
            <el-table-column prop="comment_id" label="评论ID" width="240" />
            <el-table-column prop="comment_user_name" label="评论人" width="240" />
            <el-table-column prop="ip" label="IP地址" width="180" />
            <el-table-column prop="platform" label="发布平台" width="180" />
            <el-table-column prop="device_mode" label="设备号" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button
                  text
                  size="small"
                  style="color: #0b82fd; padding: 5px 5px"
                  @click="openDetail(scope.row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="page">
          <el-pagination
            background
            small
            layout="total, sizes, prev, pager, next, jumper"
            :total="data.total"
            :page-size="data.size"
            :current-page="data.current"
            :page-sizes="[10, 20, 50, 100]"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          >
          </el-pagination>
        </div>
      </div>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { requests } from '@/api/business/auditManagement'
import Detail from './components/detail.vue'
import { useDrawer } from '@/hooks/useDrawer'

const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
const time = ref('')
const loading = ref(false)
const { openDrawer } = useDrawer()

const openDetail = (row) => {
  openDrawer(Detail, '600px', {
    commentId: row.comment_id
  })
}

const searchData = ref({
  comment_id: '',
  start_time: '',
  end_time: ''
})

const data = ref({
  height: null,
  total: 0,
  size: 10,
  current: 1,
  records: []
})

onMounted(() => {
  fetchCommentUsers()
})

const handSearch = () => {
  if (time.value) {
    searchData.value.start_time = time.value[0]
    searchData.value.end_time = time.value[1]
  } else {
    searchData.value.start_time = ''
    searchData.value.end_time = ''
  }
  data.value.current = 1
  fetchCommentUsers()
}

const fetchCommentUsers = async () => {
  loading.value = true
  try {
    const response = await requests.getCommentUserList({
      ...searchData.value,
      size: data.value.size,
      current: data.value.current
    })
    data.value.records = response.data.commentUserInfolist.records
    data.value.total = response.data.commentUserInfolist.total
    data.value.current = response.data.commentUserInfolist.current
  } catch (error) {
    console.log(error)
    ElMessage.error('获取评论用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  fetchCommentUsers()
}

const handleCurrentChange = val => {
  data.value.current = val
  fetchCommentUsers()
}
</script>

<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  align-items: center;

  .left,
  .right {
    display: flex;
    align-items: center;
  }
}

.table-container {
  height: calc(100vh - 200px);
  overflow: auto;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

::v-deep .el-input-group__append {
  background-color: #0b82fd;
  border-color: #0b82fd;
  color: white;
}

::v-deep .el-button {
  border: none;
  background: transparent;
  color: white;
}
</style>
