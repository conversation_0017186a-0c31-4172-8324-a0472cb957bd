<template>
  <el-button v-auth="'reports:comment_reports'" type="primary" style="margin: -5px 0 10px 10px" @click="reportTypeShow = true">
    添加类型
  </el-button>
  <el-dialog
    v-model="reportTypeShow"
    title="新增类型"
    width="450px"
    align-center
    :show-close="false"
  >
    <el-input
      v-model="typeName"
      show-word-limit
      placeholder="请输入类型"
      maxlength="10"
      style="margin: 15px 0"
    />
    <template #footer>
      <div style="flex: auto">
        <el-button @click="hanldeCancel"> 取消 </el-button>
        <el-button type="primary" @click="handleAdd"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
  <ul class="typeUl">
    <li v-for="item of typeList" :key="item.id">
      {{ item.type_name }}
      <el-icon
        class="custom-icon"
        @click="handelTypeDel(item.id)"
        style="cursor: pointer"
      >
        <CircleCloseFilled />
      </el-icon>
    </li>
  </ul>
  <el-form-item class="reportNum">
          同一用户每日举报次数
     <el-input-number v-model="limiter"  :min="1" @change="handleChange" style="width: 150px; margin-left:20px"/>
 </el-form-item>
</template>

<script setup name='reportType'>
import { ref, onBeforeMount, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { requests } from "@/api/business/commentOpera";
const reportTypeShow = ref(false);
const typeName = ref("");
const typeList = ref([]);
const limiter = ref(null)
// 删除类型
const handelTypeDel = (id) => {
  requests.reportsDel({ id }).then((res) => {
    if (res.code == 0) {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      init();
    }
  });
};
// 修改举报次数
const handleChange = ()=>{
    requests.detail_report_edit({limiter:limiter.value}).then(res=>{
        if (res.code == 0) {
      ElMessage({
        message: "举报次数修改成功",
        type: "success",
      });
      init();
    }
    })
}
// 类型列表
const init = () => {
  requests.reportsList().then((res) => {
    if (res.code == 0) {
      typeList.value = res.data.type_list;
    }
  });
  requests.detail_report({code:'REPORTS'}).then(res=>{
    if (res.code == 0) {
      limiter.value = res.data.limiter;
    }
  })
};
// 新增类型
const handleAdd = () => {
  requests.create({ type_name: typeName.value }).then((res) => {
    if (res.code == 0) {
      ElMessage({
        message: "新增类型成功",
        type: "success",
      });
      reportTypeShow.value = false;
      typeName.value = "";
      init()
    }
  });
};
// 取消
const hanldeCancel = () => {
  typeName.value = "";
  reportTypeShow.value = false;
};
onMounted(() => {
  init();
});
</script>
<style lang='scss' scoped>
.reportNum{
    clear: both;
    margin-left: 10px;
    margin-top: 10px;
    overflow: hidden;
}
.typeUl {
  margin: 0 0 0 -40px;
  overflow: hidden;
  li {
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #ececec;
    padding: 0 20px;
    list-style: none;
    margin: 10px;
    float: left;
    border-radius: 5px;
    font-size: 14px;
    position: relative;
    .custom-icon {
      position: absolute;
      top: -5px;
      right: -5px;
    }
  }
}
</style>