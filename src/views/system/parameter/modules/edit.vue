<template>
  <div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      title="参数编辑"
      :size="600"
      close-on-click-moda="false"
      @close="close"
      @open="open"
      @closed="closed"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
        status-icon
        :size="500"
      >
        <h4>热门评论策略</h4>
        <el-form-item label-width="0px">
          自动推送热门评论：稿件评论点赞数达
          <el-input-number v-model="ruleForm.auto_like_count" :value-on-clear="0" :min="0" /> 次
        </el-form-item>
        <el-form-item label-width="0px">
          稿件热门评论数量限制:
          <el-input-number v-model="ruleForm.limit_top_count" :value-on-clear="0" :min="0" /> 条
        </el-form-item>
        <!-- <h4>删除重复评论策略</h4>
        <el-form-item label-width="0px">
          删除重复评论：同一用户同一稿件，
          <el-input-number v-model="ruleForm.repeat_second" :value-on-clear="10" :min="10" :max="1800" />
          秒内发布重复评论超过
          <el-input-number v-model="ruleForm.repeat_limit" :value-on-clear="0" :min="0" /> 条后，超出的重复评论自动删除。
        </el-form-item> -->
        <el-divider />
        <!-- <h4>评论自动化排序</h4>
        <el-form-item label-width="0px">
          潮客频道排序：<el-switch v-model="ruleForm.score_sort" />
        </el-form-item>
        <el-divider /> -->
        <h4>敏感词评论设置</h4>
        <el-form-item label-width="0px">
          自动删除：
          <el-radio-group v-model="ruleForm.is_auto_delete" class="ml-4">
            <el-radio
              v-for="(item, index) of radioData"
              :key="index"
              :label="item.key"
              size="large"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-divider />
        <h4 v-auth="'comment:editor_replay'">评论回复设置</h4>
        <!-- 昵称 -->
        <el-form-item v-auth="'comment:editor_replay'" label="昵称显示：" label-width="86px">
          <el-input v-model="ruleForm.replay_nickname" style="width: 200px" maxlength="10" />
        </el-form-item>
        <!-- 上传头像 -->
        <el-form-item v-auth="'comment:editor_replay'" label="头像显示：" label-width="86px">
          <div>
            <img
              :src="imgURL"
              class="avatar"
              style="width: 120px; height: 120px; border-radius: 50%"
            />
            <!-- <el-upload style="width:120px;height:120px;position: relative;" class="avatar-uploader"
                            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                            :show-file-list="false" :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                            <img v-if="ruleForm.replay_portrait_url" :src="ruleForm.replay_portrait_url" class="avatar" style="width:120px;height:120px;" />
                            <el-icon class="avatar-uploader-icon">
                                <Plus />
                            </el-icon>
                        </el-upload>
                        <p style="font-size:12px;color:#999;">尺寸推荐400*400，支持jpg、png、jpeg</p> -->
          </div>
        </el-form-item>
        <!-- 归属地 -->
        <!-- <el-form-item label-width="0" v-auth="'comment:editor_replay'">
                    归属地：
                    <el-radio-group v-model="ruleForm.show_location" class="ml-4">
                        <el-radio v-for="(item,index) of showData" :key="index" :label="item.key" size="large">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                    <el-input v-if="ruleForm.show_location=='YES'" v-model="ruleForm.location" style="width: 100px;margin-left:12px;" />
                </el-form-item> -->
      </el-form>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="sureClick"> 确认 </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { ref } from 'vue'
import { requests } from '@/api/system/parameter'
import {
  ElMessage
  // UploadProps
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
// 传入的数据
const props = defineProps({
  editData: Object
})
const imgURL = ref('https://app-stc.zjol.com.cn/logo/cxw-logo.png')
const ruleFormRef = ref(null)
const show_location = ref('YES')
const ruleForm = ref({
  code: 'TOP',
  auto_like_count: '',
  limit_top_count: '',
  is_auto_delete: null,
  // repeat_second: '',
  repeat_limit: '',
  // 1.0.9
  replay_nickname: null, // 昵称
  replay_portrait_url: null, // 头像显示：
  location: null, // 归属地
  show_location: null, // 是否显示
  score_sort: null // 潮客排序
})
const radioData = ref([
  {
    key: 0,
    label: '是'
  },
  {
    key: 1,
    label: '否'
  }
])
// 归属地
const showData = ref([
  {
    key: 'NO',
    label: '不显示'
  },
  {
    key: 'YES',
    label: '显示'
  }
])
const emit = defineEmits(['init'])
const drawer = ref(false)
const isEdit = ref(false)
onMounted(() => {
  //   console.log('传入的值', props.data)
})
const sureClick = () => {
  console.log('值', ruleForm.value)
  // 表单验证
  requests
    .update(ruleForm.value)
    .then(res => {
      console.log('res', res)
      ElMessage({
        message: '更改成功！',
        type: 'success'
      })
      drawer.value = false
      emit('init')
    })
    .catch(error => {
      console.log(error)
    })
}
// 打开弹窗时
const open = () => {
  console.log('打开弹窗')
  if (isEdit.value == true) {
    ruleForm.value = {
      code: 'TOP',
      ...props.editData
    }
  }
}
// 取消弹窗时
const close = formEl => {
  drawer.value = false
}
// 弹窗动画结束时
const closed = () => {
  isEdit.value = false
}
const handleAvatarSuccess = (response, uploadFile) => {
  console.log(uploadFile)
  ruleForm.value.replay_portrait_url = URL.createObjectURL(uploadFile.raw)
}
// 上传限制
const beforeAvatarUpload = rawFile => {
  if (
    rawFile.type !== 'image/jpeg' &&
    rawFile.type !== 'image/png' &&
    rawFile.type !== 'image/jpg'
  ) {
    ElMessage.error('请上传正确格式的图片！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    console.log(1243)
    ElMessage.error('上传的图片过大!')
    return false
  }
  return true
}
defineExpose({
  drawer,
  isEdit
})
</script>
<style lang="scss" scoped>
.avatar-uploader .el-upload {
  width: 120px;
  height: 120px;
  position: relative;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  //   background: #ebebeb;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #ebebeb;
}
</style>
