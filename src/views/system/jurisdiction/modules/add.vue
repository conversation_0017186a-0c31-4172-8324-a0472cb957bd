<template>
  <div>
    <el-drawer v-model="drawer" direction="rtl" :title="isEdit ?'编辑权限': '添加权限'" :size="600" close-on-click-moda="false">
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        status-icon
      >
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择权限类型">
            <el-option label="菜单" :value="0" />
            <el-option label="功能" :value="1" />
            <el-option label="频道" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="功能类型" prop="operation_type">
          <el-select v-model="ruleForm.operation_type	" placeholder="请选择功能类型">
            <el-option label="操作权限" :value="1" />
            <el-option label="数据权限" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 0" label="排序值">
          <el-input v-model.trim="ruleForm.sort_number" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item label="父级ID" prop="parent_id">
          <el-input v-model="ruleForm.parent_id" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="ruleForm.name" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="ruleForm.code" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item label="权限路径" prop="url">
          <el-input v-model.trim="ruleForm.url" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 0" label="图标">
          <el-input v-model.trim="ruleForm.icon" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item v-if="ruleForm.type === 0" label="组件路径">
          <el-input v-model.trim="ruleForm.component" maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button @click="drawer = false">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submitForm('ruleFormRef')">确认</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup name="add">
import { requests } from '@/api/system/jurisdiction.js'
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()

const ruleForm = ref({
  parent_id: '',
  name: '',
  code: '',
  url: '',
  type: '',
  operation_type: '',
  icon: '',
  component: '',
  sort_number: ''
})
const editForm = ref({
  parent_id: '',
  name: '',
  code: '',
  url: '',
  type: '',
  operation_type: '',
  icon: '',
  component: '',
  sort_number: ''

})
const emit = defineEmits(['success'])

const rules = ref({
  parent_id: [
    { required: true, message: '请输入父级ID', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入权限路径', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ],
  operation_type: [
    { required: true, message: '请选择功能类型', trigger: 'change' }
  ]
})
const loading = ref(false)
const drawer = ref(false)
const isEdit = ref(false)
const ruleFormRef = ref(null)
watch(drawer, (newValue, oldValue) => {
  if (!newValue) {
    ruleFormRef.value.resetFields()
    isEdit.value = false
  } else {
    nextTick(() => {
      if (isEdit.value) {
        ruleForm.value = editForm.value
      }
    })
  }
})
// const form = ref(null)
function submitForm(formName) {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let funName
      if (isEdit.value) {
        funName = 'updatePermission'
      } else {
        funName = 'createPermission'
      }
      //   formModel.value.exchangeRatio = formModel.value.exchangeRatio1 + ':' + formModel.value.exchangeRatio2
      ruleForm.value.project_id = 0
      requests[funName](ruleForm.value).then(res => {
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        drawer.value = false
        loading.value = false
        emit('success')
      }).catch(v => {
        loading.value = false
      })
    } else {
      return false
    }
  })
}
defineExpose({
  drawer,
  isEdit,
  editForm
})
</script>
