<template>
  <div class="head-box">
    <div class="left">
      <el-button type="primary" color="#0B82FD" @click="handleAdd"
        v-auth="'admin_permission:create_permission'">添加权限</el-button>
    </div>
    <div class="right">
      <el-input v-model="props.searchData.name" placeholder="请输入关键词" style="margin-right: 10px" class="input-with-select"
        clearable @keyup.enter="handlSearch">
        <template #prepend>
          <el-select v-model="props.searchData.type" clearable placeholder="请选择" style="width: 100px"
            @change="typeChange">
            <el-option v-for="item in selectList.upStatusList" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </template>
        <template #append>
          <el-button type="primary" :icon="Search" color="#0B82FD" style="display: flex; align-items: center"
            @click="handlSearch">
            搜索
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>
<script setup name="headModel">
const route = useRoute(), router = useRouter()

// 组件传参声明方式
const props = defineProps({
  searchData: Object
})
// 组件抛出事件
const emit = defineEmits(['search', 'add', 'clear'])
const selectList = ref({

  upStatusList: [
    {
      value: 0,
      label: '菜单'
    },
    {
      value: 1,
      label: '功能'
    },
    {
      value: 2,
      label: '频道'
    }
  ]
})
function handlSearch() {
  emit('search')
}
function handlClear() {
  emit('clear')
}
function handleAdd() {
  emit('add')
}
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-end;

  .left {
    display: flex;
    flex-wrap: wrap;
    margin-right: 40px;
    min-width: 20%;
  }

  .right {
    display: flex;
    flex-wrap: wrap;

    .moudle {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;

      .label {
        flex-shrink: 0;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
