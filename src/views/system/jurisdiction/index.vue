<template>
  <AbsoluteContainer>
    <page-main>
      <HeadModel :search-data="searchData" @search="handleSearch" @add="handleAdd" />
      <Table ref="table" :table="tableData" :search="searchData" />
    </page-main>
    <Add ref="add" @success="init" />
  </AbsoluteContainer>
</template>
<script setup name="Role">
import Table from '@/components/Table/index.vue'
import {
  nextTick,
  ref
} from 'vue'
import HeadModel from './modules/headModule.vue'
import Add from './modules/add.vue'
import { requests } from '@/api/system/jurisdiction.js'
import {
  Delete
} from '@element-plus/icons-vue'
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
const route = useRoute(), router = useRouter()
const store = useStore()
const { proxy } = getCurrentInstance()
// 搜索参数声明
const searchData = ref({
  type: null,
  name: ''
})
// 传给添加角色页面的数据
const roleData = ref(null)
const tableData = ref({
  path: 'admin_permission/permission_list',
  backName: 'permission_list',
  columns: [
    {
      label: 'ID',
      name: 'id'
    },
    {
      label: '父级ID',
      name: 'parent_id'
    },
    {
      label: '权限名称',
      name: 'name'
    },
    {
      label: '权限编码',
      name: 'code'
    },
    {
      label: '路径',
      name: 'url'
    },
    {
      label: '组件',
      name: 'component'
    },

    {
      label: '权限类型',
      name: 'type',
      options: [
        {
          label: 0,
          value: '菜单'
        },
        {
          label: 1,
          value: '功能'
        },
        {
          label: 2,
          value: '频道'
        }
      ]
    },
    {
      label: '权限功能类型',
      name: 'operation_type',
      options: [
        {
          label: 1,
          value: '操作权限'
        },
        {
          label: 2,
          value: '数据权限'
        }
      ]
    },
    {
      label: '创建时间',
      name: 'created_at',
      time: true
    },
    {
      label: '排序值',
      name: 'sort_number'
    }
  ],
  operationWidth: 150,
  operation: [
    {
      label: '编辑',
      fun: handleEdit,
      auth: 'admin_permission:update_permission'
    },
    {
      label: '删除',
      fun: handleDel,
      auth: 'admin_permission:delete_permission'
    }

  ]
})

const add = ref(null)
onMounted(() => {
  //   console.log(3333333333, store.getters['menu/permissions'])
})
// 搜索
function handleSearch() {
  let search = Object.assign({}, searchData.value)
  proxy.$refs.table.handleSearch(search)
}

// 修改
function handleEdit(row) {
  add.value.drawer = true
  //   如果是编辑 isEdit=true
  let newData = Object.assign({}, row)
  add.value.editForm.id = newData.id
  add.value.editForm.parent_id = newData.parent_id
  add.value.editForm.name = newData.name
  add.value.editForm.code = newData.code
  add.value.editForm.url = newData.url
  add.value.editForm.type = newData.type
  add.value.editForm.operation_type = newData.operation_type
  add.value.editForm.icon = newData.icon
  add.value.editForm.sort_number = newData.sort_number
  add.value.editForm.component = newData.component
  add.value.isEdit = true
}
// 删除
function handleDel(row) {

  ElMessageBox.confirm(
    '确定要删除该权限吗？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  }).then(() => {

    requests.deletePermission({
      id: row.id
    }).then(res => {
      proxy.$refs.table.init()
      ElMessage({
        type: 'success',
        message: '删除成功！'
      })
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '已取消！'
    })
  })

}
// 新增
function handleAdd() {
  add.value.drawer = true
}

function init() {
  proxy.$refs.table.init()
}

</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
}
</style>
