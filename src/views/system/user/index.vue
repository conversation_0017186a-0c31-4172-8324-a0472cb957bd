<template>
  <AbsoluteContainer>
    <page-main>
      <HeadModel
        :search-data="searchData"
        @search="handleSearch"
        @clear="handleClean"
      />
      <Table
        ref="table"
        :table="tableData"
        :search="searchData"
      />
    </page-main>
    <Edit
      ref="edit"
      :user-data="userData"
      @init="init"
    />
  </AbsoluteContainer>
</template>
<script setup name="Answer">
import Table from '@/components/Table/index.vue'
import { nextTick, ref } from 'vue'
import HeadModel from './modules/headModule.vue'
import Edit from './modules/edit.vue'
import { requests } from '@/api/system/user.js'
import { ElMessage, ElMessageBox } from 'element-plus'
const route = useRoute(), router = useRouter()
const { proxy } = getCurrentInstance()
// 搜索参数声明
const searchData = ref({
  keyword: '',
  status: ''
})
const edit = ref(null)
// 传入编辑界面的数据
const userData = ref(null)
const tableData = ref({
  path: 'admin_user/user_list',
  backName: 'admin_user_list',
  columns: [
    {
      label: '序号',
      name: 'auto_pk'
    },
    {
      label: '用户名',
      name: 'user_name'
    },
    {
      label: '姓名',
      name: 'name'
    },
    {
      label: '角色名称',
      name: 'roles'
    },
    {
      label: '状态',
      name: 'status',
      switch: true,
      fun: handleSwitch,
      auth: 'user:status'
    }
  ],
  operationWidth: 150,
  operation: [
    {
      label: '关联角色',
      fun: handleEdit,
      auth: 'user:update'
    }
  ]
})
onMounted(() => {

})
// 搜索
function handleSearch () {
  let search = Object.assign({}, searchData.value)
  proxy.$refs.table.handleSearch(search)
}
// 上下线
function handleSwitch (row) {
  let message
  if (row.status == 0) {
    message = '停用成功！'
  } else {
    message = '启用成功！'

  }
  requests.updateStatus({
    id: row.id
  }).then(res => {
    ElMessage({
      message: message,
      type: 'success'
    })
    proxy.$refs.table.init()
  }).catch(res => {
    proxy.$refs.table.init()
  })
}
// 修改
function handleEdit (row) {
  userData.value = row
  edit.value.drawer = true
  edit.value.roleListAll()
  //   router.push({ path: '/answer/add-answer', query: { id: row.id } })
}
function init () {
  console.log('刷新一下')
  proxy.$refs.table.init()
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
}
</style>
