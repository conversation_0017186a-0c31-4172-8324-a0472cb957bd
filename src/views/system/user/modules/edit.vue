<template>
  <el-drawer v-model="drawer"
             direction="rtl"
             @close="close"
             title="角色关联">
    <el-input v-model="keyWord"
              placeholder="输入关键词筛选" />
    <div class="center">
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="item in CroleList"
                     :key="item.id"
                     :label="item.id"> {{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </div>

    <template #footer>
      <div style="flex: auto;">
        <el-button @click="close">取消</el-button>
        <el-button type="primary"
                   @click="sureClick">
          确认
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup name="edit">
import {
requests
} from '@/api/system/user.js';
import {
ElMessage
} from 'element-plus';
import {
computed, ref
} from 'vue';
const emit = defineEmits(['init'])

// 传入的 当前用户数据
const props = defineProps({
  userData: Object
})
const drawer = ref(false)
const checkList = ref([])
const keyWord = ref('')
const roleList = ref([{
  id: 1,
  name: '123'
},])
onMounted(() => {

})
const CroleList = computed(() => {
  if (keyWord.value.trim()) {
    let newArr = roleList.value.filter(v => v.name.indexOf(keyWord.value) != -1)
    return newArr
  }
  return roleList.value
})
// 角色关联列表
const roleListAll = () => {
  checkList.value = []
  requests.roleListAll().then(res => {
    roleList.value = res.data.role_list
    // 对应已经存在的角色打上勾
    for (let i = 0; i < props.userData.user_role_list.length; i++) {
      checkList.value.push(props.userData.user_role_list[i].role_id)
    }
  }).catch(res => {

  })
}
// 点击取消
function close () {
//   checkList.value = []
//   for (let i = 0; i < props.userData.user_role_list.length; i++) {
//     checkList.value.push(props.userData.user_role_list[i].role_id)
//   }
drawer.value=false
}
// 点击确定
function sureClick () {
  console.log('点击确定')
  requests.updateUserRole({
    id: props.userData.id,
    role_ids: checkList.value.toString(),
    name: '',
    phone_number: '',
    email: '',
    password: '',
    sex: ''
  }).then(res => {
    ElMessage({
      message: '保存成功！',
      type: 'success'
    })
    drawer.value=false
    emit('init')
  }).catch(res => {

  })
}
defineExpose({
  drawer,
  roleListAll
})

</script>
<style scoped>
.center {
  height: calc(100vh - 280px);
  overflow-y: auto;
}
</style>
