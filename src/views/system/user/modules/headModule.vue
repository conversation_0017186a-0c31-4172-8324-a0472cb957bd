<template>
  <div class="head-box">
    <div class="left">
    </div>
    <div class="right">
      <div class="moudle">
        <el-select
          v-model="props.searchData.status"
          clearable

          style="width: 150px;"
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in selectList.upStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="moudle">
        <el-input
          v-model="props.searchData.keyword"
          placeholder="请输入用户或姓名"
          class="input-with-select" clearable
        >
          <template #append>
            <el-button type="primary" @click="handlSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>
<script setup name="headModel">
import { Search } from '@element-plus/icons'
const route = useRoute(), router = useRouter()

// 组件传参声明方式
const props = defineProps({
  searchData: Object
})
// 组件抛出事件
const emit = defineEmits(['search', 'add', 'clear'])
const selectList = ref({
  actStatusList: [
    {
      value: -1,
      label: '未开始'
    },
    {
      value: 1,
      label: '进行中'
    },
    {
      value: 0,
      label: '已结束'
    }
  ],
  upStatusList: [
    {
      value: 0,
      label: '停用'
    },
    {
      value: 1,
      label: '启用'
    }
  ]
})
function handlSearch() {
  emit('search')
}
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-end;
  .left {
    display: flex;
    flex-wrap: wrap;
    margin-right: 40px;
    min-width: 20%;
  }
  .right {
    display: flex;
    flex-wrap: wrap;
    .moudle {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;
      .label {
        flex-shrink: 0;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
