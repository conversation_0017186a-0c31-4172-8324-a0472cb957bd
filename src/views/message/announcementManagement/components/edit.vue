<script setup>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, onMounted, ref, shallowRef, watch } from 'vue'
import { requests } from '@/api/system/notification'
import dayjs from 'dayjs'
import { ElLoading, ElMessage } from 'element-plus'
import { DomEditor } from '@wangeditor/editor'
import { useAnnouncement } from '@/hooks/useAnnouncement'
import { useNotifications } from '@/hooks/useNotifications'

const { fetchAllNotifications } = useNotifications()

const props = defineProps({
  announcementId: {
    type: String,
    required: false,
    default: ''
  }
})

onMounted(() => {
  console.log(props.announcementId)
  if (props.announcementId) {
    // 编辑公告
    requests.getSingleAnnouncementDetailApi(props.announcementId).then(res => {
      const data = res.data
      inputTitle.value = data.notice_system.notice_title
      valueHtml.value = data.notice_system.notice_content
      inputName.value = data.notice_system.subscribe
    })
  }
})

const emit = defineEmits('onReturnListClick')
const onReturnClick = () => {
  emit('onReturnListClick')
}

const inputTitle = ref('')
const inputName = ref('')
const toolbarConfig = {
  excludeKeys: ['group-video', 'fullScreen', 'insertTable', 'codeBlock']
}
const editorConfig = { placeholder: '请输入内容...' }
const editorRef = shallowRef()
const valueHtml = ref('')
const TEXT_LIMIT = 5000

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
  // 打印所有菜单项
  // const allMenus = editor.getAllMenuKeys()
  // console.log('All menus:', allMenus)
  // console.log(editor)
  const editorConfig = editor.getConfig()
  // 配置上传图片
  editorConfig.MENU_CONF['uploadImage'] = {
    // 自定义上传
    async customUpload(file, insertFn) {
      // 检查图片类型
      if (!/image\/\w+/.test(file.type)) {
        ElMessage.error('请确保文件为图像类型')
        return
      }
      // 检查图片大小
      if (file.size > 1024 * 1024 * 5) {
        ElMessage.error('图片大小不能超过 5MB')
        return
      }
      // const url = 'https://www.baidu.com/img/bd_logo1.png'
      const formData = new FormData()
      formData.append('file', file) // 将文件添加到表单中
      console.log(formData, 'formData')
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '上传中...',
        background: 'rgba(0, 0, 0, 0.6)'
      })
      requests
        .uploadRichTextImage(formData)
        .then(res => {
          if (res.code === 0) {
            const imgUrl = res.data.url
            insertFn(imgUrl, '', '')
          } else {
            ElMessage.error('图片上传失败')
          }
        })
        .finally(() => {
          loadingInstance.close()
        })
    }
  }

  setTimeout(() => {
    const toolbar = DomEditor.getToolbar(editor)
    const curToolbarConfig = toolbar.getConfig()
    console.log(curToolbarConfig.toolbarKeys) // 当前菜单排序和分组
  }, 1000)
}

let errorMessageInstance = null
watch(
  () => valueHtml.value,
  () => {
    const editor = editorRef.value
    const text = editor.getText()
    if (text.length > TEXT_LIMIT) {
      if (errorMessageInstance) {
        errorMessageInstance.close()
      }
      errorMessageInstance = ElMessage.error(`公告内容不能超过${TEXT_LIMIT}字`)
    }
  }
)

const checkContent = () => {
  if (!inputTitle.value) {
    ElMessage.error('请输入公告标题')
    return false
  }
  if (!inputName.value) {
    ElMessage.error('请输入署名')
    return false
  }
  const text = editorRef.value.getText()
  // if (!text || text.trim() === '') {
  //   ElMessage.error('请输入公告内容')
  //   return false
  // }
  if (text.length > TEXT_LIMIT) {
    ElMessage.error(`公告内容不能超过${TEXT_LIMIT}字`)
    return false
  }
  return true
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const { showAnnouncementLocal } = useAnnouncement()
const onPreviewClick = () => {
  console.log(valueHtml.value)
  if (!checkContent()) return
  showAnnouncementLocal({
    title: inputTitle.value,
    content: valueHtml.value,
    author: inputName.value,
    time: dayjs().format('YYYY-MM-DD HH:mm:ss')
  })
}

const onSaveClick = () => {
  console.log('保存')
  if (!checkContent()) return
  if (props.announcementId) {
    // 编辑公告
    requests
      .updateAnnouncementApi(
        props.announcementId,
        inputTitle.value,
        valueHtml.value,
        inputName.value
      )
      .then(() => {
        console.log('编辑成功')
        onReturnClick()
      })
  } else {
    // 新建公告
    requests.createAnnouncementApi(inputTitle.value, valueHtml.value, inputName.value).then(() => {
      console.log('新建成功')
      onReturnClick()
    })
  }
}

const onSaveAndPublishClick = () => {
  console.log('保存并发布')
  if (!checkContent()) return
  if (props.announcementId) {
    // 编辑公告
    requests
      .updateAndPublishAnnouncementApi(
        props.announcementId,
        inputTitle.value,
        valueHtml.value,
        inputName.value
      )
      .then(() => {
        console.log('编辑成功')
        onReturnClick()
        fetchAllNotifications()
      })
  } else {
    // 新建公告
    requests
      .createAndPublishAnnouncementApi(inputTitle.value, valueHtml.value, inputName.value)
      .then(() => {
        console.log('新建成功')
        onReturnClick()
        fetchAllNotifications()
      })
  }
}
</script>

<template>
  <div class="edit-container">
    <div class="top-button">
      <el-button type="primary" @click="onReturnClick">返回列表</el-button>
    </div>
    <div class="line-container">
      <div class="left-container">
        <span>公告标题</span>
      </div>
      <div class="title-input">
        <el-input
          v-model="inputTitle"
          maxlength="20"
          show-word-limit
          placeholder="请输入公告标题"
        ></el-input>
      </div>
    </div>
    <div class="line-container">
      <div class="left-container">
        <span>发布署名</span>
      </div>
      <div class="title-input">
        <el-input v-model="inputName" maxlength="20" show-word-limit placeholder="请输入署名">
        </el-input>
      </div>
    </div>
    <div class="line-container item-start">
      <div class="left-container">
        <div>公告内容</div>
      </div>
      <div class="editor-content" style="border: 1px solid #ccc">
        <Toolbar
          style="border-bottom: 1px solid #ccc"
          :editor="editorRef"
          :default-config="toolbarConfig"
          mode="default"
        />
        <Editor
          v-model="valueHtml"
          style="height: 500px; overflow-y: hidden"
          :default-config="editorConfig"
          @onCreated="handleCreated"
        />
      </div>
    </div>
    <div class="bottom-button">
      <el-button @click="onPreviewClick">预览</el-button>
      <el-button @click="onSaveClick">保存</el-button>
      <el-button type="primary" @click="onSaveAndPublishClick">保存并发布</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edit-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
}

.line-container {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: start;
}

.item-start {
  align-items: start;
}
.left-container {
  width: 100px;
  font-weight: bold;
  white-space: nowrap;
  flex-shrink: 0;
}
.title-input {
  flex: 1;
  max-width: 500px;
}
.bottom-button {
  margin-left: 100px;
  margin-top: 40px;
  max-width: 1000px;
  display: flex;
  justify-content: center;
}
.editor-content {
  display: block;
  max-width: 1000px;
}
</style>
