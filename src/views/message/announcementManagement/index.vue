<script setup>
import { onMounted, reactive, ref } from 'vue'
import Edit from './components/edit.vue'
import { requests } from '@/api/system/notification'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAnnouncement } from '@/hooks/useAnnouncement'
import { useNotifications } from '@/hooks/useNotifications'

let data = reactive({
  total: 0, // 总条目数
  size: 10, // 每页显示条目
  current: 1 // 当前页码
})
const loading = ref(false)
const mode = ref('list')
const editData = ref(null)

const list = ref([])

const { fetchAllNotifications } = useNotifications()

const getList = () => {
  loading.value = true
  requests
    .getSystemAnnouncementListApi(data.current, data.size)
    .then(res => {
      list.value = res.data.page.records
      const { total, size, current } = res.data.page
      data = { total, size, current }
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  getList()
})

// 新建公告
const onCreateClick = () => {
  editData.value = null
  mode.value = 'edit'
}
// 编辑页面返回列表
const onReturnListClick = () => {
  mode.value = 'list'
  getList()
}
// 发布公告
const onPublishClick = async row => {
  console.log('发布点击', row)
  ElMessageBox.confirm('确定发布该条消息？', '公告发布', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
    .then(async () => {
      await requests.publishOrRevokeAnnouncementApi(row.id, 1)
      getList()
      ElMessage({
        type: 'success',
        message: '发布成功'
      })
      fetchAllNotifications()
    })
    .catch(e => {
      console.log(e)
    })
}

const onDeleteClick = async row => {
  console.log('删除点击', row)
  ElMessageBox.confirm('确定删除该条消息？', '删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(async () => {
      await requests.deleteAnnouncementApi(row.id)
      getList()
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
    })
    .catch(e => {
      console.log(e)
    })
}

const onEditClick = row => {
  console.log('编辑', row)
  editData.value = row
  mode.value = 'edit'
}

const onRevokeClock = async row => {
  console.log('撤回点击', row)
  ElMessageBox.confirm('确定撤回该条消息？', '撤回', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await requests.publishOrRevokeAnnouncementApi(row.id, 0)
      getList()
      ElMessage({
        type: 'success',
        message: '撤回成功'
      })
      fetchAllNotifications()
    })
    .catch(e => {
      console.log(e)
    })
}

const { showAnnouncementWithApi } = useAnnouncement()
const onPreviewClick = row => {
  console.log('预览', row)
  showAnnouncementWithApi(row.id)
}

// 选择每页几条
const handleSizeChange = val => {
  data.size = val
  getList()
}
// 点击分页器
const handleCurrentChange = val => {
  data.current = val
  getList()
}
</script>

<template>
  <AbsoluteContainer>
    <page-main v-if="mode === 'list'">
      <div class="top-button">
        <el-button type="primary" @click="onCreateClick">新建</el-button>
      </div>
      <div class="table-container">
        <el-table v-loading="loading" :data="list" size="default">
          <el-table-column prop="id" label="序号" width="120" type="index" />
          <el-table-column prop="notice_title" label="公告标题" />
          <el-table-column label="发布状态" width="160">
            <template v-slot="{ row }">
              <span v-if="row.state === 0" style="color: #87d068">待发布</span>
              <span v-if="row.state === 1" style="color: #333">已发布</span>
            </template>
          </el-table-column>
          <el-table-column prop="subscribe" label="发布署名" width="160" />
          <el-table-column prop="update_at_string" label="最后修改时间" width="180" />
          <el-table-column prop="announce_name" label="操作人" width="220" />
          <el-table-column v-slot="scope" label="操作" width="180" fixed="right">
            <el-button
              v-if="scope.row.state === 0"
              style="margin-left: 0; padding: 5px 5px"
              text
              size="small"
              @click="onPublishClick(scope.row)"
            >
              <span class="operate-text" style="color: #0b82fd">发布</span>
            </el-button>
            <el-button
              v-if="scope.row.state === 0"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="onDeleteClick(scope.row)"
            >
              <span class="operate-text" style="color: #0b82fd">删除</span>
            </el-button>
            <el-button
              v-if="scope.row.state === 0"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="onEditClick(scope.row)"
            >
              <span class="operate-text" style="color: #0b82fd">编辑</span>
            </el-button>

            <el-button
              v-if="scope.row.state === 1"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="onRevokeClock(scope.row)"
            >
              <span class="operate-text" style="color: #0b82fd">撤回</span>
            </el-button>

            <el-button
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="onPreviewClick(scope.row)"
            >
              <span class="operate-text" style="color: #0b82fd">预览</span>
            </el-button>
          </el-table-column>
        </el-table>
      </div>
      <div class="page">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.total"
          :page-size="data.size"
          :current-page="data.current"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </page-main>
    <page-main v-if="mode === 'edit'">
      <Edit :announcement-id="editData?.id" @onReturnListClick="onReturnListClick" />
    </page-main>
  </AbsoluteContainer>
</template>

<style scoped lang="scss">
.top-button {
  margin-bottom: 14px;
}
.table-container {
  width: 100%;
  height: 80vh;
  overflow-y: scroll;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 20px;
}

.operate-text {
  font-size: 14px;
}
</style>
