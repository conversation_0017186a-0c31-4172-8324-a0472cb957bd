<template>
  <AbsoluteContainer>
    <div class="Main">
      <div class="leftInfo">
        <div class="iconList">
          <div
            v-for="item in iconList"
            :key="item.id"
            :class="['icon', item.name, { iconHover: item.name === currentIcon }]"
            @click="handleIcon(item.name)"
          >
            <span>{{ iconNames[item.name] }}</span>
            <i v-show="item.flag"></i>
          </div>
        </div>
        <div class="textList">
          <div class="title">告警</div>
          <div
            v-for="(tab, index) in tabs"
            :id="tab.id"
            :key="index"
            v-auth="tab.auth"
            :class="{ 'tabTit tabSelect': activeTab === index, tabTit: activeTab !== index }"
            @click="selectTab(index)"
          >
            <span :class="'def icon' + (index + 1)"></span>
            {{ tab.name }}
            <i v-if="index == 0 && countNum > 0">{{ countNum }}</i>
          </div>
          <!-- <div class="title">评论</div>
          <div class="tabTit"><span class="def icon4"></span>评论运营</div> -->
        </div>
      </div>

      <div class="centerInfo">
        <div
          v-if="activeTab == 0"
          :class="!handerFoucs ? 'hander' : 'handerHover'"
          @click="hanldeHander"
        >
          <div class="left">
            <div class="title">告警评论汇总</div>
            <div class="info">集中展示所有告警的用户评论</div>
          </div>
          <div class="right"></div>
        </div>
        <el-date-picker
          v-if="activeTab != 0"
          v-model="time"
          style="width: 240px; float: left; margin-bottom: 20px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :editable="false"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :default-time="defaultTime"
          @change="handleDateChange"
          @blur="handleBlur"
        >
        </el-date-picker>
        <div
          v-loading="leftLoading"
          class="scrollList"
          :class="{ 'active-tab-height': activeTab === 0 }"
        >
          <div
            v-for="(item, index) in summaryList"
            :key="item.id"
            :class="item.cover_url ? 'list' : 'listNoImg'"
            :style="item.isClick ? { background: '#EEF5FE' } : {}"
            @click="handleSunmmaryListClick(item.article_id, index, item.share_url)"
          >
            <img
              v-if="item.cover_url"
              :src="
                interfaceTab == 'weibo'
                  ? 'https://image.baidu.com/search/down?url=' + item.cover_url
                  : item.cover_url
              "
            />
            <div class="tit">
              {{ item.title }}
            </div>

            <div class="bottomRig">
              <div class="time">{{ item.article_publish_date_str }}</div>
              <div class="wring">
                <span></span>
                {{
                  activeTab === 0
                    ? item.alarm_new_count
                    : activeTab === 1
                    ? item.alarm_processed_count
                    : item.alarm_ignored_count
                }}
              </div>
            </div>
          </div>
          <!-- 分页组件 -->
          <div v-if="totalItemsNum > 20" class="page">
            <el-pagination
              background
              small
              layout="total, sizes, prev, pager, next"
              :total="totalItemsNum"
              :page-size="pageSizeNum"
              :current-page="currentPageNum"
              :pager-count="3"
              :page-sizes="[20, 50]"
              @current-change="summary_handlePageChange"
              @size-change="summary_handleSizeChange"
            />
          </div>
        </div>
      </div>
      <div class="rightInfo">
<div
          v-if="activeTab == 0"
          v-show="!isBatchShow && commentsList.length"
          class="batch"
          @click="isBatchShow = true"
        >
          <span v-auth="'socialPlatform:batch'"></span>批量处理
        </div>
        <div v-show="isBatchShow" class="batchList">
          <span class="cancel" @click="cancel()">取消</span>
          <span v-show="newSelectedArray.length > 1" @click="handleBatch(null, null, 1)">批量设为已处理</span>
          <span v-show="newSelectedArray.length > 1" @click="handleBatch(null, null, 2)">批量设为忽略</span>
        </div>
        <div v-show="commentsList.length" class="infoText" @click="infoShow = !infoShow"><span></span>说明</div>
        <div v-if="activeTab == 0" v-show="isBatchShow" class="select">
          <el-checkbox
            v-model="passAllBut"
            class="checkAll"
            label=""
            size="large"
            @change="handelPassAll"
          />
          选择（{{ newSelectedArray.length }}）
        </div>
        <div
          ref="listRef"
          v-loading="rightLoading"
          :class="activeTab == 0 ? 'contList' : 'contList2'"
        >
          <div v-for="item of commentsList" :key="item.id" class="list">
            <el-checkbox
              v-if="activeTab == 0"
              v-show="isBatchShow"
              v-model="item.isSelect"
              class="checkBlue"
              label=""
              size="large"
              @change="handelPass"
            />
            <img
              v-if="item.avatar"
              class="contImg"
              :src="
                interfaceTab == 'weibo'
                  ? 'https://image.baidu.com/search/down?url=' + item.avatar
                  : item.avatar
              "
            />
            <img
              v-if="!item.avatar"
              class="contImg default-avatar"
            />
            <div :class="isBatchShow ? 'nameHover' : 'name'">{{ item.nick_name }}</div>
            <div :class="isBatchShow ? 'timeHover' : 'time'">
              {{ item.comment_publish_date_str }}
            </div>

            <div class="cont">
              <div v-if="item.reply_dto" class="reference">
                @{{ item.reply_dto.nick_name }}： {{ item.reply_dto.content }}
              </div>
              <span
                v-html="
                  highlightSensitiveWords(
                    item.content,
                    item.sensitive_word,
                    item.leader_sensitive_word
                  )
                "
              ></span>
              <span v-if="item.red_result" class="djh"></span>
              <span v-if="item.black_result" class="gjh"></span>
              <span v-if="item.single_emotion_result == '正'" class="zheng"></span>
              <span v-if="item.single_emotion_result == '中'" class="zhong"></span>
              <span v-if="item.single_emotion_result == '负'" class="fu"></span>
              <el-tooltip v-if="item.event" effect="dark" :content="item.event" placement="top">
                <span class="shijian"></span>
              </el-tooltip>
              <span v-if="item.whole_emotion_result == '负'" class="genping"></span>
</div>
            <div class="like"><span></span>{{ item.like_count }}</div>
            <div class="icon"><span></span>{{ item.follow_count }}</div>
            <div
              v-if="activeTab == 0 && !isBatchShow"
              class="ignore"
              @click="handleBatch(item.comment_id, item.content, 2, item.article_id, item.reply_id)"
            >
              <span v-auth="'socialPlatform:ignoredAlert'"></span>标记已忽略
            </div>
            <div
              v-if="activeTab == 0 && !isBatchShow"
              class="processed"
              @click="handleBatch(item.comment_id, item.content, 1, item.article_id, item.reply_id)"
            >
              <span v-auth="'socialPlatform:handledAlert'"></span>标记已处理
            </div>
            <div v-show="!isBatchShow" class="more">
              <el-select
                placeholder="..."
                class="no-border-select"
                clearable
                filterable
                @focus="removeFocus"
                @change="value => handleSelectChange(item, value)"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <!-- 分页组件 -->
        <div v-if="totalItems > 20 && totalItemsNum" class="page">
          <el-pagination
            background
            small
            layout="total, sizes, prev, pager, next"
            :total="totalItems"
            :page-size="pageSize"
            :current-page="currentPage"
            :page-sizes="[20, 50]"
            :pager-count="3"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
      <el-dialog v-model="infoShow" width="400" align-center :show-close="true">
        <div class="infoCont"></div>
      </el-dialog>
    </div>
  </AbsoluteContainer>
</template>

<script setup name='tiktok'>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElPagination } from 'element-plus'
import { requests } from '@/api/socialPlatform/tiktok'
import { requestsWeibo } from '@/api/socialPlatform/weibo'
import { requestsWeiXin } from '@/api/socialPlatform/weixin'
import useClipboard from 'vue-clipboard3'
// import as from './data.json'
const { toClipboard } = useClipboard()
const time = ref([])
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
const listRef = ref(null)
const infoShow = ref(false)
const leftLoading = ref(false)
const rightLoading = ref(false)
const activeTab = ref(0)
const interfaceTab = ref('tiktok')
const iconList = ref([
  {
    id: 1,
    name: 'tiktok',
    flag: false
  },
  {
    id: 2,
    name: 'weibo',
    flag: false
  },
  {
    id: 3,
    name: 'weixin',
    flag: false
  }
])
const iconNames = ref({
  tiktok: '抖音',
  weibo: '微博',
  weixin: '微信'
})
const currentIcon = ref('tiktok')
const tabs = ref([
  {
    name: '最新告警',
    auth: 'socialPlatform:newTab',
    id: 0
  },
  {
    name: '已处理告警',
    auth: 'socialPlatform:processedTab',
    id: 1
  },
  {
    name: '忽略告警',
    auth: 'socialPlatform:ignoreTab',
    id: 2
  }
])
const handerFoucs = ref(false)
const getVideos = ref({
  state: 0,
  current: 1,
  size: 20,
  start_time: '',
  end_time: ''
})
const getComments = ref({
  state: 0,
  current: 1,
  size: 50,
  article_id: ''
})
const firstSummary = ref({
  id: '',
  share_url: ''
})
const passAllBut = ref(false)
const countNum = ref('0')
const share_url = ref()
const summaryList = ref([])
const commentsList = ref([])
const totalItemsNum = ref()
const pageSizeNum = ref()
const currentPageNum = ref()
const totalItems = ref()
const pageSize = ref()
const currentPage = ref()
const isBatchShow = ref(false)
const notProcess = ref(false)
const newSelectedArray = ref([])
const options = ref([
  // { value: 'option1', label: '回复' },
  // { value: 'option2', label: '删除' },
  { value: 'option3', label: '复制' },
  // { value: 'option4', label: '查看详情' },
  { value: 'option5', label: '查看稿件' }
  // 更多选项...
])
const handle = ref({
  comment_ids: '',
  reply_ids: '',
  state: '',
  article_id: ''
})
// 抖音 微博切换
const handleIcon = icon => {
  isBatchShow.value = false
  currentIcon.value = icon
  const firstTab = document.querySelector('.tabTit')
  if (firstTab.id) {
    switch (icon) {
      case 'weibo':
      case 'tiktok':
      case 'weixin':
        interfaceTab.value = icon
        activeTab.value = Number(firstTab.id)
        getVideosFun()
        getCount()

        leftLoading.value = true
        commentsList.value = []
        summaryList.value = []
        break
      default:
        break
    }
  }
}
onMounted(() => {
  // 默认最新告警
  handleIcon('tiktok')
  const handleAiClick = event => {
    const target = event.target.closest('.tiktokAi')
    if (target) {
      const content = decodeURIComponent(target.getAttribute('data-content'))
      handelAi(content, target)
    }
  }

  // 在父组件上添加事件委托
  document.addEventListener('click', handleAiClick)

  closeTipHandler = event => {
    // 检查点击的目标是否是 .tiktokAi 或其子元素
    if (!event.target.closest('.tiktokAi') && !isStreamActive) {
      // 如果不是，则关闭所有 .tip
      document.querySelectorAll('.tiktokAi .tip').forEach(tip => {
        tip.style.display = 'none'
      })
      currentTip = null
    }
  }
  // 添加关闭 .tip 的事件监听器
  document.addEventListener('click', closeTipHandler)
  onUnmounted(() => {
    // 移除 handleAiClick 事件监听器
    document.removeEventListener('click', handleAiClick)
    // 移除 closeTipHandler 事件监听器
    document.removeEventListener('click', closeTipHandler)
  })
})
const getRequestFunction = () => {
  const requestMap = {
    'tiktok': requests,
    'weibo': requestsWeibo,
    'weixin': requestsWeiXin
  }
  return requestMap[interfaceTab.value] || requests 
}
// 时间筛选
const handleDateChange = () => {
  if (time.value) {
    getVideos.value.start_time = time.value[0]
    getVideos.value.end_time = time.value[1]
    getVideosFun()
  }
}
const handleBlur = () => {
  getVideos.value.start_time = ''
  getVideos.value.end_time = ''
  time.value = []
  handleDateChange()
}
// ai 输出
let currentTip = null
let isStreamActive = false // 标志变量，跟踪流的状态
let sseClient = ref(null)
let closeTipHandler = null

const handelAi = (name, target) => {
  if (isStreamActive) {
    // 如果流正在进行，忽略新的点击事件
    return
  }

  // 隐藏所有 .tip
  document.querySelectorAll('.tiktokAi .tip').forEach(tip => {
    tip.style.display = 'none'
  })

  // 获取当前点击的 .tiktokAi 元素下的 .tip
  const tip = target.querySelector('.tip')
  if (tip) {
    // 显示当前 .tip
    tip.style.display = 'block'
    currentTip = tip // 更新当前显示的 tip
  }

  // 执行其他逻辑
  const errorCallback = () => {
    // 错误处理
  }

  const eventCallback = (event, data) => {
    console.log('event===', event)
    const jsonString = data

    try {
      const obj = JSON.parse(jsonString)
      console.log(obj.result)

      // 更新当前 .tip 的内容，并在结尾添加固定文本
      if (currentTip) {
        currentTip.innerHTML = `${obj.result}<p>·该内容由AI生成，仅供参考。</p>`
      }
    } catch (error) {
      console.log(error)
    } finally {
      if (event === 'stream-end') {
        isStreamActive = false // 流结束，重置流状态
        sseClient.value.close()
      }
    }
  }

  // 检查当前 .tip 是否已经有内容
  if (tip && (tip.innerHTML.trim() === '' || tip.innerHTML === 'AI思考中...')) {
    isStreamActive = true // 标记流开始
    sseClient.value = requests.politician_info_generate({ name }, eventCallback, errorCallback)
  }
}

// 点击空白处关闭当前 .tip
document.addEventListener('click', event => {
  // 检查点击的目标是否是 .tiktokAi 或其子元素
  if (!event.target.closest('.tiktokAi') && !isStreamActive) {
    // 如果不是，则关闭所有 .tip
    document.querySelectorAll('.tiktokAi .tip').forEach(tip => {
      tip.style.display = 'none'
    })
    currentTip = null // 重置当前显示的 tip
  }
})

// 敏感词筛选
const generateSensitiveWordHtml = (word, type, isLeader) => {
  const encodedWord = encodeURIComponent(word)
  const highlighted = `<span class="comments_sensitive-word" data-type="${type}" data-word="${word}" data-content="${encodedWord}"><span style="color: red;">${word}</span></span>`
  const aiHighlighted = `<span  data-word="${word}" data-content="${encodedWord}"><span style="color: red;">${word}</span></span>`
  if (isLeader) {
    return `${aiHighlighted} <span class="tiktokAi" data-content="${encodedWord}"><i></i><span class="tip">AI思考中...</span></span>`
  }
  return highlighted
}

const highlightSensitiveWords = (content, sensitiveWordJson, leaderSensitiveWordJson) => {
  if (!content) return content

  let sensitiveWords = []
  let leaderSensitiveWords = []
  let sensitiveWordTypes = {}

  try {
    // 解析敏感词，提取每个对象的 word 属性和类型
    if (sensitiveWordJson) {
      const parsedSensitiveWords = JSON.parse(sensitiveWordJson)
      sensitiveWords = parsedSensitiveWords.map(item => item.word)
      sensitiveWordTypes = parsedSensitiveWords.reduce((acc, item) => {
        acc[item.word] = item.type
        return acc
      }, {})
    }
    // 解析领导敏感词
    if (leaderSensitiveWordJson) leaderSensitiveWords = JSON.parse(leaderSensitiveWordJson)
  } catch (error) {
    return content 
  }

  // 创建正则表达式
  const combinedWords = [...new Set([...sensitiveWords, ...leaderSensitiveWords])]
  const combinedRegex = combinedWords.length > 0 ? new RegExp(combinedWords.join('|'), 'gi') : null

  // 检查是否有匹配
  if (combinedRegex && combinedRegex.test(content)) {
    return content.replace(combinedRegex, match => {
      const type = sensitiveWordTypes[match] || '敏感词'
      return generateSensitiveWordHtml(match, type, leaderSensitiveWords.includes(match))
    })
  }
  return content
}
// 数量
const getCount = () => {
  // 定义一个通用的请求函数
  const fetchCount = (requestFunction, interfaceName, index) => {
    requestFunction({ state: 0 }).then(res => {
      if (res.code === 0) {
        if (res.data.count >= 1) {
          iconList.value[index].flag = true
        }
        if (interfaceTab.value === interfaceName) {
          countNum.value = res.data.count
        }
      }
    })
  }

  // 调用通用请求函数
  fetchCount(requests.get_count, 'tiktok', 0)
  fetchCount(requestsWeibo.get_count, 'weibo', 1)
  fetchCount(requestsWeiXin.get_count, 'weixin', 2)
}
// 汇总
const hanldeHander = () => {
  commentsList.value = []
  if (summaryList.value.length > 0) {
    handerFoucs.value = true
    handleSunmmaryListClick(null)
    summaryList.value.map(v => (v.isClick = false))
  }
}
// 告警区域点击切换
const selectTab = index => {
  getVideos.value.start_time = ''
  getVideos.value.end_time = ''
  time.value = []
  getVideos.value.current = 1
  getComments.value.current = 1
  isBatchShow.value = false
  activeTab.value = index
  commentsList.value = []
  getVideosFun(index)
  leftLoading.value = true
}
// 汇总列表
const getVideosFun = () => {
  if (leftLoading.value) return
  getVideos.value.state = activeTab.value
  const requestFunction = getRequestFunction()
  requestFunction.get_videos(getVideos.value).then(res => {
    if (res.code == 0) {
      leftLoading.value = false
      let updatedComments = res.data.page.records.map(comment => ({
        ...comment,
        isClick: false
      }))
      summaryList.value = updatedComments
      totalItemsNum.value = res.data.page.total
      pageSizeNum.value = res.data.page.size
      currentPageNum.value = res.data.page.current

      if (summaryList.value.length && activeTab.value != 0) {
        firstSummary.value.id = summaryList.value[0].article_id
        firstSummary.value.share_url = summaryList.value[0].share_url
        handleSunmmaryListClick(firstSummary.value.id, 0, firstSummary.value.share_url)
      } else {
        hanldeHander()
      }
    }
  })
}
// 汇总列表点击
const handleSunmmaryListClick = (id, index = 0, url) => {
  rightLoading.value = true
  if (!notProcess.value) {
    cancel()
  } else {
    newSelectedArray.value = []
    passAllBut.value = false
  }
  id && (handerFoucs.value = false)
  if (summaryList.value.length > 0 && id) {
    summaryList.value.map(v => (v.isClick = false))
    summaryList.value[index].isClick = true
  }

  getComments.value.article_id = id
  share_url.value = url
  getComments.value.state = activeTab.value

  const requestFunction = getRequestFunction()
  requestFunction.get_comments(getComments.value).then(res => {
    if (res.code == 0) {
      rightLoading.value = false
      let updatedComments = res.data.page.records.map(comment => ({
        ...comment,
        isSelect: false
      }))
      commentsList.value = updatedComments
      // commentsList.value = as
      getVideos.value.current = 1
      getComments.value.current = 1
      totalItems.value = res.data.page.total
      pageSize.value = res.data.page.size
      currentPage.value = res.data.page.current

      notProcess.value = false
      if (listRef.value) {
        listRef.value.scrollTop = 0 // 滚动到顶部
      }
    }
  })
}
const removeFocus = event => {
  event.target.blur()
}
// 批量处理选择（单选）
const handelPass = () => {
  newSelectedArray.value = []
  commentsList.value.forEach(item => {
    if (item.isSelect) {
      // 检查 newSelectedArray 中是否已存在该项
      const existingIndex = newSelectedArray.value.findIndex(
        selectedItem => selectedItem.id === item.id
      )
      if (existingIndex === -1) {
        newSelectedArray.value.push(item)
      }
    }
  })
}
const handelPassAll = () => {
  newSelectedArray.value = []
  if (passAllBut.value) {
    commentsList.value.forEach(item => {
      item.isSelect = true
      newSelectedArray.value.push(item)
    })
  } else {
    commentsList.value.forEach(item => {
      item.isSelect = false
      newSelectedArray.value = []
    })
  }
}
const handleSelectChange = (item, value) => {
  switch (value) {
    // 复制
    case 'option3':
      copyAddress(item.content)
      break
    case 'option5':
      window.open(item.share_url)
      break
    default:
      break
  }
}
const copyAddress = asyncrow => {
  try {
    toClipboard(asyncrow)
    ElMessage({
      type: 'success',
      message: '复制成功！'
    })
  } catch (e) {
    console.error(e)
  }
}
// 页码改变时的处理

const handlePageChange = newPage => {
  getComments.value.current = newPage
  notProcess.value = true
  handleSunmmaryListClick(getComments.value.article_id)
}
const handleSizeChange = newPage => {
  getComments.value.size = newPage
  notProcess.value = true
  handleSunmmaryListClick(getComments.value.article_id)
}
const summary_handlePageChange = newPage => {
  getVideos.value.current = newPage
  getVideosFun()
}
const summary_handleSizeChange = newPage => {
  getVideos.value.size = newPage
  getVideosFun()
}
// 评论处理
const handleBatch = (comment_ids, content, state, article_id, reply_ids) => {
  if (newSelectedArray.value.length > 1) {
    // 多条处理
    const { commentIds, replyIds, articleIds } = newSelectedArray.value.reduce(
      (acc, comment) => {
        if (comment.comment_id) {
          acc.commentIds.push(comment.comment_id)
        }
        if (comment.reply_id) {
          acc.replyIds.push(comment.reply_id)
        }
        if (comment.article_id) {
          acc.articleIds.push(comment.article_id)
        }
        return acc
      },
      { commentIds: [], replyIds: [], articleIds: [] }
    )
    const articleIdsStr = articleIds.join(',')
    const commentIdsStr = commentIds.join(',')
    const replyIdsStr = replyIds.join(',')

    handle.value.comment_ids = commentIdsStr
    handle.value.reply_ids = replyIdsStr
    handle.value.state = state
    handle.value.article_id = articleIdsStr
    ElMessageBox.confirm(
      `确认处理以下${
        commentIdsStr.split(',').filter(id => id).length +
        replyIdsStr.split(',').filter(id => id).length
      }条评论？`,
      state == 1 ? '标记为已处理' : '标记为已忽略',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'Warning',
        dangerouslyUseHTMLString: true
      }
    ).then(() => {
      handleProcessing()
    })
  } else {
    // 单条处理
    handle.value.comment_ids = comment_ids
    handle.value.state = state
    handle.value.article_id = article_id
    handle.value.reply_ids = reply_ids
    ElMessageBox.confirm(`【${content}】`, state == 1 ? '标记为已处理' : '标记为已忽略', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true
    }).then(() => {
      handleProcessing()
    })
  }
  const handleProcessing = () => {
    const requestFunction = getRequestFunction()
    requestFunction.handle(JSON.stringify(handle.value)).then(res => {
      if (res.code == 0) {
        ElMessage.success('处理成功')
        isBatchShow.value = false
        getCount()
        handleSunmmaryListClick(getComments.value.article_id)
        cancel()
      }
    })
  }
}
// 取消
const cancel = () => {
  isBatchShow.value = false
  newSelectedArray.value.forEach(comment => {
    comment.isSelect = false
  })
  newSelectedArray.value = []
  passAllBut.value = false
}
</script>
<style>
.comments_sensitive-word {
  position: relative;
  display: inline-block;
  cursor: pointer; /* 鼠标悬浮时显示指针 */
}

.comments_sensitive-word::before {
  content: attr(data-type);
  position: absolute;
  top: -3em; /* 调整这个值以改变悬浮框的位置 */
  left: 0;
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 0.8em;
  color: #333;
  white-space: nowrap;
  opacity: 0; /* 默认不显示 */
  transition: opacity 0.3s; /* 平滑过渡效果 */
}

.comments_sensitive-word:hover::before {
  opacity: 1; /* 鼠标悬浮时显示 */
}

.tiktokAi {
  color: #f65520;
  margin-right: 15px;
  position: relative;
}

.tiktokAi i {
  width: 16px;
  height: 12px;
  display: inline-block;
  position: absolute;
  top: 4px;
  left: -3px;
  background: url('@/assets/images/tiktok/ai.png') no-repeat center center / 100% auto;
  background-size: 16px auto;
  cursor: pointer;
}

.tiktokAi .tip {
  position: absolute;
  top: 100%;
  left: -30px;
  background: #eef5fe;
  border-image: linear-gradient(270deg, rgba(238, 245, 254, 1), rgba(209, 238, 255, 1)) 1 1;
  line-height: 18px;
  font-size: 12px;
  color: #333;
  padding: 5px;
  width: 210px;
  word-wrap: break-word;
  padding: 10px;
  text-align: justify;
  white-space: normal;
  overflow-y: auto;
  cursor: pointer;
  display: none;
  z-index: 9999;
  border-radius: 10px;
}

.tiktokAi .tip p {
  width: 100%;
  clear: both;
  display: block;
  font-size: 12px;
  color: #a2a2a2;
  margin-top: 5px;
  font-style: normal;
}
.more .el-select__wrapper {
  cursor: pointer;
  box-shadow: none !important;
  background-color: none !important;
}

.more .el-select__wrapper :hover {
  color: #0070f3;
}

.more .el-select__suffix {
  display: none !important;
}
.more .el-select__placeholder.is-transparent span {
  margin-left: 35px;
  position: relative;
  top: -4px;
}
</style>
<style lang="scss" scoped>
.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

img {
  width: 100%;
  height: 100%;
  display: block;
}
.checkBlue,
.checkAll {
  width: 25px;
  float: left;
  font-weight: 600;
  color: #333;
  margin-top: 5px;
  .el-checkbox.el-checkbox--large {
    height: 30px;
  }
}
.checkAll {
  margin-top: -7px;
}
.Main {
  width: 100%;
  height: 100%;
  display: flex;

  .leftInfo {
    min-width: 230px;
    float: left;
    border-radius: 12px;
    height: calc(100% - 20px);
    overflow: hidden;
    background-color: #fff;

    .iconList {
      width: 80px;
      height: 100%;
      border-top-left-radius: 12px;
      border-bottom-left-radius: 12px;
      background: #f8f9fa;
      padding-top: 10px;
      float: left;

      .icon,
      .iconHover {
        width: 60px;
        height: 60px;
        font-size: 12px;
        padding: 10px 0;
        position: relative;
        cursor: pointer;
        margin-left: 10px;
        margin-bottom: 10px;

        span {
          display: block;
          position: absolute;
          bottom: 2px;
          width: 60px;
          text-align: center;
        }

        i {
          width: 10px;
          height: 10px;
          background: #ff0000;
          border-radius: 100%;
          position: absolute;
          right: 8px;
          top: 2px;
        }
      }

      .tiktok {
        background-image: url('@/assets/images/tiktok/tiktok.png');
        background-repeat: no-repeat;
        background-size: 32px auto;
        background-position: center 6px;
      }

      .weibo {
        background-image: url('@/assets/images/tiktok/weibo.png');
        background-repeat: no-repeat;
        background-size: 32px auto;
        background-position: center 6px;
      }

      .weixin {
        background-image: url('@/assets/images/tiktok/weixin.png');
        background-repeat: no-repeat;
        background-size: 32px auto;
        background-position: center 6px;
      }

      .iconHover {
        background-color: #eef5fe;
        border-radius: 12px;

        span {
          color: #0070f3;
        }
      }
    }

    .textList {
      width: 140px;
      float: left;
      padding: 0 10px;

      .title {
        margin-top: 20px;
        font-size: 12px;
        color: #a2a3a4;
        margin-bottom: 10px;
      }

      .tabTit {
        width: 130px;
        height: 32px;
        line-height: 32px;
        // background: #EEF5FE;
        border-radius: 6px;
        font-size: 14px;
        margin-bottom: 10px;
        cursor: pointer;

        .def {
          width: 15px;
          height: 15px;
          float: left;
          margin-top: 8px;
          margin-left: 10px;
          margin-right: 10px;
        }

        i {
          font-style: normal;
          background: #ff0000;
          color: #fff;
          font-size: 10px;
          padding: 0 5px;
          border-radius: 10px;
          position: relative;
          top: -2px;
        }

        &:hover {
          background: #eef5fe;
          color: #0070f3;

          .icon1 {
            background: url('@/assets/images/tiktok/icon1_hover.png') no-repeat center center / 100%
              auto;
            background-size: 16px auto;
          }

          .icon2 {
            background: url('@/assets/images/tiktok/icon2_hover.png') no-repeat center center / 100%
              auto;
            background-size: 16px auto;
          }

          .icon3 {
            background: url('@/assets/images/tiktok/icon3_hover.png') no-repeat center center / 100%
              auto;
            background-size: 16px auto;
          }

          .icon4 {
            background: url('@/assets/images/tiktok/icon4_hover.png') no-repeat center center / 100%
              auto;
            background-size: 16px auto;
          }
        }
      }

      .tabSelect {
        background: #eef5fe;
        color: #0070f3;

        .icon1 {
          background: url('@/assets/images/tiktok/icon1_hover.png') no-repeat center center / 100%
            auto;
          background-size: 16px auto;
        }

        .icon2 {
          background: url('@/assets/images/tiktok/icon2_hover.png') no-repeat center center / 100%
            auto;
          background-size: 16px auto;
        }

        .icon3 {
          background: url('@/assets/images/tiktok/icon3_hover.png') no-repeat center center / 100%
            auto;
          background-size: 16px auto;
        }

        .icon4 {
          background: url('@/assets/images/tiktok/icon4_hover.png') no-repeat center center / 100%
            auto;
          background-size: 16px auto;
        }
      }

      .icon1 {
        background: url('@/assets/images/tiktok/icon1.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
      }

      .icon2 {
        background: url('@/assets/images/tiktok/icon2.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
      }

      .icon3 {
        background: url('@/assets/images/tiktok/icon3.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
      }

      .icon4 {
        background: url('@/assets/images/tiktok/icon4.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
      }
    }
  }

  .centerInfo {
    float: left;
    margin-left: 15px;
    width: 100%;
    max-width: 460px;
    min-width: 320px;
    border-radius: 12px;
    height: calc(100% - 20px);
    background-color: #fff;
    padding: 20px;

    .hander,
    .handerHover {
      width: 100%;
      height: 95px;
      padding-top: 5px;
      background-color: #fcfcfc;
      border-radius: 6px;
      margin-bottom: 20px;
      cursor: pointer;
      .left {
        float: left;
        margin-left: 10px;
        margin-top: 18px;
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .info {
        width: 168px;
        height: 18px;
        line-height: 12px;
        font-size: 12px;
        color: #828282;
        padding-left: 5px;
        padding-top: 3px;
        background-image: url('@/assets/images/tiktok/arrow_r.png'),
          url('@/assets/images/tiktok/arrow.png');
        background-position: right top, left bottom;
        background-repeat: no-repeat;
        background-size: 4px auto;
      }

      .right {
        width: 80px;
        background: url('@/assets/images/tiktok/gaojing.png') no-repeat 0 -5px;
        background-size: 80px auto;
        height: 80px;
        float: right;
        margin-right: 10px;
        margin-top: 5px;
      }
    }
    .handerHover {
      background: linear-gradient(95deg, #f1f9ff 0%, #e7f4fe 100%), #fcfcfc;
    }
    .active-tab-height {
      height: calc(100% - 120px) !important;
    }
    .scrollList {
      width: 100%;
      height: calc(100% - 60px);
      overflow-y: auto;
      .list,
      .listNoImg {
        overflow: hidden;
        margin-bottom: 20px;
        background: #fcfcfc;
        border-radius: 10px;
        cursor: pointer;
        padding: 5px;
        height: 90px;
        position: relative;
        img {
          float: left;
          width: 60px;
          height: 80px;
          border-radius: 5px;
        }

        .tit {
          width: calc(100% - 70px);
          line-height: 21px; /* 行高设置为22px */
          max-height: 66px; /* 最大高度设置为2行的高度 */
          overflow: hidden;
          font-size: 14px;
          float: right;
          text-align: left;
          margin-bottom: 5px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3; /* 限制为2行 */
        }

        .bottomRig {
          width: calc(100% - 85px);
          position: absolute;
          bottom: 3px;
          left: 75px;
        }

        .time {
          float: left;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);
          position: relative;
          bottom: 0;
        }

        .wring {
          float: right;
          color: rgba(0, 0, 0, 0.6);
          font-size: 12px;

          span {
            float: left;
            width: 15px;
            height: 15px;
            background: url('@/assets/images/tiktok/wring.png') no-repeat center center / 100% auto;
            background-size: 15px auto;
            margin-right: 2px;
          }
        }
      }

      .listNoImg {
        height: 110px;
        .tit {
          width: 100%;
          margin-top: 15px;
        }

        .bottomRig {
          width: calc(100% - 15px);
          left: 5px;
        }
      }
    }
  }

  .rightInfo {
    float: right;
    margin-left: 15px;
    border-radius: 12px;
    height: calc(100% - 20px);
    background-color: #fff;
    width: 100%;
    min-width: 500px;
    margin-right: 20px;
    padding: 20px;
    .infoText{
      span{
        display: inline-block;
        width:16px;
        height: 16px;
        background: url('@/assets/images/tiktok/info.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
        position: relative;
        top: 4px;
        margin-right: 3px;
      }
      float: right;
      cursor: pointer;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      margin-right: 15px;
      color: #666;
    }
    
    .batch {
      float: right;
      cursor: pointer;
      width: 96px;
      height: 32px;
      line-height: 32px;
      background: #eef5fe;
      border-radius: 6px;
      text-align: center;
      color: #0070f3;
      font-size: 14px;
      margin-bottom: 20px;

      span {
        width: 16px;
        height: 16px;
        background: url('@/assets/images/tiktok/batch.png') no-repeat center center / 100% auto;
        background-size: 16px auto;
        display: block;
        float: left;
        margin-top: 8px;
        margin-left: 10px;
      }
    }
    .batchList {
      float: right;
      cursor: pointer;

      span {
        display: block;
        float: right;
        cursor: pointer;
        width: 104px;
        height: 32px;
        line-height: 32px;
        border: 1px solid #eef5fe;
        border-radius: 6px;
        text-align: center;
        color: #0070f3;
        font-size: 14px;
        margin-bottom: 20px;
        margin-right: 10px;
      }
      .cancel {
        width: 72px;
        color: #333;
      }
    }
    .select {
      font-size: 12px;
      margin-bottom: 10px;
      float: left;
      margin-top: 20px;
    }
    .contList,
    .contList2 {
      clear: both;
      overflow-y: auto;
      height: calc(100% - 90px);
      overflow-x: hidden;
      .list {
        border-bottom: 1px solid #efefef;
        padding-bottom: 30px;
        margin-bottom: 20px;

        .contImg {
          float: left;
          width: 30px;
          height: 30px;
          margin-bottom: 15px;
          border-radius: 100%;
        }
        .default-avatar{
          float: left;
          width: 30px;
          height: 30px;
          margin-bottom: 15px;
          border-radius: 100%;
          background: url('@/assets/images/tiktok/haderImg.png') no-repeat center center / 100% auto;
            background-size: 30px auto;
        }
        .name {
          font-size: 14px;
          float: right;
          width: calc(100% - 45px);
        }
        .nameHover {
          font-size: 14px;
          float: right;
          width: calc(100% - 70px);
        }
        .time {
          float: right;
          width: calc(100% - 45px);
          font-size: 14px;
          color: #666;
        }
        .timeHover {
          float: right;
          width: calc(100% - 70px);
          font-size: 14px;
          color: #666;
        }

        .cont,
        .reference {
          width: 100%;
          clear: both;
          font-size: 14px;
          line-height: 22px;
          margin-bottom: 10px;
          position: relative;

          .sensitive {
            color: #f65520;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            background: #fee1d9;
            border-radius: 2px;
            border: 1px solid #f65520;
            font-size: 12px;
            margin-left: 5px;
            padding: 0 3px;
          }
        }

        .reference {
          background: #f5f5f5;
          border-radius: 6px;
          padding: 6px;
        }
        .djh{
          display: inline-block;
          width: 40px;
          height: 16px;
          background: url('@/assets/images/tiktok/djh.png') no-repeat center center / 100% auto;
          background-size: 40px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .gjh{
          display: inline-block;
          width: 40px;
          height: 16px;
          background: url('@/assets/images/tiktok/gjh.png') no-repeat center center / 100% auto;
          background-size: 40px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .zheng{
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('@/assets/images/tiktok/zheng.png') no-repeat center center / 100% auto;
          background-size: 16px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .fu{
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('@/assets/images/tiktok/fu.png') no-repeat center center / 100% auto;
          background-size: 16px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .zhong{
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('@/assets/images/tiktok/zhong.png') no-repeat center center / 100% auto;
          background-size: 16px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .shijian{
          display: inline-block;
          width: 26px;
          height: 18px;
          background: url('@/assets/images/tiktok/shiijan.png') no-repeat center center / 100% auto;
          background-size: 26px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .genping{
          display: inline-block;
          width: 56px;
          height: 16px;
          background: url('@/assets/images/tiktok/genping.png') no-repeat center center / 100% auto;
          background-size: 56px auto;
          margin: 0 3px;
          position: relative;
          top: 3px;
        }
        .like,
        .icon {
          color: #c0c0c0;
          font-size: 14px;
          float: left;

          span {
            width: 16px;
            height: 16px;
            background: url('@/assets/images/tiktok/like.png') no-repeat center center / 100% auto;
            background-size: 16px auto;
            float: left;
            margin-top: 2px;
            margin-right: 2px;
          }
        }

        .icon {
          margin-left: 30px;

          span {
            background: url('@/assets/images/tiktok/cont.png') no-repeat center center / 100% auto;
            background-size: 16px auto;
          }
        }

        .ignore {
          float: right;
          color: #666;
          font-size: 14px;
          cursor: pointer;

          span {
            width: 17px;
            height: 16px;
            background: url('@/assets/images/tiktok/ignore.png') no-repeat center center / 100% auto;
            background-size: 17px auto;
            float: left;
            margin-top: 2px;
          }

          &:hover {
            color: #0070f3;

            span {
              background: url('@/assets/images/tiktok/ignore_hover.png') no-repeat center center /
                100% auto;
              background-size: 17px auto;
            }
          }
        }

        .more {
          float: right;
          color: #666;
          font-size: 14px;
          cursor: pointer;
          width: 77px;
          height: 16px;
          margin-top: -5px;
        }

        .processed {
          float: right;
          color: #666;
          font-size: 14px;
          margin-right: 15px;
          cursor: pointer;

          span {
            width: 17px;
            height: 16px;
            background: url('@/assets/images/tiktok/processed.png') no-repeat center center / 100%
              auto;
            background-size: 17px auto;
            float: left;
            margin-top: 2px;
          }

          &:hover {
            color: #0070f3;

            span {
              background: url('@/assets/images/tiktok/processed_hover.png') no-repeat center center /
                100% auto;
              background-size: 17px auto;
            }
          }
        }
      }
    }
    .contList2 {
      height: calc(100% - 35px);
    }
  }
  .infoCont{
      width: 372px;
      height: 325px;
      background: url('@/assets/images/tiktok/info_cont.jpg') no-repeat center center / 100% auto;
        background-size: 372px auto;
    }
}
</style>