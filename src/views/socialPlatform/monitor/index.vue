<template>
  <div class="index">
    <div class="list">
      <div class="head">
        <el-input v-model="input" style="width: 220px" placeholder="请输入稿件标题" />
      </div>
      <div class="body">
        <div class="child">
          <div class="left"></div>
          <div class="right">
            <div class="title">
              趁新政红利卖二手房，快来潮新闻“口水楼市趁新政红利卖二手房，快来潮新闻“口水楼市趁新政红利卖二手房，快来潮新闻“口水楼市
            </div>
            <div class="foot">
              <div class="time">2024-03-21 09:41:20</div>
              <div class="child-right">
                <div class="src">详情</div>
                <div class="line"></div>
                <div class="num">12</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="page">
        <el-pagination
          background
          small
          layout="total, sizes, prev, pager, next, jumper"
          :total="listData.total"
          :page-size="listData.size"
          :current-page="listData.current"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <div class="comment">
      <div class="comment-des">
        <div class="head">
          <div class="avatar"></div>
          <div class="right">
            <div class="name">潮客_kijuil</div>
            <div class="next">
              <div class="date">2023-03-04</div>
              <div class="palce">浙江杭州</div>
            </div>
          </div>
        </div>
        <div class="comment-des">关爱小学生的合法权益，保护好青少年的身心</div>
        <div class="footer">
          <div class="f-left">
            <div class="like">19</div>
            <div class="number">12</div>
          </div>
          <div class="btns">
            <div class="reply">
              <img src="@/assets/images/tiktok/replay.png" alt="" />
              回复
            </div>
            <div class="del">
              <img src="@/assets/images/tiktok/delete.png" alt="" />
              删除
            </div>
          </div>
        </div>
        <div class="second">
          <div class="head">
            <div class="avatar"></div>
            <div class="right">
              <div class="name">潮客_kijuil</div>
              <div class="next">
                <div class="date">2023-03-04</div>
                <div class="palce">浙江杭州</div>
              </div>
            </div>
          </div>
          <div class="comment-des">关爱小学生的合法权益，保护好青少年的身心</div>
          <div class="footer">
            <div class="f-left">
              <div class="like">19</div>
              <div class="number">12</div>
            </div>
            <div class="btns">
              <div class="reply">
                <img src="@/assets/images/tiktok/replay.png" alt="" />
                回复
              </div>
              <div class="del">
                <img src="@/assets/images/tiktok/delete.png" alt="" />
                删除
              </div>
            </div>
          </div>
        </div>
        <div class="show-more">
          <span class="line"></span>
          <span>展开1条回复</span>
          <svg-icon name="icon-more" />
        </div>
        <div class="show-more">
          <span class="line"></span>
          <span>收起</span>
          <svg-icon name="icon-up" />
        </div>
        <el-divider />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'

const listData = ref({
  data: [],
  total: 0,
  size: 100,
  current: 1
})

// 选择每页几条
const handleSizeChange = val => {
  listData.value.size = val
  listData.value.current = 1
}
// 点击分页器
const handleCurrentChange = val => {
  listData.value.current = val
}
</script>
<style lang="scss" scoped>
.index {
  display: flex;
}
.list {
  max-width: 500px;
  min-width: 300px;
  height: 800px;
  background: #fff;
  border-radius: 12px;
  padding-top: 20px;
  margin-right: 10px;
  .head {
    margin-bottom: 20px;
    padding: 0 20px;
  }
  .body {
    overflow-y: auto;
    height: calc(100% - 100px);
    padding: 0 20px;
    .child {
      width: 100%;
      height: 90px;
      background: #fcfcfc;
      border-radius: 6px;
      display: flex;
      padding: 10px;
      margin-bottom: 20px;
      .left {
        width: 53px;
        height: 69px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 10px;
      }
      .right {
        width: calc(100% - 63px);
        .title {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 22px;
          height: 44px;
          margin-bottom: 10px;
          overflow: hidden; //超出文本隐藏
          text-overflow: ellipsis; ///超出部分省略号显示
          display: -webkit-box; //弹性盒模型
          -webkit-box-orient: vertical; //上下垂直
          -webkit-line-clamp: 2; //自定义行数
        }
        .foot {
          display: flex;
          justify-content: space-between;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          .child-right {
            display: flex;
            align-items: center;
            .line {
              width: 1px;
              height: 6px;
              background: #666666;
              margin: 0 10px;
            }
          }
        }
      }
    }
  }
  .page {
    width: 100%;
    border-top: 1px solid #efefef;
    display: flex;
    justify-content: center;
    height: 44px;
    align-items: center;
  }
}
.comment {
  width: 946px;
  height: 800px;
  background: #ffffff;
  border-radius: 12px;
  padding: 30px 40px;
  overflow-y: auto;
  .comment-des {
    .head {
      display: flex;
      align-items: center;
      .avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #ccc;
        overflow: hidden;
        margin-right: 6px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .right {
        .name {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
        }
        .next {
          margin-top: 6px;
          display: flex;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          .date {
            margin-right: 6px;
          }
        }
      }
    }
    .comment-des {
      margin: 10px 0;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
    .footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .f-left {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: #c0c0c0;
        .like {
          margin-right: 20px;
        }
      }
      .btns {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        img {
          width: 16px;
          height: 16px;
          margin-right: 3px;
        }
        .reply {
          margin-right: 20px;
        }
        .reply,
        .del {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .reply:hover,
        .del:hover {
          filter: invert(51%) sepia(87%) saturate(4713%) hue-rotate(194deg) brightness(99%)
            contrast(101%);
        }
      }
    }
    .second {
      margin-top: 30px;
      padding-left: 36px;
    }
    .show-more {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 400;
      color: #0b82fd;
      margin-top: 10px;
      cursor: pointer;

      .line {
        width: 40px;
        height: 1px;
        border-top: 1px solid #0b82fd;
        margin-right: 10px;
      }

      .svg-icon {
        width: 14px;
        height: 14px;
        margin-left: 2px;
      }
    }
  }
}
</style>
