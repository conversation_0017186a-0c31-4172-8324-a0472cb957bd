<template>
  <div class="head-box">
    <div class="left">
      <el-date-picker
        v-model="time"
        style="margin-right: 35px; float: left;
        margin-top:3px"
        class="time"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :editable="false"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      >
      </el-date-picker>
      <el-input
        v-model="searchData.admin_name"
        :prefix-icon="Search"
        style="width: 300px; margin-right: 35px"
        placeholder="请输入账号"
        class="input-with-select"
        clearable
        :disabled="checkValue"
      >
      </el-input>
      <el-checkbox
        v-model="checkValue"
        class="checkBlue"
        label="只看我的"
        @change="handleCheckbox"
      />
      <el-button type="primary" @click="init"> 筛选 </el-button>
      <el-button type="primary" plain @click="reset"> 重置 </el-button>
    </div>
  </div>
  <div class="box">
    <div class="left">
      <div class="leftCont">
        <div class="title">评论审核总数</div>
        <div class="num">{{ allData.pinglunzongshu }}</div>
        <div class="time">时间更新于：</div>
        <div class="time">{{ allData.time }}</div>
      </div>
      <div class="echarts-box" style="float: left; margin:-20px 0 0 50px">
        <div id="myEcharts1" ref="myEcharts1" :style="{ width: '180px', height: '180px'}"></div>
      </div>
      <div class="center">
        <div class="item"><i></i> 已通过：{{ allData.yitongguo }}</div>
        <div class="item"><i></i> 待审核：{{ allData.daishenhe }}</div>
        <div class="item"><i></i> 已删除：{{ allData.yishanchu }}</div>
        <div class="item"><i></i> 已通过+已删除数量：{{ allData.youxiaoshu }}</div>
      </div>
    </div>
    <div class="right">
      <div class="leftCont">
        <div class="title">后台发布评论总数</div>
        <div class="num">{{ allData.fabuzongshu }}</div>
        <div class="time">时间更新于：</div>
        <div class="time">{{ allData.time }}</div>
      </div>
      <div class="echarts-box" style="float: left; margin:-20px 0 0 50px">
        <div id="myEcharts2" ref="myEcharts2" :style="{ width: '185px', height: '185px'}"></div>
      </div>
      <div class="center">
        <div class="item"><i></i> 马甲评论：{{ allData.pinglunrenwushu }}</div>
        <div class="item"><i></i> 小编评论+马甲回复：{{ allData.houtaihuifushu }}</div>
      </div>
    </div>
  </div>
  <div class="box" style="margin-bottom:30px">
    <div id="myEcharts3" ref="myEcharts3" :style="{ width: '1350px', height: '600px'}"></div>
  </div>
</template>

<script setup name='comment'>
import * as echarts from 'echarts'
const store = useStore()
import { Search } from '@element-plus/icons'
import { requests } from '@/api/business/dataStatistics'
import { ref, onBeforeMount, onMounted, watch } from 'vue'
const myEcharts1 = ref()
const myEcharts2 = ref()
const myEcharts3 = ref()
let echart1 = echarts
let echart2 = echarts
let echart3 = echarts
// 时间选择器
const time = ref([])
const allData = ref({
  yitongguo: '',
  channelList: [],
  daishenhe: '',
  houtaihuifushu: '',
  fabuzongshu: '',
  pinglunzongshu: '',
  yishanchu: '',
  youxiaoshu: '',
  pinglunrenwushu: '',
  time: '',
  channel_name: []
})
const checkValue = ref(false)
const searchData = ref({
  time_start: '',
  time_end: '',
  admin_name: ''
})
onMounted(() => {
  init()
})
onUnmounted(() => {
  echart1.dispose
  echart2.dispose
  echart3.dispose
})
// 重置
function reset() {
  const reset = {
    time_start: '',
    time_end: '',
    admin_name: ''
  }
  searchData.value = reset
  checkValue.value = false
  time.value = []
  init()
}
function handleCheckbox(value) {
  value ?  searchData.value.admin_name = store.getters['menu/user'].name  : searchData.value.admin_name = ''
}
// init
function init() {
  if (time.value) {
    searchData.value.time_start = time.value[0]
    searchData.value.time_end = time.value[1]
  } else {
    searchData.value.time_start = ''
    searchData.value.time_end = ''
  }
  requests.comment_number(searchData.value).then(res => {
    if (res.code == 0) {
      allData.value = res.data.result
      initChart1()
      initChart2()
      initChart3()
    }
  }).catch(error => {
    console.log(error)
  })
}
function initChart1() {
  let chart1 = echart1.init(myEcharts1.value)
  // 把配置和数据放这里
  chart1.setOption({
    tooltip: {
      trigger: 'item'
    },
    backgroundColor: '#fff',
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        color: ['#5793f7', '#ec9d38', '#70809b'],
        data: [
          { value: allData.value.yitongguo, name: '已通过' },
          { value: allData.value.daishenhe, name: '待审核' },
          { value: allData.value.yishanchu, name: '已删除' }

        ]
      }
    ]
  })
}
function initChart2() {
  let chart2 = echart2.init(myEcharts2.value)
  // 把配置和数据放这里
  chart2.setOption({
    tooltip: {
      trigger: 'item'
    },
    backgroundColor: '#fff',
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        color: ['#5ecfc6', '#215dbf'],
        data: [
          { value: allData.value.pinglunrenwushu, name: '马甲评论数量' },
          { value: allData.value.houtaihuifushu, name: '马甲回复+小编评论' }
        ]
      }
    ]
  })
}
function initChart3() {
  let channel_name = []
  let check_delete_comment_cnt = []
  let check_pass_comment_cnt = []
  let check_pending_comment_cnt = []
  let comment_reply_cnt = []
  let comment_task_cnt = []
  allData.value.channelList.forEach(v => {
    channel_name.push(v.channel_name)
    check_delete_comment_cnt.push(v.check_delete_comment_cnt)
    check_pass_comment_cnt.push(v.check_pass_comment_cnt)
    check_pending_comment_cnt.push(v.check_pending_comment_cnt)
    comment_reply_cnt.push(v.comment_reply_cnt)
    comment_task_cnt.push(v.comment_task_cnt)
  })

  var app = {}
  var option
  let myChart = echart3.init(myEcharts3.value)
  const posList = [
    'left',
    'right',
    'top',
    'bottom',
    'inside',
    'insideTop',
    'insideLeft',
    'insideRight',
    'insideBottom',
    'insideTopLeft',
    'insideTopRight',
    'insideBottomLeft',
    'insideBottomRight'
  ]
  app.configParameters = {
    rotate: {
      min: -90,
      max: 90
    },
    align: {
      options: {
        left: 'left',
        center: 'center',
        right: 'right'
      }
    },
    verticalAlign: {
      options: {
        top: 'top',
        middle: 'middle',
        bottom: 'bottom'
      }
    },
    position: {
      options: posList.reduce(function(map, pos) {
        map[pos] = pos
        return map
      }, {})
    },
    distance: {
      min: 0,
      max: 100
    }
  }
  app.config = {
    rotate: 90,
    align: 'left',
    verticalAlign: 'middle',
    position: 'insideBottom',
    distance: 15,
    onChange: function() {
      const labelOption = {
        rotate: app.config.rotate,
        align: app.config.align,
        verticalAlign: app.config.verticalAlign,
        position: app.config.position,
        distance: app.config.distance
      }
      myChart.setOption({
        series: [
          {
            label: labelOption
          },
          {
            label: labelOption
          },
          {
            label: labelOption
          },
          {
            label: labelOption
          }
        ]
      })
    }
  }
  const labelOption = {
    show: true,
    position: app.config.position,
    distance: app.config.distance,
    align: app.config.align,
    verticalAlign: app.config.verticalAlign,
    rotate: app.config.rotate,
    formatter: '{c}  {name|{a}}',
    fontSize: 16,
    rich: {
      name: {}
    }
  }
  option = {
    dataZoom: [
      // 1.横向使用滚动条
      {
        type: 'slider', // 有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
        show: true, // 是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
        start: 0, // 数据窗口范围的起始百分比0-100
        end: 10, // 数据窗口范围的结束百分比0-100
        bottom: 0 // 距离底部的距离
      }
    ],

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['已通过', '待审核', '已删除', '马甲回复+小编评论', '马甲评论数']
    },
    color: ['#4892FF', '#F89A02', '#6D809E', '#085BC6', '#08D2C7'],
    toolbox: {
      show: false,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack'] },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: channel_name
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '已通过',
        type: 'bar',
        barGap: 0,
        emphasis: {
          focus: 'series'
        },
        data: check_pass_comment_cnt
      },
      {
        name: '待审核',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: check_pending_comment_cnt
      },
      {
        name: '已删除',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: check_delete_comment_cnt
      },
      {
        name: '马甲回复+小编评论',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: comment_reply_cnt
      },
      {
        name: '马甲评论数',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: comment_task_cnt
      }

    ]
  }

  option && myChart.setOption(option)

}
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  margin-top: 20px;
  .left {
    .el-button {
      margin-left: 30px;
    }
  }
  .right {
    & > .el-select {
      width: 110px;
      margin-right: 10px;
    }
  }
}
.box {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
  .left,.right {
    flex-shrink: 0;
    width: 650px;
    border: 1px solid #cccccc;
    border-radius: 5px;
    padding: 25px;
    position: relative;
    display: flex;
    flex-shrink: 0;
    justify-content: space-around;
    .leftCont {
      float: left;
      .title {
        font-size: 18px;
        margin-bottom: 10px;
      }
      .time {
        font-size: 12px;
        p{
          line-height: 20px;
        }
      }
      .num {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 40px 0;
        color: #5793f7;
        border-radius: 2px;
        padding: 5px 5px  5px 0;
      }
    }

    .center {
      .item {
        font-size: 12px;
        margin-bottom: 25px;
        i {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 100%;
          background-color: #5893f7;
          margin-right: 10px;
        }
        &:nth-child(2) {
          i {
            background-color: #f9dd74;
          }
        }
        &:nth-child(3) {
          i {
            background-color: #70809b;
          }
        }
        &:nth-child(4) {
          i {
            background-color: #56be93;
          }
        }
      }
    }
  }
  .left {
    margin-right: 50px;
  }
  .right {
    .center {

      .item {
        font-size: 12px;
        margin-top: 30px;
        margin-bottom: 40px;
        i{
          background-color: #5ecfc6;
        }
        &:nth-child(2) {
          i {
            background-color: #215dbf;
          }
        }
      }
    }
  }
}

</style>
