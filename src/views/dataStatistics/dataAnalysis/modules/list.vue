<template>
  <div class="mianPage">
    <div class="left">
      <div class="headerTitle">账号评论榜</div>
      <div style="display: flex;align-items: center;justify-content: space-between;padding: 20px 40px 0 10px;">
        <el-date-picker
          v-model="commmentTime" style="width: 150px;margin-right: 50px;" class="commentTime"
          type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
          :editable="false" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
          clearable
          @change="timeChange"
        />
        <el-select v-model="commentNum" clearable @change="commentChange">
          <!-- 1.0.9 小编回复 -->
          <el-option label="昨天" value="7" />
          <el-option label="周榜" value="1" />
          <el-option label="月榜" value="2" />
          <el-option label="全部" value="3" />
        </el-select>
      </div>
      <div class="outPut" @click="output"></div>
      <el-divider />
      <div class="header">
        <div v-if="commentNum != 3 && commentNum != 7" class="time2">数据日期：{{ allData.time1 }}-{{ allData.time2 }}</div>
        <div class="time">时间更新于：{{ allData.time }}</div>
        <el-table :data="allData.commentList" style="width: 100%" :height="525">
          <el-table-column v-slot="scope" align="center" label="排名" width="80">
            {{ scope.$index + 1 }}
          </el-table-column>
          <el-table-column prop="check_admin_name" align="center" label="账号" width="245" />
          <el-table-column align="center" width="320">
            <template #header>
              后台发布评论：<span style="color:#08D2C7">马甲评论</span>/<span style="color:#085BC6">马甲回复+小编评论</span>
            </template>
            <template #default="scope">
              {{ scope.row.t_total_comment_task_cnt }} (<span style="color:#08D2C7">{{
                scope.row.t_comment_task_cnt }}</span>/<span style="color:#085BC6">{{
                scope.row.t_check_comment_reply_cnt }})</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="right">
      <div class="headerTitle">账号审核榜</div>
      <div style="display: flex;align-items: center;justify-content: space-between;padding: 20px 40px 0 10px;">
        <el-date-picker
          v-model="auditTime" style="width: 150px;margin-right: 50px;" class="commentTime"
          type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
          :editable="false" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
          clearable
          @change="timeChange2"
        />
        <el-select v-model="auditNum" clearable @change="handleTabComment2">
          <el-option label="昨天" value="8" />
          <el-option label="周榜" value="4" />
          <el-option label="月榜" value="5" />
          <el-option label="全部" value="6" />
        </el-select>
      </div>
      <div class="outPut" @click="output2"></div>
      <el-divider />
      <div class="header">
        <div v-if="auditNum != 6 && auditNum != 8" class="time2">数据日期：{{ allData2.time1 }}-{{ allData2.time2 }}</div>
        <div class="time">时间更新于：{{ allData2.time }}</div>
        <el-table :data="allData2.auditList" style="width: 100%" :height="525">
          <el-table-column v-slot="scope" align="center" label="排名" width="80">
            {{ scope.$index + 1 }}
          </el-table-column>
          <el-table-column prop="check_admin_name" align="center" label="账号" width="245" />
          <el-table-column align="center" width="320">
            <template #header>
              评论审核总数：<span style="color:#1272FF">通过</span> / <span style="color:#FF4B4B">删除</span>
            </template>
            <template #default="scope">
              {{ scope.row.all_audit }} (<span style="color:#1272FF">{{
                scope.row.t_check_pass_comment_cnt }}</span>/<span style="color:#FF4B4B">{{
                scope.row.t_check_delete_comment_cnt }})</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup name='list'>
import { ref, onBeforeMount, onMounted, watch } from 'vue'
import { requests } from '@/api/business/dataStatistics'
const commentNum = ref('1')
const auditNum = ref('4')
const commmentTime = ref([])
const auditTime = ref([])
const allData = ref({
  commentList: [],
  time1: '',
  time2: '',
  time: ''
})
const allData2 = ref({
  auditList: [],
  time1: '',
  time2: '',
  time: ''
})
onMounted(() => {
  init()
  init2()
})
function timeChange() {
  commentNum.value = ''
  requests.date({ start_time: commmentTime.value[0], end_time: commmentTime.value[1], type: 1 }).then(res => {
    allData.value.commentList = res.data.list
    allData.value.time1 = commmentTime.value[0]
    allData.value.time2 = commmentTime.value[1]
    // allData.value.time = res.data.result.time
  }).catch(error => {
    console.log(error)
  })
}
function timeChange2() {
  auditNum.value = ''
  requests.date({ start_time: auditTime.value[0], end_time: auditTime.value[1], type: 2 }).then(res => {
    allData2.value.auditList = res.data.list
    allData.value.time1 = auditTime.value[0]
    allData.value.time2 = auditTime.value[1]
    // allData.value.time = res.data.result.time
  }).catch(error => {
    console.log(error)
  })
}
// 导出
function output() {
  if (commentNum.value) {
    window.open(import.meta.env.VITE_APP_API_BASEURL + 'statistics/export_excel?select_date=' + commentNum.value)
  } else {
    window.open(import.meta.env.VITE_APP_API_BASEURL + 'statistics/export_excel?select_date=10&start_time=' + commmentTime.value[0] + '&end_time=' + commmentTime.value[1])
  }

}
function output2() {
  if (auditNum.value) {
    window.open(import.meta.env.VITE_APP_API_BASEURL + 'statistics/export_excel?select_date=' + auditNum.value)
  } else {
    window.open(import.meta.env.VITE_APP_API_BASEURL + 'statistics/export_excel?select_date=11&start_time=' + auditTime.value[0] + '&end_time=' + auditTime.value[1])
  }
}
function commentChange(v) {
  if (!commentNum.value) {
    return
  }
  commmentTime.value = []
  init()
}
function handleTabComment2(v) {
  if (!auditNum.value) {
    return
  }
  auditTime.value = []
  init2()
}

function init() {
  requests.rank_list({ select_date: commentNum.value }).then(res => {
    if (commentNum.value == 1) {
      allData.value.commentList = res.data.result.commentWeekList
      allData.value.time1 = res.data.result.week1
      allData.value.time2 = res.data.result.week2
    } else if (commentNum.value == 2) {
      allData.value.commentList = res.data.result.commentMonthList
      allData.value.time1 = res.data.result.month1
      allData.value.time2 = res.data.result.month2
    } else if (commentNum.value == 7) {
      allData.value.commentList = res.data.result.commentWeekList
      allData.value.time1 = res.data.result.month1
      allData.value.time2 = res.data.result.month2
    } else {
      allData.value.commentList = res.data.result.commentAllList
    }
    allData.value.time = res.data.result.time
  }).catch(error => {
    console.log(error)
  })
}
function init2() {
  requests.rank_list({ select_date: auditNum.value }).then(res => {
    if (auditNum.value == 4) {
      allData2.value.auditList = res.data.result.auditWeekList
      allData2.value.time1 = res.data.result.week1
      allData2.value.time2 = res.data.result.week2
      console.log(allData)
    } else if (auditNum.value == 5) {
      allData2.value.auditList = res.data.result.auditMonthList
      allData2.value.time1 = res.data.result.month1
      allData2.value.time2 = res.data.result.month2
    } else {
      allData2.value.auditList = res.data.result.auditAllList
    }
    allData2.value.time = res.data.result.time
  }).catch(error => {
    console.log(error)
  })

}
</script>
<style lang="scss" scoped>
.mianPage {
    overflow: hidden;
    padding-bottom: 30px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;

    .left,
    .right {
        flex-shrink: 0;
        width: 650px;
        position: relative;
        margin-top: 30px;
        border: 1px solid #e4e7ed;

        .outPut {
            cursor: pointer;
            width: 20px;
            height: 20px;
            background: url("@/assets/images/derive.png") no-repeat;
            background-size: 20px auto;
            position: absolute;
            top: 96px;
            right: 6px;
            z-index: 1;
        }

        .headerTitle {
            width: 210px;
            height: 42px;
            background: url("@/assets/images/dataAnalysis.png") no-repeat;
            background-size: 210px auto;
            text-align: center;
            color: #fff;
            line-height: 42px;
            margin: 0 auto 30px auto;
        }

        .header {
            margin-top: 15px;

            .title {
                font-size: 16px;
                margin-bottom: 10px;
            }

            .time {
                text-align: right;
                font-size: 12px;
                margin-bottom: 20px;
                color: rgba(0, 0, 0, 0.4);
                margin-right: 25px;
                float: right;
            }

            .time2 {
                text-align: left;
                font-size: 12px;
                margin-bottom: 20px;
                color: rgba(0, 0, 0, 0.4);
                margin-left: 25px;
                float: left;
            }
        }
    }

    .el-divider--horizontal {
        margin-top: -1px;
    }
}
</style>
