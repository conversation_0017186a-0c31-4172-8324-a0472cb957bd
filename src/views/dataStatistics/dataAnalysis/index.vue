<template>
  <AbsoluteContainer>
    <page-main>
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane v-auth="'statistics:number'" label="评论总数" name="comment">
          <Comment></Comment>
        </el-tab-pane>
        <el-tab-pane v-auth="'statistics:rank'" label="敬请期待" name="list">
          <List></List>
        </el-tab-pane>
      </el-tabs>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup name="cardList">
import Comment from '../dataAnalysis/modules/comment.vue'
import List from '../dataAnalysis/modules/list.vue'
// import { requests } from "@/api/business/dataStatistics";
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const activeName = ref('comment')
const data = ref({
  count: 0
})
onMounted(() => {
  // init();
})
// const init = () => {
//   requests
//     .count()
//     .then((res) => {
//       console.log(res);
//       if (res.code == 0) {
//         data.value.count = res.data.count;
//       }
//     })
//     .catch((error) => {
//       console.log(error);
//     });
// };
const go = () => {
  router.push({
    path: '/auditManagement/adopt'
  })
}
const handleClick = (tab, event) => {
  console.log(tab, event)
}
</script>

<style lang="scss" scoped>
.page-main {
    display: flex;
    flex-direction: column;
    // 减去的 40px 为 page-main 的上下 margin
    // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
    height: calc(100% - 30px);
    overflow: auto;
  .box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
