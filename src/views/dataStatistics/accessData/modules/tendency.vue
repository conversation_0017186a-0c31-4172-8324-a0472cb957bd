<template>
  <div class="boxMain">
    <div class="hedaer">
      <div class="left">
        有效/优质评论趋势
        <el-tooltip placement="bottom">
          <template #content>
            1、有效评论：评论系统后台已通过评论数<br />
            2、优质评论：根据系统自动筛选+评论系统后台审核的潮评系统自动筛选优质评论规则：<br />
            a、评论字数≥10个中文，且连字不超过6个字符、表情不超过两个。<br />
            b、发表的评论，点赞数≥10个<br />
            c、盖楼数（跟评）≥1条<br />
            d、以上数据统计周期为3天<br />
          </template>
          <span class="tip"></span>
        </el-tooltip>
      </div>
    </div>
    <div class="box" style="overflow: hidden">
      <div class="left">
        <div class="infoTop">
          <div class="num">
            <span>{{
              props.dataInfo.filter_mj_effective_comment_cnt_total_sum
            }}</span>条
          </div>
          <div class="num2">
            <span
              :class="
                props.dataInfo.filter_mj_effective_comment_cnt_total_sum_h.replace(
                  '%',
                  ''
                ) >= 0
                  ? 'icon'
                  : 'icon2'
              "
            ></span>
            {{ props.dataInfo.filter_mj_effective_comment_cnt_total_sum_h }}
          </div>
        </div>
        <div class="infoBot">
          <div class="num">
            <span>{{
              props.dataInfo.filter_mj_excellent_comment_cnt_total_sum
            }}</span>条
          </div>
          <div class="num2">
            <span
              :class="
                props.dataInfo.filter_mj_excellent_comment_cnt_total_sum_h.replace(
                  '%',
                  ''
                ) >= 0
                  ? 'icon'
                  : 'icon2'
              "
            ></span>
            {{ props.dataInfo.filter_mj_excellent_comment_cnt_total_sum_h }}
          </div>
        </div>
      </div>
      <div
        class="echarts-box"
        style="width: 100%; "
      >
        <div
          id="myEcharts1"
          ref="myEcharts1"
          :style="{height: '320px'}"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup name='distribution'>
import * as echarts from 'echarts'
import { ref, onBeforeMount, onMounted, watch } from 'vue'
let echart1 = echarts
const myEcharts1 = ref()

const props = defineProps({
  dataInfo: Object,
  checkValue: Boolean
})

watch(
  [() => props.checkValue, () => props.dataInfo],
  ([checkValue, newDataInfo]) => {
    initChart1(newDataInfo.statistics_data_copilot_list, checkValue)
  },
  { deep: true }
)
function initChart1 (value, checkValue) {
  // 日期
  const summaryDates = value.map(item => String(item.summary_date))
  // 有效
  const effective_comment_cnt = !checkValue
    ? value.map(item => item.effective_comment_cnt)
    : value.map(item => item.filter_mj_effective_comment_cnt)
  // 优质
  const excellent_comment_cnt = !checkValue
    ? value.map(item => item.excellent_comment_cnt)
    : value.map(item => item.filter_mj_excellent_comment_cnt)
  let chart1 = echart1.init(myEcharts1.value)
  window.addEventListener('resize', function () {
    chart1.resize()
  })
  
  // 把配置和数据放这里
  chart1.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['有效评论', '优质评论']
    },
    grid: {
      left: '3%',
      right: '2%', // 设置后侧内边距为0
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: summaryDates,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '',
        max: 'auto',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '有效评论',
        type: 'bar',
        barWidth: 30,
        itemStyle: {
          color: '#4E7BFE' // 设置柱子颜色为蓝色
        },
        tooltip: {
          valueFormatter: function (value) {
            return value
          }
        },
        data: effective_comment_cnt
      },

      {
        name: '优质评论',
        type: 'line',
        yAxisIndex: 0,
        itemStyle: {
          color: '#60C5FE' // 设置柱子颜色为蓝色
        },
        tooltip: {
          valueFormatter: function (value) {
            return value
          }
        },
        data: excellent_comment_cnt
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]
  })
}
</script>

<style lang="scss" scoped>

.boxMain {
  clear: both;
  background-color: #fff;
  padding: 0 20px;
  margin-top: 20px;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .left {
      float: left;
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 10px;
      }
    }
  }
  .left {
    float: left;
  }
  .infoTop {
    width: 232px;
    height: 101px;
    background: url("@/assets/images/qushi1.png") no-repeat;
    background-size: 232px auto;
    position: relative;
    margin-bottom: 15px;
    .num {
      position: absolute;
      left: 16px;
      top: 35px;
      height: 34px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.42);
      span {
        font-size: 24px;
        color: #333;
      }
    }
    .num2 {
      position: absolute;
      left: 97px;
      top: 72px;

      font-size: 12px;
    }
  }
  .icon {
    display: inline-block;
    width: 11px;
    height: 11px;
    background: url("@/assets/images/arrow-top.png") no-repeat;
    background-size: 11px auto;
  }
  .icon2 {
    display: inline-block;
    width: 11px;
    height: 11px;
    background: url("@/assets/images/arrow-bot.png") no-repeat;
    background-size: 11px auto;
  }
  .infoBot {
    width: 232px;
    height: 101px;
    background: url("@/assets/images/qushi2.png") no-repeat;
    background-size: 232px auto;
    position: relative;
    .num {
      position: absolute;
      left: 16px;
      top: 35px;
      height: 34px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.42);
      span {
        font-size: 24px;
        color: #333;
      }
    }
    .num2 {
      position: absolute;
      left: 97px;
      top: 72px;
      font-size: 12px;
    }
  }
  .echarts-box {
    position: relative;
    margin-top: 20px;
 
  }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>