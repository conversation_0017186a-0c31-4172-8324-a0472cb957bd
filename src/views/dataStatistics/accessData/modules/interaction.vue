<template>
  <div class="boxMain">
    <div class="hedaer">
      <div class="left">
        评论互动量
        <el-tooltip
          class="box-item"
          effect="dark"
          content="评论互动量计算方式：选择周期内总评论量（包含跟评数）+总点赞量"
          placement="bottom"
        >
          <span class="tip"></span>
        </el-tooltip>
      </div>
      <div class="right">
        <div :class="view.id1 ? 'viewHover' : 'view'" @click="setAll">全部</div>
        <div :class="view.id2 ? 'viewHover' : 'view'" @click="setComment">
          评论
        </div>
        <div
          :class="view.id3 ? 'viewHover' : 'view'"
          plain
          size="small"
          @click="setLike"
        >
          点赞
        </div>
      </div>
    </div>
    <div class="box">
      <div class="echarts-box">
        <div
          id="myEcharts1"
          ref="myEcharts1"
          :style="{ width: '320px', height: '320px' }"
        ></div>
        <div class="echarts1Info">
          <span>{{ 
            (filterComment && !filterLike)
              ? props.dataInfo.filter_mj_comment_cnt_total_sum
              : (filterLike && !filterComment)
                ? props.dataInfo.filter_mj_comment_like_cnt_total_sum
                : (props.dataInfo.filter_mj_comment_cnt_total_sum + props.dataInfo.filter_mj_comment_like_cnt_total_sum)
          }}</span>
          <p>{{ commentLikeText }}</p>
        </div>
      </div>
      <div
        id="myEcharts2"
        ref="myEcharts2"
        :style="{ width: '100%', height: '320px' }"
      ></div>
    </div>
  </div>
</template>

<script setup name='interaction'>
import * as echarts from 'echarts'
import {
  ref,
  onBeforeMount,
  onMounted,
  watch,
  defineProps,
  defineEmits
} from 'vue'
const props = defineProps({
  dataInfo: Object,
  checkValue: Boolean
})
const view = ref({
  id1: true,
  id2: false,
  id3: false
})
let echart1 = echarts
let echart2 = echarts
const emit = defineEmits(['allData'])
const myEcharts1 = ref()
const myEcharts2 = ref()
const allData = ref()
const filterComment = ref(false)
const filterLike = ref(false)
const commentLikeText = ref('总评论互动量')
const setCommentFlag = ref(false)
const setLikeFlag = ref(false)
const setAll = () => {
  emit('allData')
  filterComment.value = false
  filterLike.value = false
  commentLikeText.value = '总评论互动量'
  view.value.id1 = true
  view.value.id2 = false
  view.value.id3 = false
}
const setComment = () => {
  emit('allData')
  setCommentFlag.value = true
  setLikeFlag.value = ref(false)
  setTimeout(() => {
    props.dataInfo.filter_mj_comment_like_cnt_total_sum = []
    filterComment.value = true
    filterLike.value = false
    commentLikeText.value = '总评论量'
    view.value.id1 = false
    view.value.id2 = true
    view.value.id3 = false
  }, 800)
}
const setLike = () => {
  emit('allData')
  setLikeFlag.value = true
  setCommentFlag.value = false
  setTimeout(() => {
    props.dataInfo.filter_mj_comment_cnt_total_sum = []
    filterLike.value = true
    filterComment.value = false
    commentLikeText.value = '总点赞量'
    view.value.id1 = false
    view.value.id2 = false
    view.value.id3 = true
  }, 800)
}
watch(
  [() => props.checkValue, () => props.dataInfo],
  ([checkValue, newDataInfo]) => {
    allData.value = props.dataInfo
    initChart1(
      newDataInfo.filter_mj_comment_cnt_total_sum,
      newDataInfo.filter_mj_comment_like_cnt_total_sum,
      checkValue
    )
    initChart2(newDataInfo.statistics_data_copilot_list, checkValue)
  },
  { deep: true }
)

onMounted(() => {})

function initChart1 (cnt, like_cnt, checkValue) {
  // 评论数
  if (!filterLike.value) {
    like_cnt
  } else {
    cnt = []
  }
  // 点赞数
  if (!filterComment.value) {
    cnt
  } else {
    like_cnt = []
  }

  let chart1 = echart1.init(myEcharts1.value, 'dark')
  window.addEventListener('resize', function () {
    chart1.resize()
  })
  // 把配置和数据放这里
  chart1.setOption({
    tooltip: {
      trigger: 'item'
    },
    backgroundColor: '#fff',
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
          borderColor: '#fff',
          borderWidth: 10
        },
        label: {
          show: false
        },
        color: ['#1279FF', '#2DB6FB'],
        data: [
          { value: cnt, name: '评论数' },
          { value: like_cnt, name: '点赞数' }
        ]
      }
    ]
  })
}
function initChart2 (value, checkValue) {
  // 日期
  const summaryDates = value.map(item => String(item.summary_date))

  let comment_like_cnt
  let comment_cnt
  // 评论数
  if (!filterLike.value) {
    comment_cnt = !checkValue
      ? value.map(item => item.comment_cnt)
      : value.map(item => item.filter_mj_comment_cnt)
  } else {
    comment_cnt = []
  }
  // 点赞数
  if (!filterComment.value) {
    comment_like_cnt = !checkValue
      ? value.map(item => item.comment_like_cnt)
      : value.map(item => item.filter_mj_comment_like_cnt)
  } else {
    comment_like_cnt = []
  }

  let chart2 = echart2.init(myEcharts2.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart2.resize()
  })
  chart2.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '0%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: summaryDates
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    color: ['#1279FF', '#2DB6FB'],
    series: [
      {
        name: '评论数',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        barWidth: 20,
        data: comment_cnt
      },
      {
        name: '点赞数',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        data: comment_like_cnt
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]
  })
}
</script>
<style lang="scss" scoped>
.boxMain {
  background-color: #fff;
  padding: 0 20px;
  clear: both;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .left {
      float: left;
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 10px;
      }
    }
    .right {
      float: right;
      margin-top: 20px;
      .view {
        width: 48px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        float: left;
        font-size: 14px;
        cursor: pointer;
      }
      .viewHover {
        width: 48px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        float: left;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
        cursor: pointer;
      }
    }
  }
  .echarts-box {
    position: relative;
    margin-top: 20px;
    float: left;
    .echarts1Info {
      width: 180px;
      height: 120px;
      text-align: center;
      position: absolute;
      left: 70px;
      top: 120px;
      z-index: 999;
      overflow: hidden;
      span {
        font-weight: 600;
        font-size: 30px;
      }
      p {
        font-size: 14px;
      }
    }
  }
    #myEcharts1 {
    }
    #myEcharts2 {
      padding-right: 0;
    }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>