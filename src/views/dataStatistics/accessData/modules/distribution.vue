<template>
  <div class="boxMain">
    <div class="hedaer">
      <div class="left">
        评论指数分布
        <el-tooltip placement="bottom">
          <template #content>
            1、客户端内各频道（含潮客及视频）评论及跟评总数。<br />
            2、环比率：计算当前周期对比上一周期升降情况（如当前周期为8月27日-9月2日。则上一周期为8月20日-8月26日）<br />
          </template>
          <span class="tip"></span>
        </el-tooltip>
      </div>
      <div class="right">
        <div :class="view ? 'view' : 'view2'" @click="handleView()">视图</div>
        <div :class="!view ? 'list' : 'list2'" @click="view = false">列表</div>
      </div>
    </div>
    <div v-show="view" class="echarts-box" style="width: 100%;">
      <div id="myEcharts1" ref="myEcharts1" :style="{ height: '360px',width: '100%'}"></div>
    </div>
    <div v-show="!view" class="listBox">
      <el-table :data="tableData.slice(0, 19)" style="width: 25%; float: left">
        <el-table-column prop="id" align="center" label="排名" />
        <el-table-column label="频道" align="center">
          <template #default="scope">
            <div class="channel-name" :title="scope.row.name">
              {{ scope.row.name.length > 3 ? scope.row.name.substring(0, 3) + '...' : scope.row.name }}
            </div>
          </template>
         </el-table-column>
        <el-table-column prop="value" label="评论量" align="center" />
        <el-table-column
          v-slot="scope"
          label="环比"
          align="center"
          width="130px"
        >
          <div
            :style="
              scope.row.Percentage > 0 ? { color: 'red' } : { color: '#1279FF' }
            "
          >
            {{
              /\d+/.test(scope.row.Percentage)
                ? scope.row.Percentage + "%"
                : scope.row.Percentage
            }}
          </div>
        </el-table-column>
      </el-table>
      <el-table :data="tableData.slice(19, 38)" style="width: 25%; float: left">
        <el-table-column prop="id" align="center" label="排名" />
        <el-table-column label="频道" align="center">
          <template #default="scope">
            <div class="channel-name" :title="scope.row.name">
              {{ scope.row.name.length > 3 ? scope.row.name.substring(0, 3) + '...' : scope.row.name }}
            </div>
          </template>
         </el-table-column>
        <el-table-column prop="value" label="评论量" align="center" />
        <el-table-column
          v-slot="scope"
          label="环比"
          align="center"
          width="130px"
        >
          <div
            :style="
              scope.row.Percentage > 0 ? { color: 'red' } : { color: '#1279FF' }
            "
          >
            {{
              /\d+/.test(scope.row.Percentage)
                ? scope.row.Percentage + "%"
                : scope.row.Percentage
            }}
          </div>
        </el-table-column>
      </el-table>
      <el-table :data="tableData.slice(38, 57)" style="width: 25%; float: left">
        <el-table-column prop="id" align="center" label="排名" />
        <el-table-column label="频道" align="center">
          <template #default="scope">
            <div class="channel-name" :title="scope.row.name">
              {{ scope.row.name.length > 3 ? scope.row.name.substring(0, 3) + '...' : scope.row.name }}
            </div>
          </template>
         </el-table-column>

        <el-table-column prop="value" label="评论量" align="center" />
        <el-table-column
          v-slot="scope"
          label="环比"
          align="center"
          width="130px"
        >
          <div
            :style="
              scope.row.Percentage > 0 ? { color: 'red' } : { color: '#1279FF' }
            "
          >
            {{
              /\d+/.test(scope.row.Percentage)
                ? scope.row.Percentage + "%"
                : scope.row.Percentage
            }}
          </div>
        </el-table-column>
      </el-table>
      <el-table :data="tableData.slice(57, 76)" style="width: 25%; float: left">
        <el-table-column prop="id" align="center" label="排名" />
        <el-table-column label="频道" align="center">
          <template #default="scope">
            <div class="channel-name" :title="scope.row.name">
              {{ scope.row.name.length > 3 ? scope.row.name.substring(0, 3) + '...' : scope.row.name }}
            </div>
          </template>
         </el-table-column>
        <el-table-column prop="value" label="评论量" align="center" />
        <el-table-column
          v-slot="scope"
          label="环比"
          align="center"
          width="130px"
        >
          <div
            :style="
              scope.row.Percentage > 0 ? { color: 'red' } : { color: '#1279FF' }
            "
          >
            {{
              /\d+/.test(scope.row.Percentage)
                ? scope.row.Percentage + "%"
                : scope.row.Percentage
            }}
          </div>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup name='distribution'>
import * as echarts from 'echarts'
import { ref, onBeforeMount, onMounted, watch } from 'vue'
let echart1 = echarts
const myEcharts1 = ref()
const props = defineProps({
  dataInfo: Object,
  checkValue: Boolean
})

const tableData = ref([])
const view = ref(true)
watch(
  [() => props.checkValue, () => props.dataInfo],
  ([checkValue, newDataInfo]) => {
    initChart1(newDataInfo.filter_mj_data_statistics_channel_audit_entities)
  },
  { deep: true }
)
const handleView = () => {
  view.value = true
  chart1.resize()
}
function initChart1 (value) {
  // 取第二条数据
  const sortedData = value.sort((a, b) => b.value - a.value)
  const secondLargestValue = sortedData[1].comments_cnt
  // 全前15条 排序 并设置第一条的区块值大小，防止第一条区块值太大
  const convertedData = value
    .sort((a, b) => b.value - a.value)
    .slice(0, 15)
    .map(({ channel, comments_cnt, comments_cnt_hb }) => ({
      name: `${channel}  ${comments_cnt} `,
      value: comments_cnt > secondLargestValue ? (comments_cnt = secondLargestValue * 1.2) : comments_cnt,
      Percentage: comments_cnt_hb || 0,
      itemStyle: {
        color: comments_cnt_hb.includes('-') ? '#4EA5FE' : '#128FFF' // 设置背景颜色为淡蓝色
      },
      label: {
        show: true,
        position: 'inside',
        formatter: params => `${params.name}\n\n${params.data.Percentage}`
      }
    }))
  // 列表数据 排序
  tableData.value = value
    .sort((a, b) => b.comments_cnt - a.comments_cnt)
    .map((item, index) => ({
      id: index + 1,
      name: item.channel,
      value: item.comments_cnt,
      Percentage: item.comments_cnt_hb
        ? item.comments_cnt_hb.includes('%')
          ? item.comments_cnt_hb.replace('%', '')
          : item.comments_cnt_hb
        : 0
    }))

  let chart1 = echart1.init(myEcharts1.value, 'treemap')
  window.addEventListener('resize', function () {
    chart1.resize()
  })
  // 把配置和数据放这里
  chart1.setOption({
    grid: {
      right: 0
    },
    series: [
      {
        width: '100%',
        height: '100%',
        type: 'treemap',
        roam: false,
        nodeClick: false,
        breadcrumb: {
          show: false // 将 breadcrumb 设置为不显示
        },
        itemStyle: {
          gapWidth: 3,
          borderColorSaturation: 0.6,
          maxSize: 200
        },
        data: convertedData
      }
    ],
   
    dataZoom: [
      {
        type: 'slider',
        orient: 'horizontal', // 设置为横向滚动条
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100
      }
    ]
  })
}
</script>

<style lang="scss" scoped>
.boxMain {
  clear: both;
  background-color: #fff;
  padding: 0 20px;
  margin-top: 20px;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    .left {
      float: left;
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 10px;
      }
    }
    .right {
      float: right;
      cursor: pointer;
    }
    .view {
      width: 48px;
      height: 24px;
      background: #e5e5e5;
      border-radius: 3px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      display: inline-block;
    }
    .view2 {
      width: 48px;
      height: 24px;
      border-radius: 3px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      display: inline-block;
    }
    .list {
      background: #e5e5e5;
      width: 48px;
      height: 24px;
      border-radius: 3px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      display: inline-block;
    }
    .list2 {
      width: 48px;
      height: 24px;
      border-radius: 3px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      display: inline-block;
    }
  }
  .echarts-box {
    position: relative;
    margin-top: 20px;
    float: left;
    margin-left: -70px;
    .echarts1Info {
      width: 180px;
      height: 120px;
      text-align: center;
      position: absolute;
      left: 70px;
      top: 120px;
      z-index: 999;
      overflow: hidden;
      span {
        font-weight: 600;
        font-size: 30px;
      }
      p {
        font-size: 14px;
      }
    }
  }
  .listBox {
    overflow: hidden;
    padding: 10px 0 30px 0;
    ul {
      width: 410px;
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      li {
      }
    }
  }
  .box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  #myEcharts1 {
    margin-left: 70px;
    padding: 10px 0 40px 0;
  }
}
</style>