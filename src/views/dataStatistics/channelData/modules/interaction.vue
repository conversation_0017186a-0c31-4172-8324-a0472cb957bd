<template>
  <div class="boxMain">
    <div class="hedaer">
      <div class="left">
        评论量
        <el-tooltip
          class="box-item"
          effect="dark"
          content="指定的时间周期内，统计每个频道的以下数据：评论的总数量、有效的评论数量、以及评论的回复数量。以及统计量与上一个相同时间周期内的数据相比的环比增长率。"
          placement="bottom"
        >
          <span class="tip"></span>
        </el-tooltip>
      </div>
    </div>
    
    <div class="box">
      <div class="echarts-box">
        <div class="numData">
          <div class="hander">
            <div class="top">评论总数</div>
            <div class="num">
              {{ props.dataInfo.effective_comment_cnt_total_sum }}
              <span :style="{ color: getColor(props.dataInfo.effective_comment_cnt_total_sum_h) } ">{{ props.dataInfo.effective_comment_cnt_total_sum_h }}</span>
            </div>
          </div>
          <div class="fix">
            <div class="top">有效评论数</div>
            <div class="num">
              {{ props.dataInfo.comment_cnt_total_sum }}
              <p :style="{ color: getColor(props.dataInfo.comment_cnt_total_sum_h) } ">{{ props.dataInfo.comment_cnt_total_sum_h }}</p>
            </div>
          </div>
          <div class="fix" style="float: right;">
            <div class="top">评论回复数</div>
            <div class="num">
              {{ props.dataInfo.reply_comment_cnt_total_sum }}
              <p :style="{ color: getColor(props.dataInfo.reply_comment_cnt_total_sum_h) } ">{{ props.dataInfo.reply_comment_cnt_total_sum_h }}</p>
            </div>
          </div>
        </div>
      </div>
      <div
        id="myEcharts2"
        ref="myEcharts2"
        :style="{ width: '100%', height: '260px' }"
      ></div>
    </div>
  </div>
</template>

<script setup name='interaction'>
import * as echarts from 'echarts'
import {
  ref,
  onBeforeMount,
  onMounted,
  watch,
  defineProps,
  defineEmits
} from 'vue'
const props = defineProps({
  dataInfo: Object,
  checkValue: Boolean
})
let echart2 = echarts
const emit = defineEmits(['allData'])
const myEcharts2 = ref()
const allData = ref()

watch(
  [() => props.checkValue, () => props.dataInfo],
  ([checkValue, newDataInfo]) => {
    allData.value = props.dataInfo
    console.log(props.dataInfo)
    initChart2(newDataInfo.line_chart_list, checkValue)
  },
  { deep: true }
)
const getColor = percentageString => {
  const percentage = parseFloat(percentageString)
  if (percentage <= 0) {
    return '#00D4A6'
  } else {
    return '#FF0000'
  }
}
function initChart2 (value, checkValue) {
  // 日期
  const summaryDates = value.map(item => String(item.summary_date))
  // 总数 
  let total_comment_cnt
  // 有效评论数
  let effective_comment_cnt
  // 回复数
  let comment_reply_cnt

  if (!checkValue) {
    total_comment_cnt = value.map(item => item.total_comment_cnt)
  } else {
    total_comment_cnt = value.map(item => item.mj_total_comment_cnt)
  }
  effective_comment_cnt = value.map(item => item.effective_comment_cnt)
  comment_reply_cnt = value.map(item => item.comment_reply_cnt)
  let chart2 = echart2.init(myEcharts2.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart2.resize()
  })
  chart2.setOption({
    color: [  '#FF7932', '#4D9FFF', '#91CB76' ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['评论回复数', '有效评论数', '评论总数']
    },
    toolbox: {
      feature: {}
    },
    grid: {
      left: '3%',
      right: '3.5%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      boundaryGap: false,
      data: summaryDates
      
    }],
    yAxis: [{
      type: 'value'
    }],
    series: [
      {
        name: '评论回复数',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: comment_reply_cnt
      },
      {
        name: '有效评论数',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: total_comment_cnt
      },
      {
        name: '评论总数',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: effective_comment_cnt
      }
      
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]
  })
}
</script>
<style lang="scss" scoped>
.boxMain {
  background-color: #fff;
  padding: 0 20px;
  clear: both;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    .left {
      float: left;
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 10px;
      }
    }
    .right {
      float: right;
      margin-top: 20px;
      .view {
        width: 48px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        float: left;
        font-size: 14px;
        cursor: pointer;
      }
      .viewHover {
        width: 48px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        float: left;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
        cursor: pointer;
      }
    }
  }
  .echarts-box {
    width: 320px;
    min-width: 320px;
    height: 248px;
    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
    border-radius: 6px; 
    position: relative;
    margin-top: 20px;
    float: left;
    .numData{
      padding: 30px;
      .hander,.fix{
        float: left;
      }
      .hander{
        margin-bottom: 40px;
        width: 100%;
      }
      .top{
        color: rgba(0,0,0,0.45);
        font-size: 14px;
        margin-bottom:5px;
      }
      .num{
        font-size: 24px;
        color: #000;
        span{
          font-size: 14px;
          color: #FF0000;
          margin-left:5px
        }
        p{
          font-size: 14px;
          color: #FF0000;
          margin-left:5px
        }
      }
    }
    .fix p{
      margin-top:0px
    }
  }
  .positive {
    color: #FF0000;
  }

  .negative {
    color: #00D4A6;
  }
    #myEcharts2 {
      padding-right: 0;
    }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>