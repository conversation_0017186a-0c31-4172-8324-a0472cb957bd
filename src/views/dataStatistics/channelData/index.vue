<template>
  <AbsoluteContainer>
    <page-main style="background-color: #f5f7f8">
      <div class="head-box">
        <div class="left">
          <el-select v-model="cascadeValue" style="float:left;margin-right:15px; width: 110px" size="small" placeholder="请选择频道" @change="changeChannel">
            <el-option v-for="item of options" :key="item.channel_id" :label="item.channel" :value="item.channel_id">
            </el-option>
          </el-select>
          <el-date-picker
            v-model="time"
            style="float: left; width: 250px; border-right-width: 0"
            class="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :editable="false"
            :disabled-date="disabledDate"
            size="small"
            @change="handleDateChange"
          >
          </el-date-picker>
          <div class="buttonStyle">
            <el-button
              plain
              size="small"
              class="custom-button"
              style="margin-top: -2px"
              @click="setTime(7)"
            >
              近7天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTime(10)"
            >
              近10天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTime(30)"
            >
              近30天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTimeMonth"
            >
              当月
            </el-button>
          </div>
        </div>
        <el-switch
          v-model="checkValue"
          v-auth="'channelDat:shielding'"
          class="right"
          active-text="屏蔽马甲号"
          @change="handleSwitchChange"
        />
      </div>
      <interaction
        :data-info="dataInfo"
        :check-value="checkValue"
        @allData="getInit"
      ></interaction>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup name="accessData">
import interaction from './modules/interaction.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { requests } from '@/api/business/dataStatistics'
import { ref,  onMounted } from 'vue'
const time = ref([
  new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 700),
  new Date()
])
const cascadeValue = ref('5d8b4d25cf8dfd0001a4143c')
const options = ref([
])
const checkValue = ref(false)

const dataInfo = ref({
  mj_comment_cnt_total_sum: '', // 过滤马甲总数
  mj_comment_cnt_total_sum_h: '' // 过滤马甲总数环比
})
// 马甲号事件
function handleSwitchChange () {
  getInit()
}
function setTime (v) {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * v)
  time.value = [start, end]
  handleDateChange(time.value)
}

function setTimeMonth () {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const firstDay = new Date(year, month, 1)
  const yesterday = new Date(year, month, currentDate.getDate())
  time.value = [firstDay, yesterday]
  handleDateChange(time.value)
}
onMounted(() => {
  getInit()
  handleChannel()
})
const changeChannel = () => {
  getInit()
}
// 日期转换字符串
function convert (dateStr) {
  const date = new Date(dateStr)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}${month}${day}`
}
// 时期事件
function handleDateChange (value) {
  time.value[0] = value[0]
  time.value[1] = value[1]
  getInit()
}

function handleChannel () {
  requests
    .copilot_info({
      summary_date_start: convert(time.value[0]),
      summary_date_end: convert(time.value[1])
    })
    .then(res => {
      if (res.data.code == 0) {
        options.value = res.data.data.filter_mj_data_statistics_channel_audit_entities
      }
    })
}
// 数据获取
function getInit () {
  requests
    .channel_info({
      summary_date_start: convert(time.value[0]),
      summary_date_end: convert(time.value[1]),
      channel_id: cascadeValue.value
    })
    .then(res => {
      if (res.code == 0) {
        dataInfo.value = res.data
        // 判断是否开启马甲号屏蔽
        if (checkValue.value) {
          dataInfo.value.comment_cnt_total_sum = res.data.mj_comment_cnt_total_sum
          dataInfo.value.comment_cnt_total_sum_h = res.data.mj_comment_cnt_total_sum_h
        } 
      } 
    })
}
// 之后日期不能选
function disabledDate (time) {
  return time.getTime() > Date.now()
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  overflow: auto;

  .head-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    margin-top: 20px;
    .left {
      .el-button {
        margin-left: 30px;
      }
    }
    .right {
      & > .el-select {
        width: 110px;
        margin-right: 10px;
      }
    }
  }
  .box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .head-box .left {
    .el-button {
      margin-left: 0px;
      border: none;
      font-size: 12px;
      width: 50px;
    }
    .buttonStyle {
      border: 1px solid #dedede;
      float: left;
      margin-left: 5px;
      height: 24px;
      overflow: hidden;
      border-radius: 3px;
    }
    .time .el-date-editor {
      border-right-width: 0;
    }
  }
}
</style>
