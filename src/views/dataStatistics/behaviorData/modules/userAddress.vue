<template>
  <div class="boxMain">
    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>TOP10用户地区分布</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="统计评论用户（游客+用户）IP地址，并计算前十省份及城市"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="echarts-box" style="width: 50%">
          <div
            id="myEcharts1"
            ref="myEcharts1"
            :style="{ width: '100%', height: '400px' }"
          ></div>
        </div>
        <div class="echarts-box" style="width: 50%">
          <div
            id="myEcharts2"
            ref="myEcharts2"
            :style="{ width: '100%', height: '400px' }"
          ></div>
        </div>
      </div>
    </div>

    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>人均访问时长</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="人均访问时长=总访问时长/总访问量。访问量为包括游客及用户总数。"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="echarts-box" style="width: 100%">
          <div
            id="myEcharts3"
            ref="myEcharts3"
            :style="{ height: '240px' }"
          ></div>
        </div>
      </div>
    </div>

    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>日均活动时间段</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="日均活跃时间段=选中周期内每小时访问量平均值"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="echarts-box" style="width: 100%">
          <div
            id="myEcharts4"
            ref="myEcharts4"
            :style="{ height: '240px' }"
          ></div>
        </div>
      </div>
    </div>

    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>TOP10用户机型</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="统计评论用户使用的手机设备号，取前10名使用频率最高的设备及系统展示"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="iphone_left">
          <div class="icon"></div>
          <div class="circle">
            <div class="circleBox">
              <span>{{ dataInfo.android_p }}</span>
              <p>安卓占比</p>
            </div>
          </div>
          <div class="circle2">
            <div class="circleBox">
              <span>{{ dataInfo.ios_p }}</span>
              <p>苹果占比</p>
            </div>
          </div>
        </div>
        <div class="Android">
          <div class="icon"></div>
          <div class="tabBox">
            <div class="tab1">排名</div>
            <div class="tab2">机型</div>
            <div class="tab3">用户数</div>
          </div>
          <ul v-for="(item, index) of dataInfo.android_cnts" :key="index" class="table">
            <li :class="{'red': index === 0, 'blue': index === 1, 'yellow': index === 2,'tab1':index > 2}">{{ index+1 }}</li>
            <li class="tab2">{{ item.channel }}</li>
            <li class="tab3">{{ item.comments_cnt }}</li>
          </ul>
        </div>
        <div class="phone">
          <div class="icon"></div>
          <div class="tabBox">
            <div class="tab1">排名</div>
            <div class="tab2">机型</div>
            <div class="tab3">用户数</div>
          </div>
          <ul v-for="(item, index) of dataInfo.ios_cnts" :key="index" class="table">
            <li :class="{'red': index === 0, 'blue': index === 1, 'yellow': index === 2,'tab1':index > 2}">{{ index+1 }}</li>
            <li class="tab2">{{ item.channel }}</li>
            <li class="tab3">{{ item.comments_cnt }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name='userAddress'>
import * as echarts from 'echarts'
let echart1 = echarts
let echart2 = echarts
let echart3 = echarts
let echart4 = echarts
import { ref, onMounted, watch, defineProps, defineEmits } from 'vue'
const myEcharts1 = ref()
const myEcharts2 = ref()
const myEcharts3 = ref()
const myEcharts4 = ref()
const props = defineProps({
  dataInfo: Object
})
const emit = defineEmits([''])

onMounted(() => {
})

watch(() => props.dataInfo, newDataInfo => {
  initChart1(newDataInfo)
  initChart2(newDataInfo)
  initChart3(newDataInfo)
  initChart4(newDataInfo)
}, { deep: true }
)

const initChart1 = val => {
  let channel = []
  // 有效
  let comments_cnt_l = []
  // 总数
  let comments_cnt = []
  val.province_cnts.sort((b, a) => b.comments_cnt - a.comments_cnt)
    .forEach(element => {
      channel.push(element.channel)
      comments_cnt_l.push(element.comments_cnt_l)
      comments_cnt.push(element.comments_cnt)
    })

  let chart1 = echart1.init(myEcharts1.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart1.resize()
  })
  chart1.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: channel
    },
    color: ['#1DCCE0', '#4FB0FF'],
    series: [
      {
        name: '有效访问量',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        barWidth: 20,
        data: comments_cnt_l
      },
      {
        name: '总访问量',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        data: comments_cnt
      }
    ]
    // dataZoom: [
    //   {
    //     type: "slider",
    //     xAxisIndex: 0,
    //     filterMode: "weakFilter",
    //     showDataShadow: false,
    //     bottom: 10,
    //     start: 0,
    //     end: 100,
    //   },
    // ],
  })
}

const initChart2 = val => {
  let channel = []
  // 有效
  let comments_cnt_l = []
  // 总数
  let comments_cnt = []
  val.city_cnts.sort((b, a) => b.comments_cnt - a.comments_cnt)
    .forEach(element => {
      channel.push(element.channel)
      comments_cnt_l.push(element.comments_cnt_l)
      comments_cnt.push(element.comments_cnt)
    })

  let chart2 = echart2.init(myEcharts2.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart2.resize()
  })
  chart2.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: channel
    },
    color: ['#1DCCE0', '#4FB0FF'],
    series: [
      {
        name: '有效访问量',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        barWidth: 20,
        data: comments_cnt_l
      },
      {
        name: '总访问量',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        data: comments_cnt
      }
    ]
    // dataZoom: [
    //   {
    //     type: "slider",
    //     xAxisIndex: 0,
    //     filterMode: "weakFilter",
    //     showDataShadow: false,
    //     bottom: 10,
    //     start: 0,
    //     end: 100,
    //   },
    // ],
  })
}
const initChart3 = val => {
  let summary_date = []
  let avg_visit_time = []
  val.entirety_user_actions.forEach(element => {
    summary_date.push(element.summary_date)
    avg_visit_time.push(element.avg_visit_time)
  })
  let chart3 = echart3.init(myEcharts3.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart3.resize()
  })
  chart3.setOption({
    color: ['#7C8DFE'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['人均访问时长']
    },
    toolbox: {
      feature: {}
    },
    grid: {
      left: '1.5%',
      right: '2%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: summary_date,
        axisLabel: {
          rotate: 45 // 将标签文字旋转45度
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    // dataZoom: [
    //   {
    //     type: "slider",
    //       xAxisIndex: 0,
    //       filterMode: "weakFilter",
    //       showDataShadow: false,
    //       bottom: 10,
    //       start: 0,
    //       end: 100,
    //   }
    // ],
    series: [
      {
        name: '人均访问时长（秒）',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: avg_visit_time
      }
    ]
  })
}
const initChart4 = val => {
  // 总数
  let comments_cnt = []

  val.hour_cnts.forEach(element => {
    comments_cnt.push(element.comments_cnt)
  })
  let chart4 = echart4.init(myEcharts4.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart4.resize()
  })
  chart4.setOption({
    color: ['#EF746F'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['日均活动时间段']
    },
    toolbox: {
      feature: {}
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: [ '00:00-01:00', '01:00-02:00', '02:00-03:00', '03:00-04:00', '04:00-05:00', '05:00-06:00', '06:00-07:00', '07:00-08:00', '08:00-09:00', '09:00-10:00', '10:00-11:00', '11:00-12:00', '12:00-13:00',
                '13:00-14:00', '14:00-15:00', '15:00-16:00', '16:00-17:00', '17:00-18:00', '18:00-19:00', '19:00-20:00', '20:00-21:00', '21:00-22:00', '22:00-23:00', '23:00-24:00' ],
        axisLabel: {
          rotate: 45 // 将标签文字旋转45度
        }
      }
      
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    // dataZoom: [
    //   {
    //     type: "slider",
    //       xAxisIndex: 0,
    //       filterMode: "weakFilter",
    //       showDataShadow: false,
    //       bottom: 10,
    //       start: 0,
    //       end: 100,
    //   }
    // ],
    series: [
      {
        name: '日均访问时间段',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: comments_cnt
      }
    ]
  })
}
</script>
<style lang="scss" scoped>
.red {
  color: #FF1A1A;
  font-style: italic;
  width: 55px;
  text-align: center;
  font-weight: bold;
}
.blue {
  color: #327AFE;
  font-style: italic;
  width: 55px;
  text-align: center;
  font-weight: bold;
}
.yellow {
  color: #FCB978;
  font-style: italic;
  width: 55px;
  text-align: center;
  font-weight: bold;
}
.boxMain {
  background-color: #fff;
  .block {
    overflow: hidden;
    border-bottom: 1px solid #efefef;
  }
  clear: both;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    .left {
      float: left;
      i {
        width: 4px;
        height: 14px;
        background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
        border-radius: 3px;
        margin-right: 10px;
        float: left;
        margin-top: 23px;
      }
      em {
        font-style: normal;
      }
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 5px;
      }
    }
  }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;

    .info {
      min-width: 314px;
      float: left;
      .all {
        width: 132px;
        height: 240px;
        background: url("@/assets/images/behavior_1.png") no-repeat;
        background-size: 132px auto;
        float: left;
      }
      .effective {
        width: 132px;
        height: 240px;
        background: url("@/assets/images/behavior_2.png") no-repeat;
        background-size: 132px auto;
        float: left;
        margin-left: 10px;
      }
    }
    .Android,
    .phone {
      position: relative;
      width: 450px;
      min-width: 450px;
      height: 400px;
      background: url("@/assets/images/android_icon.png") no-repeat right bottom #f8fbff;
      background-size: 200px auto;
      .icon {
        width: 111px;
        height: 35px;
        background: url("@/assets/images/behavior_3.png") no-repeat;
        background-size: 111px auto;
        position: absolute;
        left: -10px;
        top: 10px;
      }
      .tabBox {
        overflow: hidden;
        margin-top: 55px;
        margin-bottom: 10px;
        div {
          float: left;
          font-size: 14px;
        }
        .tab1 {
          width: 55px;
          text-align: center;
          margin-left: 45px;
        }
        .tab2 {
          width: 220px;
          text-align: center;
        }
        .tab3 {
          width: 120px;
          text-align: center;
        }
      }
      .table {
        clear: both;
        li {
          float: left;
          list-style: none;
          font-size: 14px;
          margin: 3px 0;
          border-bottom:1px solid #EDF5FD;
          padding-bottom: 3px;
        }
        .tab1 {
          width: 55px;
          text-align: center;
          color: #8ec4ff;
          font-weight: bold;
        }
        .tab2 {
          width: 220px;
          text-align: center;
        }
        .tab3 {
          width: 120px;
          text-align: center;
        }
      }
    }
    
    .phone {
      background: url("@/assets/images/ios_icon.png") no-repeat right bottom #f8fbff;
      background-size: 200px auto;
      .icon {
        background: url("@/assets/images/behavior_4.png") no-repeat;
        background-size: 111px auto;
      }
    }
    .iphone_left {
      position: relative;
      width: 450px;
      min-width: 450px;
      height: 400px;
      background: #f8fbff;
      .icon {
        width: 111px;
        height: 35px;
        background: url("@/assets/images/behavior_5.png") no-repeat;
        background-size: 111px auto;
        position: absolute;
        left: -10px;
        top: 10px;
      }
      .circle,
      .circle2 {
        width: 180px;
        min-width: 180px;
        height: 180px;
        background: linear-gradient(180deg, #259ffe 0%, #92cfff 100%);
        position: absolute;
        left: 20px;
        top: 120px;
        border-radius: 100%;
        .circleBox {
          width: 140px;
          height: 140px;
          background-color: #fff;
          border-radius: 100%;
          position: absolute;
          text-align: center;
          top: 20px;
          left: 20px;
          padding-top: 30px;
          span {
            font-size: 30px;
          }
        }
      }
      .circle2 {
        left: 243px;
      }
    }
  }
  .echarts-box {
    position: relative;
    float: left;
    .echartsInfo {
      width: 180px;
      height: 90px;
      text-align: center;
      position: absolute;
      left: 60px;
      top: 110px;
      z-index: 999;
      overflow: hidden;
      span {
        font-weight: 600;
        font-size: 30px;
      }
      p {
        font-size: 14px;
      }
    }
  }
}
</style>
