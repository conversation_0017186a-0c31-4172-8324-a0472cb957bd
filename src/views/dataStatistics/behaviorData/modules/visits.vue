<template>
  <div class="boxMain">
    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>访问量</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="访问量（游客及用户）进入评论列表的总人数"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="info">
          <div class="all">
            <div class="num">{{ dataInfo.z_visit_cnt }}</div>
          </div>
          <div class="effective">
            <div class="num">{{ dataInfo.z_effective_cnt }}</div>
          </div>
        </div>

        <div class="echarts-box" style="width: 100%;">
          <div id="myEcharts1" ref="myEcharts1" :style="{ height: '240px'}"></div>
        </div>
      </div>
    </div>
    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>总访问量</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="总访问量（游客及用户）进入评论列表的总人数"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="echarts-box" style="width: 300px; margin-top:30px">
          <div
            id="myEcharts2"
            ref="myEcharts2"
            :style="{ width: '300px', height: '300px' }"
          ></div>
          <div class="echartsInfo">
            <span>{{ dataInfo.z_visit_cnt }}</span>
            <p>总访问量</p>
          </div>
        </div>

        <div class="echarts-box" style="width: 100%;">
          <div
            id="myEcharts3"
            ref="myEcharts3"
            :style="{ width: '100%', height: '260px' }"
          ></div>
        </div>
      </div>
    </div>
    <div class="block">
      <div class="hedaer">
        <div class="left">
          <i></i>
          <em>有效问量</em>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="有效访问量（游客及用户）进入评论列表并停留3S以上的总人数"
            placement="bottom"
          >
            <span class="tip"></span>
          </el-tooltip>
        </div>
      </div>
      <div class="box">
        <div class="echarts-box" style="width: 300px; margin-top:30px">
          <div
            id="myEcharts4"
            ref="myEcharts4"
            :style="{ width: '300px', height: '300px' }"
          ></div>
          <div class="echartsInfo">
            <span>{{ dataInfo.z_effective_cnt }}</span>
            <p>有效访问量</p>
          </div>
        </div>

        <div class="echarts-box" style="width: 100%;">
          <div
            id="myEcharts5"
            ref="myEcharts5"
            :style="{ width: '100%', height: '260px' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name='visits'>
import * as echarts from 'echarts'
let echart1 = echarts
let echart2 = echarts
let echart3 = echarts
let echart4 = echarts
let echart5 = echarts
import { ref, onMounted, watch, defineProps, defineEmits } from 'vue'
const myEcharts1 = ref()
const myEcharts2 = ref()
const myEcharts3 = ref()
const myEcharts4 = ref()
const myEcharts5 = ref()
const props = defineProps({
  dataInfo: Object
})
const emit = defineEmits([''])

watch(() => props.dataInfo, newDataInfo => {
  initChart1(newDataInfo.entirety_user_actions)
  initChart2(newDataInfo)
  initChart3(newDataInfo)
  initChart4(newDataInfo)
  initChart5(newDataInfo)
}, { deep: true }
)
onMounted(() => {

})

const initChart1 = val => {
  console.log(val)
  // effective_visit_cnt 有效
  // visit_cnt 总
  let timeValue = []
  let visit_cnt = []
  let effective_visit_cnt = []
  val.forEach(element => {
    visit_cnt.push(element.visit_cnt)
    effective_visit_cnt.push(element.effective_visit_cnt)
    timeValue.push(element.summary_date)
  })
  let chart1 = echart1.init(myEcharts1.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart1.resize()
  })
  chart1.setOption({
    color: [ '#1DCCE0', '#4FB0FF'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['总访问量', '有效访问量']
    },
    toolbox: {
      feature: {}
    },
    grid: {
      left: '0%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      boundaryGap: false,
      data: timeValue
    }],
    yAxis: [{
      type: 'value'
    }],
    series: [
      {
        name: '有效访问量',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: effective_visit_cnt
      },
      {
        name: '总访问量',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: visit_cnt
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]
  })
}
const initChart2 = val => {
  //	用户访问量
  let z_user_cnt = val.z_user_cnt
  // 游客访问量
  let z_visitors_cnt = val.z_visitors_cnt
  let chart2 = echart2.init(myEcharts2.value, 'dark')
  window.addEventListener('resize', function () {
    chart2.resize()
  })
  chart2.setOption({
    tooltip: {
      trigger: 'item'
    },
    backgroundColor: '#fff',
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
          borderColor: '#fff',
          borderWidth: 10
        },
        label: {
          show: false
        },
        color: ['#4FB0FF', '#C09BFF'],
        data: [
          { value: z_visitors_cnt, name: '游客' },
          { value: z_user_cnt, name: '用户' }
        ]
      }
    ]
  })
}

const initChart3 = val => {
  // 用户总数
  let visit_cnt = []; let summary_date = []
  val.user_type_users.forEach(element => {
    visit_cnt.push(element.visit_cnt)
    summary_date.push(element.summary_date)
  })
  // 游客总数
  let visitors = []
  val.user_type_visitors.forEach(element => {
    visitors.push(element.visit_cnt)
  })
  let chart3 = echart3.init(myEcharts3.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart3.resize()
  })
  chart3.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '0%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: summary_date
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    color: ['#4FB0FF', '#C09BFF'],
    series: [
      {
        name: '游客',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        barWidth: 20,
        data: visitors
      },
      {
        name: '用户',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        data: visit_cnt
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]

  })
}
const initChart4 = val => {
  //	用户访问量
  let ze_user_cnt = val.ze_user_cnt
  // 游客访问量
  let ze_visitors_cnt = val.ze_visitors_cnt
  let chart4 = echart2.init(myEcharts4.value, 'dark')
  window.addEventListener('resize', function () {
    chart4.resize()
  })
  chart4.setOption({
    tooltip: {
      trigger: 'item'
    },
    backgroundColor: '#fff',
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 3,
          borderColor: '#fff',
          borderWidth: 10
        },
        label: {
          show: false
        },
        color: ['#1DCCE0', '#C09BFF'],
        data: [
          { value: ze_visitors_cnt, name: '游客' },
          { value: ze_user_cnt, name: '用户' }
        ]
      }
    ]
  })
}

const initChart5 = val => {
  // 有效用户总数
  let effective_visit_cnt = []; let summary_date = []
  val.user_type_users.forEach(element => {
    effective_visit_cnt.push(element.effective_visit_cnt)
    summary_date.push(element.summary_date)
  })
  // 有效游客总数
  let visitors2 = []
  val.user_type_visitors.forEach(element => {
    visitors2.push(element.effective_visit_cnt)
  })
  let chart5 = echart5.init(myEcharts5.value, 'chartDom')
  window.addEventListener('resize', function () {
    chart5.resize()
  })
  chart5.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '0%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: summary_date
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    color: ['#1DCCE0', '#C09BFF'],
    series: [
      {
        name: '游客',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        barWidth: 20,
        data: visitors2
      },
      {
        name: '用户',
        type: 'bar',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        data: effective_visit_cnt
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        showDataShadow: false,
        bottom: 10,
        start: 0,
        end: 100
      }
    ]
  })
}

</script>
<style lang="scss" scoped>
.boxMain {
  background-color: #fff;
  .block{
    overflow: hidden;
    border-bottom: 1px solid #EFEFEF;
  }
  clear: both;
  .hedaer {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    .left {
      float: left;
      i{
        width: 4px;
        height: 14px;
        background: linear-gradient(180deg, #1AB8FF 0%, #0B82FD 100%);
        border-radius: 3px;
        margin-right: 10px;
        float: left;
        margin-top: 23px;
      }
      em{
        font-style: normal;
      }
      .tip {
        float: right;
        width: 18px;
        height: 18px;
        background: url("@/assets/images/tip.png") no-repeat;
        background-size: 18px auto;
        margin-top: 20px;
        margin-left: 5px;
      }
    }
  }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    .info{
      min-width: 314px;
      float: left;
      .all{
        width: 132px;
        height: 240px;
        background: url("@/assets/images/behavior_1.png") no-repeat;
        background-size: 132px auto;
        float:left;
        position: relative;
        .num{
          width: 132px;
          font-size: 30px;
          font-weight: 600;
          color: #FFFFFF; 
          position: absolute;
          left: 0;
          text-align: center;
          top: 148px;
        }
      }
      .effective{
        width: 132px;
        height: 240px;
        background: url("@/assets/images/behavior_2.png") no-repeat;
        background-size: 132px auto;
        float:left;
        margin-left: 10px;
        position: relative;
        .num{
          width: 132px;
          font-size: 30px;
          font-weight: 600;
          color: #FFFFFF; 
          position: absolute;
          left: 0;
          text-align: center;
          top: 148px;
        }
      }
    }
  }
  .echarts-box {
    position: relative;
    float: left;
    .echartsInfo {
      width: 180px;
      height: 90px;
      text-align: center;
      position: absolute;
      left: 60px;
      top: 110px;
      z-index: 999;
      overflow: hidden;
      span {
        font-weight: 600;
        font-size: 30px;
      }
      p {
        font-size: 14px;
      }
    }
  }
}
</style>