<template>
  <AbsoluteContainer>
    <page-main style="background-color: #fff">
      <div class="head-box">
        <div class="left">
          <el-date-picker
            v-model="time"
            style="float: left; width: 250px; border-right-width: 0"
            class="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :editable="false"
            :disabled-date="disabledDate"
            size="small"
            @change="handleDateChange"
          >
          </el-date-picker>
          <div class="buttonStyle">
            <el-button
              plain
              size="small"
              class="custom-button"
              style="margin-top: -2px"
              @click="setTime1"
            >
              近7天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTime2"
            >
              近10天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTime3"
            >
              近30天
            </el-button>
            <el-button
              plain
              size="small"
              style="margin-top: -2px"
              @click="setTime4"
            >
              当月
            </el-button>
          </div>
        </div>
      </div>
      <visits :data-info="dataInfo"></visits>
      <UserAddress :data-info="dataInfo"></UserAddress>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup name="accessData">
import visits from './modules/visits.vue'
import UserAddress from './modules/userAddress.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { requests } from '@/api/business/dataStatistics'
import { ref, onBeforeMount, onMounted, watch } from 'vue'

const dataInfo = ref({})

// 日期控制 默认七天
const time = ref([
  new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 700),
  new Date()
])

function setTime1 () {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
  time.value = [start, end]
  handleDateChange(time.value)
}
function setTime2 () {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 10)
  time.value = [start, end]
  handleDateChange(time.value)
}
function setTime3 () {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
  time.value = [start, end]
  handleDateChange(time.value)
}
function setTime4 () {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const firstDay = new Date(year, month, 1)
  const yesterday = new Date(year, month, currentDate.getDate())
  time.value = [firstDay, yesterday]
  handleDateChange(time.value)
}
// 日期change事件
function handleDateChange (value) {
  time.value[0] = value[0]
  time.value[1] = value[1]
  getInit()
}
// 之后日期不能选
function disabledDate (time) {
  return time.getTime() > Date.now()
}
// 日期转换字符串
function convert (dateStr) {
  const date = new Date(dateStr)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}${month}${day}`
}
onMounted(() => {
  getInit()
})
// 数据获取
function getInit () {
  requests.user_action_info({
    summary_date_start: convert(time.value[0]),
    summary_date_end: convert(time.value[1])
  }).then(res => {
    if (res.code == 0) {
      dataInfo.value = res.data
    } else {
      ElMessage.error(res.message)
    }
  })
}

</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  overflow: auto;

  .head-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    margin-top: 20px;
    .left {
      .el-button {
        margin-left: 30px;
      }
    }
    .right {
      & > .el-select {
        width: 110px;
        margin-right: 10px;
      }
    }
  }
  .box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .head-box .left {
    .el-button {
      margin-left: 0px;
      border: none;
      font-size: 12px;
      width: 50px;
    }
    .buttonStyle {
      border: 1px solid #dedede;
      float: left;
      margin-left: 5px;
      height: 24px;
      overflow: hidden;
      border-radius: 3px;
    }
    .time .el-date-editor {
      border-right-width: 0;
    }
  }
}
</style>
