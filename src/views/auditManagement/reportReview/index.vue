<template>
  <AbsoluteContainer>
    <page-main>
      <div class="tab">
        <div v-for="(item, index) in tabData" :key="item.value" :class="step == index + 1 ? 'child act' : 'child'"
          @click="handleClickTab(item, index)">
          {{ item.name }}
        </div>

        <div style="width: 100%">
          <div class="line" :style="'transform: translateX(' +
            (42 * (step - 1) + (step - 1) * 40) +
            'px)'
            "></div>
        </div>
      </div>
      <el-divider />
      <Adopt v-if="activeName === 1" />
      <Pass v-else-if="activeName === 2" />
      <Delete v-else-if="activeName === 3" />
    </page-main>
  </AbsoluteContainer>
</template>
<script setup name="regularAudit">
import Adopt from "./adopt/index.vue";
import Pass from "./pass/index.vue";
import Delete from "./delete/index.vue";
import { ref, onMounted } from "vue";
const store = useStore();
const activeName = ref(2);
const step = ref(1);
const tabData = ref([]);
onMounted(() => {
  tabData.value = [];
  let permissions = store.getters["menu/permissions"];
  if (permissions.filter((v) => v === "reportReview:adopt").length) {
    tabData.value.push({
      name: "待审核",
      value: 1,
    });
  }
  if (permissions.filter((v) => v === "reportReview:pass").length) {
    tabData.value.push({
      name: "无违规",
      value: 2,
    });
  }
  if (
    permissions.filter((v) => v === "reportReview:del")
      .length
  ) {
    tabData.value.push({
      name: "违规删除",
      value: 3,
    });
  }
  if (tabData.value.length) {
    activeName.value = tabData.value[0].value;
  }
});
// 切换标签
function handleClickTab(item, val) {
  activeName.value = item.value;
  step.value = val + 1;
}
</script>
<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;

  .tab {
    height: 32px;
    display: flex;
    font-size: 14px;
    line-height: 32px;
    flex-wrap: wrap;
    position: relative;
    z-index: 10;

    .child {
      cursor: pointer;
      margin-right: 40px;
      position: relative;
    }

    .child.act {
      color: #0b82fd;
      font-weight: 600;
    }

    .child.act::after {
      content: " ";
      display: block;
      width: 100%;
      height: 4px;
      background: #0b82fd;
      border-radius: 2px;
      bottom: 0;
      left: 0;
      z-index: 99;
    }
  }

  .el-divider--horizontal {
    margin-top: 2px;
  }
}
</style>
