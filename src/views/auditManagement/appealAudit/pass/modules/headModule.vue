<template>
  <div class="head-box">
    <div class="left"></div>
    <div class="right">
      <el-date-picker v-model="time" style="width: 220px; float: left;margin-right: 10px;" class="time"
        type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" :editable="false"
        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime">
      </el-date-picker>
      <!-- 全部频道 筛选-->
      <el-cascader v-model="cascadeValue" :options="options" :show-all-levels="false" clearable collapse-tags
        style="margin-right:10px" :props="optionsProps" placeholder="全部频道" @change="change" />
      <!-- 举报类型 -->
      <el-select v-model="searchData.reporting_type" clearable placeholder="请选择举报类型" style="width: 220px">
        <el-option v-for=" item  of  report_type " :key="item.id" :label="item.type_name" :value="item.id">
        </el-option>
      </el-select>
      <el-input v-model="searchData.search_word" placeholder="请输入关键词" style="margin-right: 10px" class="input-with-select"
        clearable @keyup.enter="handSearch">
        <template #prepend>
          <el-select v-model="searchData.search_type" placeholder="请选择" style="width: 100px" @change="typeChange">
            <el-option v-for="  item   of   commentSearchType  " :key="item.key" :label="item.label" :value="item.key">
            </el-option>
          </el-select>
        </template>
        <template #append>
          <el-button type="primary" :icon="Search" style="display: flex; align-items: center" @click="handSearch">
            搜索
          </el-button>
        </template>
      </el-input>

    </div>
  </div>
</template>
<script setup>
const route = useRoute(),
  router = useRouter();
import { requests } from "@/api/auditManagement/reportReview.js";
import { Search } from "@element-plus/icons";
import { defineEmits, defineExpose, onMounted, ref } from "vue";

// 组件传参声明方式
const props = defineProps({
  selectData: {
    type: Array,
  },
});
// 组件接收事件
const emit = defineEmits(["search", 'ready']);
// 圈子类型
const circleList = ref([]);
// 全部频道
const cascadeValue = ref(null);
const options = ref([]);
const optionsProps = ref({
  value: "category_id",
  label: "name",
});
// 稿件标题下拉的实时数据
const title_select_Data = ref([]);

// 搜索类型
const commentSearchType = ref([
  {
    key: "CONTENT",
    label: "评论内容",
  },
  {
    key: "ARTICLE_TITLE",
    label: "稿件标题",
  },
  {
    key: "DECLARANT_PERSON",
    label: "申诉人",
  },
]);
// 举报类型
const report_type = ref([

])
// 搜索参数
const searchData = ref({
  circle_id: "",
  category_id: "",
  search_type: "CONTENT",
  search_word: "",
  reporting_type: ''
});
const time = ref([])
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];
onMounted(() => {
  requests.listCategoryTree().then((res) => {
    let arr = res.data.category_list;
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1);
      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1);
          } else {
            for (let a = 0; a < arr[i].children[k].children.length; a++) {
              if (arr[i].children[k].children[a].permission == 0) {
                arr[i].children[k].children.splice(a--, 1);
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr;
    });
  });
  requests.getTypeList().then(res => {
    report_type.value = res.data.type_list
    emit('ready', res.data.type_list)
  })
  // 初始
  // handSearch();
});
const typeChange = () => {
  searchData.value.search_word = "";
};

const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(",");
    console.log(cascadeValue.value);
    data = data.split(",");
    data = Array.from(new Set(data));
    searchData.value.category_id = data[data.length - 1];
  } else {
    searchData.value.category_id = "";
  }
  handSearch();
};
// 点击搜索
const handSearch = () => {
  let searchObj = {};
  if (time.value) {
    searchObj.start_time = time.value[0];
    searchObj.end_time = time.value[1];
  } else {
    searchObj.start_time = '';
    searchObj.end_time = '';
  }
  searchObj.category_id = searchData.value.category_id;
  searchObj.search_type = searchData.value.search_type
  searchObj.search_word = searchData.value.search_word
  searchObj.reporting_type = searchData.value.reporting_type
  emit("search", searchObj);
};

defineExpose({
  searchData,
});
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .right {
    &>.el-select {
      width: 150px;
      margin-right: 10px;
    }

    .el-input-group {
      width: 400px;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
