<template>
  <headModel @ready="getStateList" @search="handleSearch" />
  <div id="tabel-component">
    <el-table v-loading="loading" :data="data.dataList" size="default" :height="data.height" row-key="id" lazy
      :load="load" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column v-slot="scope" label="评论内容" min-width="300">
        <span v-if="scope.row.place_on_file" class="reply">评论已归档</span>
        <span class="span-html" v-html="scope.row.comment_content"></span>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" width="60">
        <el-popover trigger="hover" placement="top" width="auto" v-if="scope.row.expression_url">
          <div style="padding: 0 10px;">
            <img style="height: 150px;  cursor: pointer;margin:0 auto;display:block" :src="scope.row.expression_url" />
          </div>

          <template #reference>
            <el-image style="width: 50px; height: 50px;cursor: pointer;" :src="scope.row.expression_url" fit="cover" />
          </template>
        </el-popover>
        <el-popover trigger="hover" placement="top" width="auto" v-if="scope.row.pic_url">
          <div style="padding: 0 10px;">
            <img style="height: 450px;  cursor: pointer;margin:0 auto;display:block" :src="scope.row.pic_url" />
          </div>

          <template #reference>
            <el-image style="width: 50px; height: 50px;cursor: pointer;" :src="scope.row.pic_url" fit="cover" />
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column v-slot="scope" align="center" width="60">
        <el-tooltip v-if="scope.row.comment_pic_risk_result" class="box-item" effect="light"
          :content="scope.row.comment_pic_risk_result" placement="top">
          <el-button type="danger" plain size="small" color="#F65520"
            style="margin-left: 5px; padding: 0 3px;height: 18px;">
            敏
          </el-button>
        </el-tooltip>
      </el-table-column>
      <el-table-column v-slot="scope" label="稿件标题" width="300" :show-overflow-tooltip="true">
        <a class="elemLink" target="_blank" v-if="scope.row.article_title" :href="scope.row.article_url">{{
          scope.row.article_title
        }}</a>
        <a class="elemLink" target="_blank" v-else :href="scope.row.article_url">无标题</a>
      </el-table-column>
      <el-table-column prop="appeal_content" label="申诉内容" width="180" />
      <el-table-column prop="comment_user_name" label="申诉人" width="180" />
      <el-table-column prop="informer_user_name" label="举报人" width="180" />
      <el-table-column prop="identified_reporting_type_string" label="举报类型" width="180" />
      <el-table-column v-slot="scope" label="审核时间" width="200">
        {{ handleTime(scope.row.audit_time) }}
      </el-table-column>
      <el-table-column prop="category_id_string" label="来源频道" width="120" />

      <el-table-column v-slot="scope" label="操作" width="80" fixed="right">
        <el-button style="margin-left: 0; padding: 5px 5px" text size="small" @click="handlePass(scope.row)"
          v-if="!scope.row.place_on_file">
          <span style="color: #0b82fd;font-size:14px">撤销并通过</span>
        </el-button>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="page">
      <el-pagination background small layout="total, sizes, prev, pager, next, jumper" :total="data.total"
        :page-size="data.size" :current-page="data.current" :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script setup name="cardList">
import { requests } from "@/api/auditManagement/reportReview";
import { nextTick, ref } from "vue";
import headModel from "./modules/headModule.vue";
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute(),
  router = useRouter();
const data = ref({
  height: null,
  dataList: [],
  total: 0,
  size: 100,
  current: 1,
  category_id: "",
  search_type: "CONTENT",
  search_word: "",
  reporting_type: '',
  start_time: "",
  end_time: '',
});
const loading = ref(false);
// 举报类型
const report_type = ref([

])
onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementById('tabel-component').offsetHeight
    console.log(height)
    let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 10
    data.value.height = height - bottom
  });
  init()
});
// 初始化
function init() {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    category_id: data.value.category_id,
    search_type: data.value.search_type,
    search_word: data.value.search_word,
    reporting_type: data.value.reporting_type,
    start_time: data.value.start_time,
    end_time: data.value.end_time,
    port_type: 6
  };
  loading.value = true;
  requests
    .getList(searchData)
    .then((res) => {
      loading.value = false;
      data.value.total = res.data.reports_list_page.total;
      if (!res.data.reports_list_page.records) {
        data.value.dataList = []
        return
      }
      res.data.reports_list_page.records.forEach(v => {
        if (v.comment_count > 1) {
          v.hasChildren = true
        } else {
          v.hasChildren = false
        }
        v.children = []
      })
      data.value.dataList = res.data.reports_list_page.records;
    })
    .catch(() => {
      loading.value = false;
    });
}
// 加载子节点
function load(row, treeNode, resolve) {
  console.log(row, treeNode,)

  let searchData = {
    category_id: data.value.category_id,
    search_type: data.value.search_type,
    search_word: data.value.search_word,
    reporting_type: data.value.reporting_type,
    start_time: data.value.start_time,
    end_time: data.value.end_time,
    port_type: 6,
    comment_id: row.comment_id
  };
  requests
    .getChildList(searchData)
    .then((res) => {
      resolve(res.data.reports_list)

    })
    .catch(() => {

    });
}
// 搜索
function handleSearch(val) {
  data.value.category_id = val.category_id;
  data.value.search_type = val.search_type;
  data.value.search_word = val.search_word;
  data.value.reporting_type = val.reporting_type;
  data.value.start_time = val.start_time;
  data.value.end_time = val.end_time;
  init();
}
// 获取举报类型列表
function getStateList(val) {
  report_type.value = val
}
// 通过
function handlePass(val) {
  let contentWithoutTags = val.comment_content.replace(/<[^>]+>/g, ''); // 去除HTML标签
  let content = contentWithoutTags.slice(0, 10); // 截取前10个字符
  ElMessageBox.confirm(
    '评论内容：' + content,
    '确定该评论未违反相关规则？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'Warning',
      dangerouslyUseHTMLString: true,
    }
  )
    .then(() => {
      loading.value = true
      // 通过接口
      requests
        .changeState({
          id: val.id,
          going_state: 5,
        })
        .then(res => {
          loading.value = false
          if (res.code == 0) {
            ElMessage({
              type: 'success',
              message: '申诉审核处理成功！'
            })
          }
          init()
        })
        .catch(error => {
          loading.value = false
        })
    })
    .catch(() => { })
}

// 选择每页几条
const handleSizeChange = (val) => {
  data.value.size = val;
  data.value.current = 1;
  init();
};
// 点击分页器
const handleCurrentChange = (val) => {
  data.value.current = val;
  init();
};
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s

}
</script>

<style lang="scss" scoped>
.elemLink {
  color: #606266;
  text-decoration: none;
}

a {
  text-decoration: none;
  color: #333;
}

a:hover,
a:visited,
a:link,
a:active {
  color: #333;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

#tabel-component {
  height: 100%;
}

.page-main {
  display: flex;
  flex-direction: column;

  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}

.reply {
  background: rgba(102, 177, 253, 0.23);
  border-radius: 2px;
  border: 1px solid #0b82fd;
  margin-right: 6px;
  font-size: 12px;
  color: #0b82fd;
  padding: 2px;
  font-family: "Arial";
}
</style>
