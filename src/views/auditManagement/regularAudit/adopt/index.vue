<template>
  <headModel
    :id="id"
    ref="searchBox"
    :disabled="data.idsPass.length + data.idsDel.length > 1"
    @search="search"
    @auditClick="auditClick"
    @adoptClick="adoptClick"
    @deleteClick="deleteClick"
  />
  <div id="tabel-component">
    <el-table v-loading="loading" :data="data.records" size="default" :height="data.height">
      <!-- <el-table-column type="selection"
                           width="40" /> -->
      <!-- <el-table-column prop="auto_pk"
            label="序号"
            width="150" /> -->
      <el-table-column
        v-tab="'regular_pending_batch_pass'"
        v-auth="'comment:batch_action_audit'"
        width="70"
      >
        <template #header>
          <el-checkbox
            v-model="passBut"
            class="checkBlue"
            label="通过"
            size="large"
            @change="handelPass"
          />
        </template>
        <template #default="scope">
          <div class="checkBlue">
            <el-checkbox
              v-model="scope.row.isPass"
              label=""
              size="large"
              fill="#0B82FD"
              @change="handelRowPass(scope.row.isPass, scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-tab="'regular_pending_batch_delete'"
        v-auth="'comment:batch_action_audit'"
        width="70"
      >
        <template #header>
          <el-checkbox
            v-model="delBut"
            class="checkRed"
            label="删除"
            size="large"
            @change="handelDel"
          />
        </template>
        <template #default="scope">
          <div class="checkRed">
            <el-checkbox
              v-model="scope.row.isDel"
              label=""
              size="large"
              fill="#FF0000"
              @change="handelRowDel(scope.row.isDel, scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-slot="scope"
        v-tab="'regular_pending_content'"
        label="评论内容"
        min-width="500"
      >
        <div
          v-if="scope.row.reply_info && scope.row.reply_info.reply_info_content"
          class="reply_info"
        >
          <span class="span-html" v-html="scope.row.reply_info.reply_info_content"></span>
          <el-tooltip
            v-if="scope.row.reply_info.sensitive_word"
            class="box-item"
            effect="light"
            :content="scope.row.reply_info.sensitive_word"
            placement="top"
          >
            <el-button
              type="danger"
              plain
              size="small"
              color="#F65520"
              style="margin-left: 5px; padding: 0 3px; height: 18px"
            >
              敏
            </el-button>
          </el-tooltip>
          <SpammerIcon v-if="scope.row.reply_info.spammer" />
          <!-- <WhiteUserIcon v-if="scope.row.white_list_user" /> -->
        </div>
        <p :class="scope.row.reply_info ? 'replyHide' : ''">
          <span v-if="scope.row.reply_info" class="reply">回复</span>
          <span class="span-html" v-html="scope.row.content"></span>
          <el-tooltip
            v-if="scope.row.sensitive_word"
            class="box-item"
            effect="light"
            :content="scope.row.sensitive_word"
            placement="top"
          >
            <el-button
              type="danger"
              plain
              size="small"
              color="#F65520"
              style="margin-left: 5px; padding: 0 3px; height: 20px"
            >
              敏
            </el-button>
          </el-tooltip>
          <SpammerIcon v-if="scope.row.spammer" />
          <WhiteUserIcon v-if="scope.row.white_list_user" />
        </p>
      </el-table-column>
      <el-table-column v-slot="scope" v-tab="'regular_pending_content'" align="center" width="60">
        <el-popover v-if="scope.row.expression_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 150px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.expression_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.expression_url"
              fit="cover"
            />
          </template>
        </el-popover>
        <el-popover v-if="scope.row.pic_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 450px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.pic_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.pic_url"
              fit="cover"
            />
          </template>
        </el-popover>
      </el-table-column>

      <el-table-column v-slot="scope" v-tab="'regular_pending_content'" width="40">
        <el-tooltip
          v-if="scope.row.comment_pic_risk_result.resultCode == 1"
          class="box-item"
          effect="light"
          :content="scope.row.comment_pic_risk_result.resultMsg"
          placement="top"
        >
          <el-button
            type="danger"
            plain
            size="small"
            color="#F65520"
            style="margin-left: 5px; padding: 0 3px; height: 18px"
          >
            敏
          </el-button>
        </el-tooltip>
      </el-table-column>
      <el-table-column v-tab="'regular_pending_colligation_score'" width="60">
        <template #header>
          <el-tooltip placement="top" popper-class="custom-tooltip">
            <span>评分<i class="quest"></i></span>
            <template #content>
              <div class="tooltip-content"></div>
            </template>
          </el-tooltip>
        </template>
        <template #default="scope">
          <el-tooltip :content="scope.row.colligation_score" placement="top">
            <div
              :class="{
                grade0: scope.row.colligation_score == 0,
                grade1: scope.row.colligation_score == 1,
                grade2: scope.row.colligation_score == 2,
                grade3: scope.row.colligation_score == 3,
                grade4: scope.row.colligation_score == 4,
                grade5: scope.row.colligation_score == 5,
                grade6: scope.row.colligation_score == 6,
                gradef1: scope.row.colligation_score == -1,
                gradef2: scope.row.colligation_score == -2,
                gradef3: scope.row.colligation_score == -3,
                gradef4: scope.row.colligation_score == -4
              }"
            ></div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column
        v-slot="scope"
        v-tab="'regular_pending_title'"
        label="稿件标题"
        :show-overflow-tooltip="true"
        width="500"
      >
        <span
          v-if="scope.row.article_title"
          class="elem"
          @click="openLink(scope.row.article_url)"
          >{{ scope.row.article_title }}</span
        >
        <span v-else class="elem" @click="openLink(scope.row.article_url)">无标题</span>
      </el-table-column>
      <el-table-column
        v-slot="scope"
        v-tab="'regular_pending_comment_user_name'"
        width="150"
        label="评论人"
      >
        <SpammerMarker
          v-model:spammer="scope.row.spammer"
          :user-data="{
            user_name: scope.row.comment_account,
            user_id: scope.row.comment_account_id
          }"
        >
          <template #reference>
            <span>{{ scope.row.comment_account }}</span>
          </template>
        </SpammerMarker>
      </el-table-column>
      <el-table-column
        v-tab="'regular_pending_source'"
        prop="source"
        label="来源频道"
        width="120"
      />
      <el-table-column
        v-tab="'regular_pending_created_at'"
        prop="created_at"
        label="评论时间"
        width="200"
      />
      <el-table-column
        v-slot="scope"
        v-tab="'regular_pending_operate'"
        label="操作"
        width="200"
        fixed="right"
      >
        <el-button
          v-auth="'comment:batch_action_audit'"
          text
          style="padding-left: 0"
          @click="pendingClick(scope.row)"
        >
          <span class="button_text">待处理</span>
        </el-button>
        <el-button
          v-auth="'comment:batch_action_audit'"
          text
          style="margin-left: 0"
          @click="adoptClick(scope.row)"
        >
          <span class="button_text">通过</span>
        </el-button>
        <el-button
          v-auth="'comment:batch_action_audit'"
          text
          style="margin-left: 0"
          @click="deleteClick(scope.row.id)"
        >
          <span class="button_text">删除</span>
        </el-button>
      </el-table-column>
    </el-table>
  </div>

  <!-- 分页 -->
  <div class="page">
    <el-pagination
      background
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
    </el-pagination>
  </div>
  <el-dialog v-model="dialogVisible" title="确定批量处理以下评论?" width="30%">
    <div class="tip">
      通过<i>{{ data.idsPass.length }}</i
      >条，删除<b>{{ data.idsDel.length }}</b
      >条
    </div>
    <br />
    <span>通过后，可在“已通过”列表内删除，删除后，可在“已删除”页面撤销</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleDialogCancel">取消</el-button>
        <el-button type="primary" @click="handleDialogPass"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisiblBatch" title="操作提示" width="30%">
    <div class="tip">
      当前批量处理评论：通过<b>{{ bachVal.passSuccessCount }}</b
      >条，删除<i>{{ bachVal.deleteTotalCount }}</i
      >条。操作失败<b>{{ bachVal.passFailCount }}</b
      >条。原因【选中的评论上级尚未获得通过状态】。
    </div>
    <br />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="dialogVisiblBatch = false"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { requests } from '@/api/business/auditManagement'
import headModel from './modules/headModule.vue'
import { ElMessage, ElMessageBox, stepProps } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch } from 'vue'
import SpammerIcon from '@/components/SpammerIcon/index.vue'
import WhiteUserIcon from '@/components/WhiteUserIcon/index.vue'

import SpammerMarker from '@/components/SpammerMarker/index.vue'
const route = useRoute(),
  router = useRouter()
const loading = ref(false)
const passBut = ref(false)
const delBut = ref(false)
const dialogVisible = ref(false)
const dialogVisiblBatch = ref(false)
const bachVal = ref()
// 搜索栏
const searchBox = ref(null)
const id = ref(route.query.id)
const data = ref({
  height: null,
  idsPass: [], // 多选的通过数据
  idsDel: [], // 多选的删除数据
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 30
  data.value.height = height - bottom
})
// 待处理
const pendingClick = val => {
  ElMessageBox.confirm('评论内容：' + val.content, '确定将该评论置为待处理？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    dangerouslyUseHTMLString: true
  }).then(() => {
    loading.value = true
    // 通过接口
    requests
      .batchAction({
        comment_ids: val.id,
        status: 'PROCESSING',
        current_status: 'PENDING'
      })
      .then(res => {
        loading.value = false
        if (res.code == 0) {
          search(searchBox.value.searchData)
          ElMessage({
            type: 'success',
            message: '置为待处理成功！'
          })
        }
      })
      .catch(error => {
        console.log(error)
        loading.value = false
      })
  })
}
// 通过全选
function handelPass() {
  if (passBut.value) {
    delBut.value = false
    for (let k = 0; k < data.value.records.length; k++) {
      data.value.records[k].isPass = true
      data.value.records[k].isDel = false
    }
  } else {
    for (let k = 0; k < data.value.records.length; k++) {
      data.value.records[k].isPass = false
    }
  }
  selectNum()
}
// 高级标题跳转
function openLink(url) {
  window.open(url)
}
// 删除全选
function handelDel() {
  if (delBut.value) {
    passBut.value = false
    for (let k = 0; k < data.value.records.length; k++) {
      data.value.records[k].isDel = true
      data.value.records[k].isPass = false
    }
  } else {
    for (let k = 0; k < data.value.records.length; k++) {
      data.value.records[k].isDel = false
    }
  }
  selectNum()
}
// 查通过删除数量
function handelRowPass(flag, row) {
  if (flag) {
    row.isDel = false
  }
  selectNum()
}
function handelRowDel(flag, row) {
  if (flag) {
    row.isPass = false
  }
  selectNum()
}
// 搜索接口
const search = val => {
  let obj
  // 如果是稿件标题
  if (val.comment_search_type == 'ARTICLE_TITLE') {
    obj = JSON.parse(JSON.stringify(val))
    obj.comment_search_type = 'ARTICLE_ID'
    if (searchBox.value.title_search_data) {
      obj.searchword = searchBox.value.title_search_data.slice(
        searchBox.value.title_search_data.indexOf('【') + 1,
        searchBox.value.title_search_data.indexOf('】')
      )
    }
  } else {
    obj = val
  }
  loading.value = true
  requests
    .list(obj)
    .then(res => {
      data.value.idsPass = []
      data.value.idsDel = []
      loading.value = false
      res.data.pg.records.forEach(v => {
        // 循环列表数据，添加勾选参数
        v.isPass = false
        v.isDel = false
        // 评论敏感词
        v.sensitives = ''
        // 回复评论敏感词
        v.sensitives2 = ''
        // 敏感词分类 自定义敏感词 百度敏感词
        // if (v.sensitive_word.length > 0) {
        //   v = custom_detect_result(v)
        // } else {
        //   v = baidu_detect_result(v)
        // }
        // 回复 敏感词 优先提取自定义敏感词
        // if (v.reply_info) {
        //   v.reply_info.new_reply_info_content = v.reply_info.reply_info_content
        //   if (v.reply_info.sensitive_word.length > 0) {
        //     v = custom_reply_info(v)
        //   } else {
        //     v = baidu_reply_info(v)
        //   }
        // }
        // 图片敏感词
        if (v.comment_pic_risk_result) {
          return (v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result))
        } else {
          return (v.comment_pic_risk_result = {})
        }
      })
      data.value.records = res.data.pg.records
      data.value.total = res.data.pg.total
      data.value.current = obj.current
    })
    .catch(error => {
      loading.value = false
      console.log(error)
    })
}


// 查询取选中数据
const selectNum = val => {
  let alldata = [] // 选择全部数据
  let idsPassValue = [] // 通过数据
  let idsdelValue = [] // 删除数据
  for (let k = 0; k < data.value.records.length; k++) {
    if (data.value.records[k].isPass || data.value.records[k].isDel) {
      alldata.push(data.value.records[k])
      if (data.value.records[k].isPass) {
        idsPassValue.push(data.value.records[k])
      } else if (data.value.records[k].isDel) {
        idsdelValue.push(data.value.records[k])
      }
    }
  }
  data.value.idsPass = idsPassValue
  data.value.idsDel = idsdelValue
  searchBox.value.batchNumber.pass = idsPassValue.length
  searchBox.value.batchNumber.del = idsdelValue.length
}
// 批量审核
const auditClick = val => {
  dialogVisible.value = true
}
// 批量审核确认后的处理
const handleDialogPass = () => {
  dialogVisible.value = false
  // 拼接接口传参数据
  let idsPassValue = ''
  let strPass = ''
  for (let i = 0; i < data.value.idsPass.length; i++) {
    strPass += data.value.idsPass[i].id + ','
  }
  idsPassValue = `${strPass.slice(0, strPass.length - 1)}`
  // 拼接接口传参数据
  let idsDelValue = ''
  let strDel = ''
  for (let k = 0; k < data.value.idsDel.length; k++) {
    strDel += data.value.idsDel[k].id + ','
  }
  idsDelValue = `${strDel.slice(0, strDel.length - 1)}`

  loading.value = true

  requests
    .batchActionAudit({
      comment_ids_pass: idsPassValue,
      comment_ids_Delete: idsDelValue
    })
    .then(res => {
      loading.value = false
      passBut.value = false
      delBut.value = false
      if (res.code == 0 && res.data.result.passFailCount == 0) {
        ElMessage({
          type: 'success',
          message: '批量审核成功！'
        })
        searchBox.value.batchNumber.pass = 0
        searchBox.value.batchNumber.del = 0
      } else {
        ElMessage({
          type: 'error',
          message: '批量审核失败！'
        })
        dialogVisiblBatch.value = true
        bachVal.value = res.data.result
      }
      //   刷新接口
      search(searchBox.value.searchData)
    })
    .catch(error => {
      ElMessage({
        type: 'error',
        message: '请求失败！'
      })
      console.log(error)
    })
}
// 取消处理
const handleDialogCancel = val => {
  dialogVisible.value = false
  ElMessage({
    type: 'info',
    message: '已取消！'
  })
}
// 单条通过
const adoptClick = val => {
  ElMessageBox.confirm('评论内容：' + val.content, '确定通过该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    dangerouslyUseHTMLString: true
  }).then(() => {
    loading.value = true
    // 通过接口
    requests
      .batchAction({
        comment_ids: val.id,
        status: 'PASS',
        current_status: 'PENDING'
      })
      .then(res => {
        loading.value = false
        if (res.code == 0) {
          search(searchBox.value.searchData)
          ElMessage({
            type: 'success',
            message: '通过成功！'
          })
        }
      })
      .catch(error => {
        console.log(error)
        loading.value = false
      })
  })
}

// 单条删除
const deleteClick = val => {
  ElMessageBox.confirm('删除后，可在“已删除”页面撤销', '确定删除该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Delete)
  })
    .then(() => {
      loading.value = true

      // 删除接口
      requests
        .batchAction({
          comment_ids: val,
          status: 'DELETE',
          current_status: 'PENDING'
        })
        .then(res => {
          loading.value = false
          if (res.code == 0) {
            ElMessage({
              type: 'success',
              message: '删除成功！'
            })
            //   删除成功之后不刷新页面 把删除的在列表删除
            search(searchBox.value.searchData)
          } else {
            ElMessage({
              type: 'error',
              message: '删除失败！'
            })
          }

          //   刷新接口
          //   search(searchBox.value.searchData)
        })
        .catch(error => {
          ElMessage({
            type: 'error',
            message: '删除失败！'
          })
        })
    })
    .catch(() => {})
}

// 选择每页几条
const handleSizeChange = val => {
  passBut.value = false
  delBut.value = false
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}
// 点击分页器
const handleCurrentChange = val => {
  passBut.value = false
  delBut.value = false
  data.value.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}
</script>
<style>
.custom-tooltip {
  background-color: white !important;
  border: none !important;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
}

.custom-tooltip .el-popper__arrow:before {
  display: none;
}
.replyHide {
  margin: 10px 0;
}
</style>
<style lang="scss" scoped>
a {
  color: #0b82fd;
}
.page-main {
  border-radius: 12px;
  display: flex;
  flex-direction: column;

  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  .reply_info {
    font-size: 14px;
    line-height: 22px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 3px 5px;
    color: #666;
  }

  .replyHide {
    padding: 0 10px;
  }

  .reply {
    background: rgba(102, 177, 253, 0.23);
    border-radius: 2px;
    border: 1px solid #0b82fd;
    margin-right: 6px;
    font-size: 12px;
    color: #0b82fd;
    padding: 2px;
    font-family: 'Arial';
  }

  .elemLink {
    color: #606266;
    text-decoration: none;
  }

  .elem:hover,
  .reviewed:hover {
    color: rgba(11, 130, 253, 0.7);
  }

  .elem {
    cursor: pointer;
  }

  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}

.checkBlue,
.checkRed {
  width: 30px;
  display: flex;
  font-weight: 600;
  color: #333;

  .el-checkbox.el-checkbox--large {
    height: 30px;
  }
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.tip {
  i {
    color: #409eff;
    font-weight: bold;
    font-style: normal;
    margin: 0 10px;
  }

  b {
    color: red;
    font-style: normal;
    font-weight: bold;
    margin: 0 10px;
  }
}

.grade0 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/0m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}

.grade1 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/1m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}

.grade2 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/2m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}

.grade3 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/3m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}

.grade4 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/4m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.grade5 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/5m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.grade6 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/6m.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.gradef1 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/m1.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.gradef2 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/m2.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.gradef3 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/m3.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.gradef4 {
  width: 20px;
  height: 21px;
  background: url('@/assets/images/m4.png') no-repeat;
  background-size: 20px auto;
  margin-left: 10px;
}
.quest {
  width: 18px;
  height: 18px;
  background: url('@/assets/images/quest.png') no-repeat;
  background-size: 18px auto;
  display: inline-block;
  vertical-align: text-top;
}

.tooltip-content {
  width: 230px;
  height: 150px;
  background: url('@/assets/images/tooltip.png') no-repeat;
  background-size: 230px auto;
  background-color: white !important;
}
</style>
