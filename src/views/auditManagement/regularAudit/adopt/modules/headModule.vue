<template>
  <div class="head-box">
    <div class="left">
      <!-- <el-button
        v-auth="'comment:batch_action'" type="primary" :disabled="disabled==0"
        @click="emit('adoptClick')"
      >
        批量通过
      </el-button>
      <el-button
        v-auth="'comment:batch_action'" type="danger" :disabled="disabled==0"
        @click="emit('deleteClick')"
      >
        批量删除
      </el-button> -->
      <el-button
        v-auth="'comment:batch_action_audit'" color="#0B82FD" type="primary"
        :disabled="batchNumber.pass + batchNumber.del < 2" @click="emit('auditClick')"
      >
        批量审核
      </el-button>
      <span class="text">
        通过<span style="color: #0b82fd">{{ batchNumber.pass }}</span>条，删除<span style="color: #ff0000">{{ batchNumber.del
        }}</span>条
      </span>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->
      <!-- <el-select v-model="searchData.comment_source" filterable clearable @change="change">
                <el-option v-for="item in comment_source_type" :key="item.key" :label="item.label" :value="item.key" />
            </el-select> -->
      <el-date-picker
        v-model="time" style="width: 220px; margin-right: 10px" class="time" type="datetimerange"
        range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" :editable="false" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime"
      >
      </el-date-picker>
      <!-- 圈子 -->
      <el-cascader
        v-model="circleValue" :options="circleList" :show-all-levels="false" clearable collapse-tags
        :props="optionsProps2" placeholder="请选择圈子" style="margin-right: 10px; width: 130px" @change="changeCircle"
      />
      <!-- 频道 -->
      <el-cascader
        v-model="cascadeValue" :options="options" :show-all-levels="false" clearable collapse-tags
        :props="optionsProps" placeholder="全部频道" style="margin-right: 10px; width: 130px" :disabled="channelDisabled"
        @change="change"
      />
      <!-- 分组 -->
      <div v-if="groupList.length > 0" v-auth="'comment:group_feature'">
        <el-cascader
          v-model="groupValue" :options="groupList" :show-all-levels="false" clearable collapse-tags
          :props="groupListProps" placeholder="请选择分组" style="margin-right: 10px; width: 130px" :disabled="groupDisabled"
          @change="changeGroup"
        />
      </div>

      <!-- 评论类型 -->
      <el-select v-model="searchData.comment_type" style="width: 110px">
        <el-option v-for="item of commentType" :key="item.key" :label="item.label" :value="item.key">
        </el-option>
      </el-select>
      <el-divider direction="vertical" />
      <!-- 搜索条件 -->
      <el-select v-model="searchData.comment_search_type" style="margin-right: 5px; width: 110px" @change="typeChange">
        <el-option v-for="item of commentSearchType" :key="item.key" :label="item.label" :value="item.key">
        </el-option>
      </el-select>
      <!-- 除了 稿件标题-->
      <el-input
        v-if="searchData.comment_search_type != 'ARTICLE_TITLE'" v-model="searchData.searchword"
        style="width: 200px" placeholder="请输入关键词" class="input-with-select" clearable @keyup.enter="handSearch"
      >
      </el-input>
      <!-- 稿件标题搜索的实时下拉 -->
      <el-select
        v-if="searchData.comment_search_type == 'ARTICLE_TITLE'" v-model="title_search_data"
        style="width: 200px; margin-right: 0" filterable remote :remote-method="remoteMethod" :loading="loading" clearable
        placeholder="请输入稿件关键字" popper-class="selectElem" :teleported="false" @clear="clear" @change="titleChange"
      >
        <el-option v-for="item in title_select_Data" :key="item.key" :label="item.label" :value="item.label" />
      </el-select>
      <!-- 搜索按钮 -->
      <el-button color="#0B82FD" type="primary" :icon="Search" style="margin-left: 5px" @click="handSearch">
        搜索
      </el-button>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose, computed } from 'vue'
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
const time = ref('')
// 组件接收事件
const emit = defineEmits(['search', 'adoptClick', 'deleteClick'])
// 组件传参声明方式
const props = defineProps({
  disabled: {
    type: Number
  },
  id: {
    type: String
  }
})
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})
const channelDisabled = computed(() => {
  if (groupValue.value && Array.isArray(groupValue.value) && groupValue.value.length > 0) {
    return true
  } else {
    return false
  }
})
// 全部分组
const groupList = ref([])
const groupValue = ref(null)
const groupListProps = ref({
  value: 'id',
  label: 'group_name',
  multiple: false
})
const groupDisabled = computed(() => {
  if (cascadeValue.value && Array.isArray(cascadeValue.value) && cascadeValue.value.length > 0) {
    return true
  } else {
    return false
  }
})
// 圈子
const circleValue = ref(null)
const optionsProps2 = ref({
  value: 'id',
  label: 'name',
  multiple: true
})
// 稿件标题下拉的实时数据
const title_select_Data = ref([])
// 稿件标题时的关键字
const title_search_data = ref(null)
// 评论类型
const commentType = ref([
  {
    key: 'ALL',
    label: '全部评论'
  },
  {
    key: 'ARTICLE_TYPE',
    label: '稿件评论'
  },
  {
    key: 'REPLY_TYPE',
    label: '回复评论'
  }
])
// 圈子类型
const circleList = ref([])

// 搜索类型
const commentSearchType = ref([
  {
    key: 'CONTENT',
    label: '评论内容'
  },
  // {
  //     key: 'COMMENT_SOURCE',
  //     label: '评论来源'
  // },
  {
    key: 'ARTICLE_TITLE',
    label: '稿件标题'
  },
  {
    key: 'COMMENT_PERSON',
    label: '评论人'
  },
  {
    key: 'ARTICLE_ID',
    label: '稿件id'
  }
])
const batchNumber = ref({
  pass: 0,
  del: 0
})
const loading = ref(false)
// 搜索参数
const searchData = ref({
  state: 'PENDING', // 状态(PENDING-待审核，PASS-审核通过，UNPASS-已删除)
  time_type: 'COMMENT_DATE', // 评论日期类型（COMMENT_DATE：评论日期 AUDIT_DATE：审核日期.）
  start_time: '', // 起始时间
  end_time: '', // 结束时间
  category_id: '',
  comment_source: '', // 频道名称
  comment_type: 'ALL', // 筛选评论类型（ARTICLE_TYPE：稿件评论 REPLY_TYPE：回复评论）
  circle_id: '',
  group_id: '',
  comment_search_type: 'CONTENT', // 搜索类型（ALL：全部  CONTENT：评论内容 COMMENT_SOURCE：评论来源 ARTICLE_TITLE：稿件标题 COMMENT_PERSON：评论人 AUDIT_PERSON:审核人  ARTICLE_ID 稿件id）
  searchword: '', // 关键词
  size: 100, // 每页条数
  current: 1 // 分页页码 默认1
})
onMounted(() => {
  requests.listCategoryTree().then(res => {
    let arr = res.data.category_list
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1)
      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1)
          } else {
            for (let a = 0; a < arr[i].children[k].children.length; a++) {
              if (arr[i].children[k].children[a].permission == 0) {
                arr[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr
    })
  })
  if (props.id) {
    searchData.value.comment_search_type = 'ARTICLE_ID'
    searchData.value.searchword = props.id
  }
  requests.getcircleList({}).then(res => {
    circleList.value = res.data.circle_list
  })
  requests.getGroupListApi().then(res => {
    groupList.value = res.data.category_group_list
  })
  emit('search', searchData.value)
})
const typeChange = () => {
  title_search_data.value = ''
  //   searchData.value.searchword = ''
}
const remoteMethod = query => {
  console.log('query', query)
  title_select_Data.value = []
  if (query) {
    loading.value = true
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.size = 10
    newSearchData.current = 1
    newSearchData.searchword = query
    // 请求
    requests.searchList(newSearchData).then(res => {
      loading.value = false

      for (let i = 0; i < res.data.list.length; i++) {
        title_select_Data.value.push({
          key: res.data.list[i].article_id,
          label: `【${res.data.list[i].article_id}】${res.data.list[i].article_title}`
        })
      }
    })
  } else {
    title_select_Data.value = []
  }
}
const clear = () => {
  console.log('清空')
}

// 点击搜索 每次当前页码变为1
function handSearch () {
  // 判断 如果是 稿件标题 就用id形式搜索
  if (time.value) {
    console.log(time.value)
    searchData.value.start_time = time.value[0]
    searchData.value.end_time = time.value[1]
  } else {
    searchData.value.start_time = ''
    searchData.value.end_time = ''
  }
  searchData.value.current = 1
  if (searchData.value.comment_search_type == 'ARTICLE_TITLE') {
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.comment_search_type = 'ARTICLE_ID'
    if (!title_search_data.value) {
      title_search_data.value = ''
    } else {
      newSearchData.searchword = title_search_data.value.slice(
        title_search_data.value.indexOf('【') + 1,
        title_search_data.value.indexOf('】')
      )
    }
    console.log('title_search_data.value', title_search_data.value)
    emit('search', newSearchData)
  } else {
    emit('search', searchData.value)
  }
}
// 全部频道选中后 直接调用搜索
const change = () => {
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.category_id = data.join(',')
  } else {
    searchData.value.category_id = ''
  }

  handSearch()
}
// 圈子选中调用
function changeCircle () {
  if (circleValue.value) {
    searchData.value.circle_id = circleValue.value.join(',')
  } else {
    searchData.value.circle_id = ''
  }
  handSearch()
}
function changeGroup () {
  if (groupValue.value) {
    searchData.value.group_id = groupValue.value.join(',')
  } else {
    searchData.value.group_id = ''
  }
  handSearch()
}
// 稿件远程 选中
const titleChange = val => {
  let newSearchData = JSON.parse(JSON.stringify(searchData.value))
  if (val) {
    newSearchData.comment_search_type = 'ARTICLE_ID'
    newSearchData.searchword = val.slice(val.indexOf('【') + 1, val.indexOf('】'))
  }
  emit('search', newSearchData)
}
defineExpose({
  searchData,
  title_search_data,
  batchNumber
})
</script>
<style lang="scss" scoped>
.text {
  font-size: 14px;
  margin-left: 10px;
  color: #000;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    margin-right: 10px;
  }

  .right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 6px;
  }
}

::v-deep .selectElem {
  width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
  background-color: #ebebeb;
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
