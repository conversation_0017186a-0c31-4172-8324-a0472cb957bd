<script setup>
import { ref } from 'vue'
import { requests } from '@/api/business/group'

const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  },
  name: {
    type: String,
    default: ''
  }
})

const text = ref(props.name)

const emits = defineEmits(['cancel', 'confirm'])

const onCancelNameEditClick = () => {
  emits('cancel')
}

const onConfirmNameEditClick = () => {
  if (props.id) {
    requests
      .editGroup({ id: props.id, group_name: text.value })
      .then(res => {
        emits('confirm', { id: props.id, name: text.value })
      })
      .catch(err => {
        console.log(err)
      })
  } else {
    requests
      .createGroup({ group_name: text.value })
      .then(res => {
        const id = res.data.category_group.id
        emits('confirm', { id, name: text.value })
      })
      .catch(err => {
        console.log(err)
      })
  }
}
</script>

<template>
  <div class="container-group-name">
    <div class="content">
      <div>分组名称</div>
      <div class="input-group-name">
        <el-input
          v-model="text"
          placeholder="请输入分组名称"
          maxlength="8"
          show-word-limit
          type="text"
        ></el-input>
      </div>
    </div>
    <div class="bottom">
      <el-button @click="onCancelNameEditClick">取消</el-button>
      <el-button type="primary" @click="onConfirmNameEditClick">确定</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.container-group-name {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .content {
    width: 50%;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .input-group-name {
      margin-left: 20px;
      flex: 1;
    }
  }
  .bottom {
    margin-top: 70px;
    margin-bottom: 30px;
  }
}
</style>
