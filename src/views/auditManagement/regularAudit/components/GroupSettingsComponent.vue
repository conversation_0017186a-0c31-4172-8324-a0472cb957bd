<script setup>
import { computed, onMounted, ref } from 'vue'
import GroupNameEditComponent from '@/views/auditManagement/regularAudit/components/GroupNameEditComponent.vue'
import { requests } from '@/api/business/group'
import { ElMessage } from 'element-plus'
import Plus from '@/assets/images/plus.png'
const groupNameEditDialogVisible = ref(false)
const groupNameDeleteDialogVisible = ref(false)
const groupList = ref([])
const currentEditGroup = ref(null)
const currentDeleteGroup = ref(null)

// 获取分组列表
const requestGroupList = async () => {
  requests
    .getGroupListApi()
    .then(res => {
      groupList.value = res.data.category_group_list
      // 默认选中第一个分组
      if (groupList.value.length > 0) {
        onGroupItemClick(groupList.value[0])
      }
    })
    .catch(err => {
      console.log(err)
    })
}

const traverseTree = (nodes, callback) => {
  nodes.forEach(node => {
    callback(node)
    if (node.children && node.children.length > 0) {
      traverseTree(node.children, callback)
    }
  })
}

const channelLoading = ref(false)
const requestChannelByUser = async () => {
  channelLoading.value = true
  requests
    .getChannelByUser()
    .then(res => {
      console.log('getChannelByUser', res)
      // 遍历所有节点及其子节点，将disabled属性设置为true
      traverseTree(res.data.user_category_list, node => {
        node.disabled = true
      })
      channelData.value = res.data.user_category_list
    })
    .catch(err => {
      console.log(err)
    })
    .finally(() => {
      channelLoading.value = false
    })
}

const requestChannelByGroup = async item => {
  channelLoading.value = true
  requests
    .getChannelByGroup({ category_group_id: item.id })
    .then(res => {
      channelData.value = res.data.user_category_list
    })
    .catch(err => {
      console.log(err)
    })
    .finally(() => {
      channelLoading.value = false
      requestAnimationFrame(() => {
        rightScroll.value.scrollTop = 0
      })
    })
}
onMounted(() => {
  // 获取分组列表
  requestGroupList()
  // 获取所有频道列表
  requestChannelByUser()
})
const onAddGroupClick = () => {
  groupNameEditDialogVisible.value = true
}

const onEditClick = item => {
  currentEditGroup.value = item
  groupNameEditDialogVisible.value = true
}

const onDeleteClick = item => {
  console.log('onDeleteClick', item)
  currentDeleteGroup.value = item
  groupNameDeleteDialogVisible.value = true
}

// 选中分组点击事件
const selectedGroup = ref(null)
const rightScroll = ref(null)
const onGroupItemClick = item => {
  console.log('onGroupItemClick', item)
  selectedGroup.value = item
  if (item === null) {
    requestChannelByUser()
  } else {
    requestChannelByGroup(item)
  }
}

/**
 * 回调事件
 */
const onGroupNameEditConfirm = data => {
  console.log('onGroupNameEditConfirm', data)
  requests
    .getGroupListApi()
    .then(res => {
      groupList.value = res.data.category_group_list
      groupNameEditDialogVisible.value = false
      currentEditGroup.value = null
      if (data.id) {
        const item = groupList.value.find(item => item.id === data.id)
        onGroupItemClick(item)
      }
      ElMessage({
        type: 'success',
        message: '保存成功，刷新页面后生效！'
      })
    })
    .catch(err => {
      console.log(err)
    })
}

const onGroupNameEditCancel = () => {
  groupNameEditDialogVisible.value = false
  currentEditGroup.value = null
}

const onDeleteGroupNameConfirmClick = () => {
  // 删除分组后，选中之前或者之后的分组
  const index = groupList.value.findIndex(item => item.id === currentDeleteGroup.value.id)
  let nextHighLightGroup = null
  if (groupList.value.length === 1) {
    nextHighLightGroup = null
  } else if (index === 0) {
    nextHighLightGroup = groupList.value[1]
  } else {
    nextHighLightGroup = groupList.value[index - 1]
  }
  // 删除分组
  requests
    .deleteGroup({ id: currentDeleteGroup.value.id })
    .then(async () => {
      requests
        .getGroupListApi()
        .then(res => {
          groupList.value = res.data.category_group_list
          if (nextHighLightGroup === null) {
            onGroupItemClick(null)
          } else {
            const item = groupList.value.find(item => item.id === nextHighLightGroup.id)
            if (index !== -1) {
              onGroupItemClick(item)
            }
          }
          ElMessage({
            type: 'success',
            message: '删除成功，刷新页面后生效！'
          })
        })
        .catch(err => {
          console.log(err)
        })
    })
    .catch(err => {
      console.log(err)
    })
    .finally(() => {
      groupNameDeleteDialogVisible.value = false
      currentDeleteGroup.value = null
    })
}

const channelData = ref([])
const tree = ref(null)
const handleCheckChange = (data, checked) => {
  console.log('checked', checked)
}
const defaultProps = {
  children: 'children',
  label: 'name',
  checked: 'permission',
  disabled: 'disabled'
}

const defaultCheckedKeys = computed(() => {
  const checkedKeys = []

  // 递归函数，用于遍历树形结构
  const traverse = node => {
    if (node.permission === 1) {
      checkedKeys.push(node.id)
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        traverse(child)
      })
    }
  }

  channelData.value.forEach(node => {
    traverse(node)
  })

  return checkedKeys
})

const onSaveChannelsWithGroup = () => {
  const checkedNodes = tree.value.getCheckedNodes()
  const checkedItemIds = checkedNodes.map(item => item.category_id)
  console.log('Checked Item IDs:', checkedItemIds)
  requests
    .updateGroupCategory({
      id: selectedGroup.value.id,
      categorys: checkedItemIds.join(',')
    })
    .then(() => {
      ElMessage({
        type: 'success',
        message: '保存成功，刷新页面后生效！'
      })
    })
    .catch(err => {
      console.log(err)
    })
}
</script>

<template>
  <div class="container">
    <div class="left">
      <div class="group-button-add" @click="onAddGroupClick">
        <img class="plus-icon" :src="Plus" />
        <span>添加分组</span>
      </div>
      <div
        class="group-button"
        v-for="(item, index) in groupList"
        :key="index"
        :class="{ 'group-button-selected': selectedGroup === item }"
        @click="onGroupItemClick(item)"
      >
        <span>{{ item.group_name }}</span>
        <div class="group-button-icon">
          <Edit style="width: 14px; height: 14px" @click.stop="onEditClick(item)" />
          <Delete style="width: 14px; height: 14px" @click.stop="onDeleteClick(item)" />
        </div>
      </div>
    </div>
    <div class="right">
      <div class="right-title">
        {{ selectedGroup === null ? '请先创建或选择分组' : '请勾选频道' }}
      </div>
      <div ref="rightScroll" class="right-content">
        <el-tree
          v-if="channelLoading === false"
          ref="tree"
          style="max-width: 600px"
          :data="channelData"
          :default-checked-keys="defaultCheckedKeys"
          show-checkbox
          default-expand-all
          node-key="id"
          :props="defaultProps"
          @check-change="handleCheckChange"
        />
        <div v-else class="loading">加载中...</div>
      </div>
      <div class="right-bottom">
        <el-button type="primary" @click="onSaveChannelsWithGroup">保存</el-button>
      </div>
    </div>
    <el-dialog v-model="groupNameEditDialogVisible" title="添加分组" width="600">
      <GroupNameEditComponent
        v-if="groupNameEditDialogVisible"
        :id="currentEditGroup ? currentEditGroup.id : null"
        :name="currentEditGroup ? currentEditGroup.group_name : ''"
        @cancel="onGroupNameEditCancel"
        @confirm="onGroupNameEditConfirm"
      ></GroupNameEditComponent>
    </el-dialog>

    <el-dialog v-model="groupNameDeleteDialogVisible" title="删除提示" width="500">
      <span>确认删除 {{ currentDeleteGroup && currentDeleteGroup.name }}</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="groupNameDeleteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onDeleteGroupNameConfirmClick"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 50vh;
  .left {
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
    align-items: center;
    width: 200px;
    gap: 10px;
    height: 50vh;

    .group-button-add {
      width: 172px;
      height: 32px;
      border-style: dashed; /* 设置边框为虚线 */
      border-color: #409eff; /* 设置边框颜色为element-plus的主色蓝色 */
      border-width: 1px;
      color: #409eff; /* 设置文字颜色为蓝色 */
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .plus-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }

    .group-button {
      width: 172px;
      height: 32px;
      color: #606266; /* 设置文字颜色为默认灰色 */
      cursor: pointer; /* 设置鼠标样式为手型 */
      display: flex; /* 设置为内联块元素 */
      justify-content: space-between;
      align-items: center;
      text-align: center; /* 文字居中对齐 */
      line-height: 32px; /* 设置行高与高度一致，实现垂直居中 */
      padding: 0 15px; /* 设置内边距 */
      padding-right: 4px;
      border-radius: 4px; /* 设置圆角 */
      background-color: #f8f8f8;
      transition:
        background-color 0.3s,
        border-color 0.3s,
        color 0.3s; /* 设置过渡效果 */

      .group-button-icon {
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;
        visibility: hidden; // 初始隐藏
      }
    }

    .group-button:hover {
      background-color: #e6f7ff; /* 鼠标悬停时背景颜色变化为浅蓝色 */
      color: #409eff; /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
      .group-button-icon {
        visibility: visible; // 鼠标悬停时显示
      }
    }

    .group-button-selected {
      background-color: #e6f7ff; /* 鼠标悬停时背景颜色变化为浅蓝色 */
      color: #409eff; /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
      .group-button-icon {
        visibility: visible; // 鼠标悬停时显示
      }
    }
  }
  .right {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex: 1;
    border-color: #f4f4f4;
    border-width: 1px;
    border-style: solid;
    height: 50vh;
    .right-content {
      margin-top: 8px;
      margin-bottom: 12px;
      flex: 1;
      overflow-y: scroll;
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }
    }
    .right-bottom {
      align-self: end;
    }
  }
}
</style>
