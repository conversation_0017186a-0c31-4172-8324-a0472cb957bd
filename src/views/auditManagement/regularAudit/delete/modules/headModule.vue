<template>
  <div class="head-box">
    <div class="left">
      <!-- <el-button type="primary"
                 v-auth="'comment:batch_action'"
                 :disabled="disabled==0"
                 @click="emit('deleteClick')">
        批量撤销并通过
      </el-button> -->
      <el-select v-model="searchData.time_type" style="width: 110px">
        <el-option
          v-for="item of timeType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        ></el-option>
      </el-select>
      <el-date-picker
        v-model="time"
        style="width: 220px; margin-right: 10px"
        class="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="defaultTime"
      >
      </el-date-picker>
    </div>
    <div class="right">
      <!-- 自动治理 -->
      <div class="auto-governance">
        <el-select v-model="searchData.govern" style="width: 110px">
          <el-option
            v-for="item of autoGovernanceType"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          ></el-option>
        </el-select>
      </div>
      <!-- 圈子 -->
      <el-cascader
        v-model="circleValue"
        :options="circleList"
        :show-all-levels="false"
        clearable
        collapse-tags
        :props="optionsProps2"
        placeholder="请选择圈子"
        style="margin-right: 10px; width: 180px"
        :disabled="circleDisabled"
        @change="changeCircle"
      />
      <!-- 全部频道 筛选-->
      <el-cascader
        v-model="cascadeValue"
        :options="options"
        :show-all-levels="false"
        collapse-tags
        clearable
        :props="optionsProps"
        placeholder="全部频道"
        style="margin-right: 10px; width: 180px"
        :disabled="channelDisabled"
        @change="change"
      />
      <!-- 分组 -->
      <div v-if="groupList.length > 0" v-auth="'comment:group_feature'">
        <el-cascader
          v-model="groupValue"
          :options="groupList"
          :show-all-levels="false"
          clearable
          collapse-tags
          :props="groupListProps"
          placeholder="请选择分组"
          style="margin-right: 10px; width: 130px"
          :disabled="groupDisabled"
          @change="changeGroup"
        />
      </div>

      <el-select
        v-model="searchData.comment_type"
        style="width: 110px"
        :disabled="allCommentDisabled"
      >
        <el-option
          v-for="item of commentType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
        <!-- 1.0.9 小编回复 -->
        <el-option v-auth="'comment:editor_replay'" label="小编回复" value="EDITOR_REPLAY_TYPE">
        </el-option>
      </el-select>
      <el-divider direction="vertical" />
      <el-select
        v-model="searchData.comment_search_type"
        placeholder="请选择"
        style="margin-right: 5px; width: 110px"
        @change="typeChange"
      >
        <el-option
          v-for="item of commentSearchType"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
      <el-input
        v-if="searchData.comment_search_type != 'ARTICLE_TITLE'"
        v-model="searchData.searchword"
        placeholder="请输入关键词"
        class="input-with-select"
        style="width: 200px"
        clearable
      >
      </el-input>
      <!-- 稿件标题搜索的实时下拉 -->
      <el-select
        v-if="searchData.comment_search_type == 'ARTICLE_TITLE'"
        v-model="title_search_data"
        style="width: 200px; margin-right: 0"
        filterable
        remote
        :remote-method="remoteMethod"
        :loading="loading"
        clearable
        popper-class="selectElem"
        :teleported="false"
        placeholder="请输入稿件关键字"
        @change="titleChange"
      >
        <el-option
          v-for="item in title_select_Data"
          :key="item.key"
          :label="item.label"
          :value="item.label"
        />
      </el-select>
      <el-button
        color="#0B82FD"
        type="primary"
        :icon="Search"
        style="margin-left: 5px"
        @click="handSearch"
      >
        搜索
      </el-button>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose, computed, watch } from 'vue'
// 组件接收事件
const emit = defineEmits(['search', 'deleteClick'])
// 组件传参声明方式
const props = defineProps({
  disabled: {
    type: Number
  }
})
// 时间选择器
const time = ref(null)
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})
const channelDisabled = computed(() => {
  if (groupValue.value && Array.isArray(groupValue.value) && groupValue.value.length > 0) {
    return true
  } else {
    return false
  }
})
// 自动治理
const autoGovernanceType = ref([
  {
    key: 0,
    label: '全部类型'
  },
  {
    key: 1,
    label: '自动治理'
  }
])

// 全部分组
const groupList = ref([])
const groupValue = ref(null)
const groupListProps = ref({
  value: 'id',
  label: 'group_name',
  multiple: false
})
const groupDisabled = computed(() => {
  if (searchData.value.govern === 1) {
    return true
  }
  if (cascadeValue.value && Array.isArray(cascadeValue.value) && cascadeValue.value.length > 0) {
    return true
  }
  return false
})
function changeGroup() {
  if (groupValue.value) {
    searchData.value.group_id = groupValue.value.join(',')
  } else {
    searchData.value.group_id = ''
  }
  handSearch()
}
// 圈子
const circleValue = ref(null)
const optionsProps2 = ref({
  value: 'id',
  label: 'name',
  multiple: true
})
// 稿件标题下拉的实时数据
const title_select_Data = ref([])
// 稿件标题时的关键字
const title_search_data = ref(null)
// 圈子类型
const circleList = ref([])
// 日期类型
const timeType = ref([
  {
    key: 'COMMENT_DATE',
    label: '评论日期'
  },
  {
    key: 'AUDIT_DATE',
    label: '审核日期'
  }
])
// 评论类型
const commentType = ref([
  {
    key: 'ALL',
    label: '全部评论'
  },
  {
    key: 'ARTICLE_TYPE',
    label: '稿件评论'
  },
  {
    key: 'REPLY_TYPE',
    label: '回复评论'
  }
])
// 搜索类型
const commentSearchType = ref([
  {
    key: 'CONTENT',
    label: '评论内容'
  },

  {
    key: 'ARTICLE_TITLE',
    label: '稿件标题'
  },
  {
    key: 'COMMENT_PERSON',
    label: '评论人'
  },
  {
    key: 'AUDIT_PERSON',
    label: '审核人'
  },
  {
    key: 'ARTICLE_ID',
    label: '稿件id'
  }
])
const loading = ref(false)
// 搜索参数
const searchData = ref({
  state: 'UNPASS', // 状态(PENDING-待审核，PASS-审核通过，UNPASS-已删除)
  time_type: 'COMMENT_DATE', // 评论日期类型（COMMENT_DATE：评论日期 AUDIT_DATE：审核日期.）
  start_time: null, // 起始时间
  end_time: null, // 结束时间
  govern: 0, // 是否自动治理（0：否 1：是）
  category_id: '',
  circle_id: '',
  comment_source: '', // 频道名称
  comment_type: 'ALL', // 筛选评论类型（ARTICLE_TYPE：稿件评论 REPLY_TYPE：回复评论）
  comment_search_type: 'CONTENT', // 搜索类型（ALL：全部  CONTENT：评论内容 COMMENT_SOURCE：评论来源 ARTICLE_TITLE：稿件标题 COMMENT_PERSON：评论人）
  searchword: null, // 关键词
  size: 100, // 每页条数
  current: 1 // 分页页码 默认1
})
// 圈子禁用
const circleDisabled = computed(() => {
  return searchData.value.govern === 1
})
// 全部评论禁用
const allCommentDisabled = computed(() => {
  return searchData.value.govern === 1
})
watch(
  () => searchData.value.govern,
  (newValue, oldValue) => {
    if (newValue === 1) {
      groupValue.value = null
      searchData.value.group_id = ''
      searchData.value.comment_type = 'ALL'
      circleValue.value = null
      if (searchData.value.comment_search_type === 'AUDIT_PERSON') {
        searchData.value.comment_search_type = 'CONTENT'
      }
      commentSearchType.value = [
        {
          key: 'CONTENT',
          label: '评论内容'
        },

        {
          key: 'ARTICLE_TITLE',
          label: '稿件标题'
        },
        {
          key: 'COMMENT_PERSON',
          label: '评论人'
        },
        {
          key: 'ARTICLE_ID',
          label: '稿件id'
        }
      ]
    } else if (newValue === 0) {
      commentSearchType.value = [
        {
          key: 'CONTENT',
          label: '评论内容'
        },

        {
          key: 'ARTICLE_TITLE',
          label: '稿件标题'
        },
        {
          key: 'COMMENT_PERSON',
          label: '评论人'
        },
        {
          key: 'AUDIT_PERSON',
          label: '审核人'
        },
        {
          key: 'ARTICLE_ID',
          label: '稿件id'
        }
      ]
    }
    console.log('govern changed from', oldValue, 'to', newValue)
  }
)
onMounted(() => {
  requests.listCategoryTree().then(res => {
    let arr = res.data.category_list
    for (let i = 0; i < arr.length; i++) {
      if (parseInt(arr[i].permission) == 0) {
        arr.splice(i--, 1)
      } else {
        for (let k = 0; k < arr[i].children.length; k++) {
          if (arr[i].children[k].permission == 0) {
            arr[i].children.splice(k--, 1)
          } else {
            for (let a = 0; a < arr[i].children[k].children.length; a++) {
              if (arr[i].children[k].children[a].permission == 0) {
                arr[i].children[k].children.splice(a--, 1)
              }
            }
          }
        }
      }
    }
    setTimeout(() => {
      options.value = arr
    })
  })
  requests.getcircleList({}).then(res => {
    circleList.value = res.data.circle_list
  })
  requests.getGroupListApi().then(res => {
    groupList.value = res.data.category_group_list
  })
  emit('search', searchData.value)
  // 初始
})
const typeChange = () => {
  title_search_data.value = ''
  //   searchData.value.searchword = ''
}
const remoteMethod = query => {
  console.log('query', query)
  title_select_Data.value = []
  if (query) {
    loading.value = true
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.size = 10
    newSearchData.current = 1
    newSearchData.searchword = query
    // 请求
    requests.searchList(newSearchData).then(res => {
      loading.value = false
      for (let i = 0; i < res.data.list.length; i++) {
        title_select_Data.value.push({
          key: res.data.list[i].article_id,
          label: `【${res.data.list[i].article_id}】${res.data.list[i].article_title}`
        })
      }
    })
  } else {
    title_select_Data.value = []
  }
}
const change = () => {
  console.log(cascadeValue.value)
  if (cascadeValue.value) {
    let data = cascadeValue.value.join(',')
    data = data.split(',')
    data = Array.from(new Set(data))
    searchData.value.category_id = data.join(',')
  } else {
    searchData.value.category_id = ''
  }

  handSearch()
}
// 圈子选中调用
function changeCircle() {
  if (circleValue.value) {
    searchData.value.circle_id = circleValue.value.join(',')
  } else {
    searchData.value.circle_id = ''
  }
  handSearch()
}
const handSearch = () => {
  if (time.value) {
    console.log('时间', time.value)
    searchData.value.start_time = time.value[0]
    searchData.value.end_time = time.value[1]
  } else {
    searchData.value.start_time = ''
    searchData.value.end_time = ''
  }
  searchData.value.current = 1
  if (searchData.value.comment_search_type == 'ARTICLE_TITLE') {
    let newSearchData = JSON.parse(JSON.stringify(searchData.value))
    newSearchData.comment_search_type = 'ARTICLE_ID'
    if (!title_search_data.value) {
      title_search_data.value = ''
    } else {
      newSearchData.searchword = title_search_data.value.slice(
        title_search_data.value.indexOf('【') + 1,
        title_search_data.value.indexOf('】')
      )
    }
    console.log('title_search_data.value', title_search_data.value)
    emit('search', newSearchData)
  } else {
    emit('search', searchData.value)
  }
}
// 稿件远程 选中
const titleChange = val => {
  let newSearchData = JSON.parse(JSON.stringify(searchData.value))
  if (val) {
    newSearchData.comment_search_type = 'ARTICLE_ID'
    newSearchData.searchword = val.slice(val.indexOf('【') + 1, val.indexOf('】'))
  }
  emit('search', newSearchData)
}
defineExpose({
  searchData,
  title_search_data
})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
  background-color: #ebebeb;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .left {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    align-content: start;
    flex-wrap: wrap;
    margin-right: 10px;
    row-gap: 6px;

    .el-button {
      float: left;
      margin-right: 10px;
    }

    .el-select {
      width: 110px;
      float: left;
      margin-right: 10px;
    }
  }

  .right {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 6px;

    & > .el-select {
      width: 110px;
    }

    .auto-governance {
      margin-right: 12px;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
