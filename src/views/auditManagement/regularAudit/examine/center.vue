<template>
  <headModel
    ref="searchBox"
    :disabled="data.idsAll.length > 1"
    :data-ids-pass="data.idsAll.length"
    @search="search"
    @deleteClick="deleteClick"
    @batchDeleteClick="batchDeleteClick"
  />
  <div id="tabel-component">
    <el-table
      v-loading="loading"
      :data="data.records"
      :height="data.height"
      size="default"
      @selection-change="handleSelectionChange"
    >
      <!--  -->
      <el-table-column
        v-tab="'regular_pass_batch_delete'"
        type="selection"
        label="全选"
        width="40"
      />
      <el-table-column
        v-tab="'regular_pass_id'"
        align="center"
        prop="auto_pk"
        label="ID"
        width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-tab="'regular_pass_content'"
        v-slot="scope"
        label="评论内容"
        min-width="500"
      >
        <div
          v-if="scope.row.reply_info && scope.row.reply_info.reply_info_content"
          class="reply_info"
        >
          <span class="span-html" v-html="scope.row.reply_info.reply_info_content"></span>
          <el-tooltip
            v-if="scope.row.reply_info.sensitive_word"
            class="box-item"
            effect="light"
            :content="scope.row.reply_info.sensitive_word"
            placement="top"
          >
            <el-button
              type="danger"
              plain
              size="small"
              color="#F65520"
              style="margin-left: 5px; padding: 0 3px; height: 18px"
            >
              敏
            </el-button>
          </el-tooltip>
          <SpammerIcon v-if="scope.row.reply_info.spammer" />
          <!-- <WhiteUserIcon v-if="scope.row.white_list_user" /> -->
        </div>
        <p :class="scope.row.reply_info ? 'replyHide' : ''">
          <span v-if="scope.row.reply_info" class="reply">回复</span>
          <span class="span-html" v-html="scope.row.content"></span>
          <el-tooltip
            v-if="scope.row.sensitive_word"
            class="box-item"
            effect="light"
            :content="scope.row.sensitive_word"
            placement="top"
          >
            <el-button
              type="danger"
              plain
              size="small"
              color="#F65520"
              style="margin-left: 5px; padding: 0 3px; height: 18px"
            >
              敏
            </el-button>
          </el-tooltip>
          <SpammerIcon v-if="scope.row.spammer" />
          <WhiteUserIcon v-if="scope.row.white_list_user" />
        </p>
      </el-table-column>
      <el-table-column v-tab="'regular_pass_content'" v-slot="scope" align="center" width="60">
        <el-popover v-if="scope.row.expression_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 150px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.expression_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.expression_url"
              fit="cover"
            />
          </template>
        </el-popover>
        <el-popover v-if="scope.row.pic_url" trigger="hover" placement="top" width="auto">
          <div style="padding: 0 10px">
            <img
              style="height: 450px; cursor: pointer; margin: 0 auto; display: block"
              :src="scope.row.pic_url"
            />
          </div>

          <template #reference>
            <el-image
              style="width: 50px; height: 50px; cursor: pointer"
              :src="scope.row.pic_url"
              fit="cover"
            />
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column v-tab="'regular_pass_content'" v-slot="scope" width="40">
        <el-tooltip
          v-if="scope.row.comment_pic_risk_result.resultCode == 1"
          class="box-item"
          effect="light"
          :content="scope.row.comment_pic_risk_result.resultMsg"
          placement="top"
        >
          <el-button
            type="danger"
            plain
            size="small"
            color="#F65520"
            style="margin-left: 5px; padding: 0 3px; height: 18px"
          >
            敏
          </el-button>
        </el-tooltip>
      </el-table-column>
      <el-table-column
        v-tab="'regular_pass_title'"
        v-slot="scope"
        label="稿件标题"
        :show-overflow-tooltip="true"
        width="400"
      >
        <span
          v-if="scope.row.article_title"
          class="elem"
          @click="openLink(scope.row.article_url)"
          >{{ scope.row.article_title }}</span
        >
        <span v-else class="elem" @click="openLink(scope.row.article_url)">无标题</span>
      </el-table-column>
      <el-table-column
        v-tab="'regular_pass_comment_user_name'"
        v-slot="scope"
        label="评论人"
        width="120"
      >
        <SpammerMarker
          :disabled="true"
          v-model:spammer="scope.row.spammer"
          :userData="{
            user_name: scope.row.comment_account,
            user_id: scope.row.comment_account_id
          }"
        >
          <template #reference>
            <span>{{ scope.row.comment_account }}</span>
          </template>
        </SpammerMarker>
      </el-table-column>
      <el-table-column v-tab="'regular_pass_source'" prop="source" label="来源频道" width="95" />

      <el-table-column
        v-tab="'regular_pass_source_created_at'"
        prop="created_at"
        label="评论时间"
        width="170"
      />
      <el-table-column
        v-tab="'regular_pass_audit_admin_name'"
        v-slot="scope"
        label="审核人"
        width="150"
      >
        <div class="reviewed" style="" @click="handleHistory(scope.row)">
          <span style="margin-right: 10px">
            {{ scope.row.audit_admin_name }}
          </span>
        </div>
      </el-table-column>
      <el-table-column
        v-tab="'regular_pass_audit_time'"
        prop="audit_time"
        label="审核时间"
        width="170"
      />
      <el-table-column
        v-tab="'regular_pass_operate'"
        v-slot="scope"
        label="操作"
        width="140"
        fixed="right"
      >
        <el-button
          v-auth="'comment:batch_action_delete'"
          style="padding-left: 0"
          text
          @click="deleteClick(scope.row)"
        >
          <span class="button_text">删除</span>
        </el-button>
        <el-button
          v-auth="'comment:editor_replay'"
          text
          style="margin-left: 0"
          @click="replyClick(scope.row)"
        >
          <span class="button_text">回复</span>
        </el-button>
        <!-- <el-button type="primary"
                            @click="replyClick(scope.row)">
                            回复
                        </el-button> -->
      </el-table-column>
    </el-table>
  </div>

  <!-- 分页 -->
  <div class="page">
    <el-pagination
      small
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.total"
      :page-size="data.size"
      :current-page="data.current"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
    </el-pagination>
  </div>
  <OperationLog ref="operationLog" />
  <DeleteAlert
    ref="deleteAlert"
    :delete-alert-data="deleteAlertData"
    @search="search"
    @deleteClickFun="deleteClickFun"
  />
  <!-- 回复评论 -->
  <Reply ref="replyBox" :reply-data="replyData" @replySearch="replySearch" />
  <el-dialog v-model="dialogVisible" title="确定批量删除评论?" width="30%">
    <div class="tip">
      已选中评论<b>{{ data.idsAll.length }}</b
      >条
    </div>
    <br />
    <span class="info">删除后，可在“已删除”页面撤销</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchDelete"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import wenHao from '@/assets/images/wenHao.png'
import OperationLog from '@/components/business/operationLog.vue'
import DeleteAlert from '@/components/business/deleteAlert.vue'
import { requests } from '@/api/business/auditManagement'
import headModel from './modules/headModule.vue'
import Reply from './modules/reply.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Tickets } from '@element-plus/icons-vue'
import { computed, nextTick, ref } from 'vue'
import SpammerIcon from '@/components/SpammerIcon/index.vue'
import WhiteUserIcon from '@/components/WhiteUserIcon/index.vue'

import SpammerMarker from '@/components/SpammerMarker/index.vue'
const loading = ref(false)
const searchBox = ref(null)
const replyBox = ref(null)
const replyData = ref(null)
// 表格数据
const data = ref({
  height: null,
  idsAll: [], // 多选中的所有数据
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
const operationLog = ref(null)
// 删除弹窗
const deleteAlert = ref(null)
// 传入删除弹窗的数据
const deleteAlertData = ref({
  type: '已通过', // 判断哪个页面进去的
  comment_id: '', // 删除接口需要的comment_id
  comment_level: '',
  article_id: '',
  top_comment_id: '',
  second_comment_id: ''
})
// 批量删除
const batch_comment_id = ref([])
const dialogVisible = ref(false)
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 30
  data.value.height = height - bottom
})
// 高级标题跳转
function openLink(url) {
  window.open(url)
}
// 历史记录
function handleHistory(val) {
  operationLog.value.message.auto_pk = val.auto_pk
  operationLog.value.message.id = val.id
  operationLog.value.message.content = val.content
  operationLog.value.getList()
  operationLog.value.drawer = true
}
// 搜索接口
const search = val => {
  let obj
  // 如果是稿件标题
  if (val.comment_search_type == 'ARTICLE_TITLE') {
    obj = JSON.parse(JSON.stringify(val))
    obj.comment_search_type = 'ARTICLE_ID'
    if (searchBox.value.title_search_data) {
      obj.searchword = searchBox.value.title_search_data.slice(
        searchBox.value.title_search_data.indexOf('【') + 1,
        searchBox.value.title_search_data.indexOf('】')
      )
    }
  } else {
    obj = val
  }
  loading.value = true
  requests
    .list(obj)
    .then(res => {
      loading.value = false
      res.data.pg.records.forEach(v => {
        // 评论敏感词
        v.sensitives = ''
        // 回复评论敏感词
        v.sensitives2 = ''
        // // 敏感词分类 自定义敏感词 百度敏感词
        // if (v.sensitive_word.length > 0) {
        //   v = custom_detect_result(v)
        // } else {
        //   v = baidu_detect_result(v)
        // }
        // // 回复 敏感词 优先提取自定义敏感词
        // if (v.reply_info) {
        //   v.reply_info.new_reply_info_content = v.reply_info.reply_info_content
        //   if (v.reply_info.sensitive_word.length > 0) {
        //     v = custom_reply_info(v)
        //   } else {
        //     v = baidu_reply_info(v)
        //   }
        // }
        // 图片敏感词
        if (v.comment_pic_risk_result) {
          return (v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result))
        } else {
          return (v.comment_pic_risk_result = {})
        }
      })
      data.value.records = res.data.pg.records
      data.value.total = res.data.pg.total
      data.value.current = obj.current
    })
    .catch(error => {
      loading.value = false

      console.log(error)
    })
}
// 百度敏感词翻译
function sensitiveWord(val) {
  let label
  if (val == 'POLITICS') {
    label = '涉政'
  } else if (val == 'ABUSE') {
    label = '谩骂'
  } else if (val == 'PORN') {
    label = '色情'
  } else if (val == 'AD') {
    label = '广告'
  } else if (val == 'FLOOD') {
    label = '灌水'
  } else if (val == 'POLITY') {
    label = '涉政'
  } else {
    label = '其他'
  }
  return label
}
// 处理一般评论自定义敏感词返回
function custom_detect_result(val) {
  let str
  let sensitives = ''
  val.sensitive_word.forEach(v => {
    str = v.content
    if (sensitives.indexOf(v.type) < 0) {
      if (sensitives.length) {
        sensitives += '、' + v.type
      } else {
        sensitives += v.type
      }
    }
    if (val.newContent.indexOf(str) >= 0) {
      val.newContent = val.newContent.replace(str, '<span style="color:#F65520">' + str + '</span>')
    }
  })
  val.sensitives = sensitives
  return val
}
// 百度敏感词
function baidu_detect_result(val) {
  let sensitives = ''
  if (val.check_results.length) {
    val.check_results.forEach(v => {
      v.label = sensitiveWord(v.label)
      if (sensitives.indexOf(v.type) < 0) {
        if (sensitives.length) {
          sensitives += '、' + v.label
        } else {
          sensitives += v.label
        }
      }
      if (v.words && v.words.length) {
        v.words.forEach(k => {
          if (val.newContent.indexOf(k.word) >= 0) {
            val.newContent = val.newContent.replace(
              k.word,
              '<span style="color:#F65520">' + k.word + '</span>'
            )
          }
        })
      }
    })
    val.sensitives = sensitives
  }
  return val
}
// 回复自定义敏感词
function custom_reply_info(val) {
  let str
  let sensitives2 = ''
  val.reply_info.sensitive_word.forEach(v => {
    str = v.content
    if (sensitives2.indexOf(v.type) < 0) {
      if (sensitives2.length) {
        sensitives2 += '、' + v.type
      } else {
        sensitives2 += v.type
      }
    }
    if (val.reply_info.new_reply_info_content.indexOf(str) >= 0) {
      val.reply_info.new_reply_info_content = val.reply_info.new_reply_info_content.replace(
        str,
        '<span style="color:#F65520;">' + str + '</span>'
      )
    }
  })
  val.sensitives2 = sensitives2
  return val
}
// 百度回复敏感词
function baidu_reply_info(val) {
  if (val.reply_info.check_results.length) {
    let sensitives2 = ''
    val.reply_info.check_results.forEach(v => {
      v.label = sensitiveWord(v.label)
      if (sensitives2.indexOf(v.type) < 0) {
        if (sensitives2.length) {
          sensitives2 += '、' + v.label
        } else {
          sensitives2 += v.label
        }
      }
      if (v.words && v.words.length) {
        v.words.forEach(k => {
          if (val.reply_info.new_reply_info_content.indexOf(k.word) >= 0) {
            val.reply_info.new_reply_info_content = val.reply_info.new_reply_info_content.replace(
              k.word,
              '<span style="color:#F65520;">' + k.word + '</span>'
            )
          }
        })
      }
    })
    val.sensitives2 = sensitives2
  }
  return val
}
const handleSelectionChange = val => {
  data.value.idsAll = val
}
// 批量选择删除
const batchDeleteClick = () => {
  dialogVisible.value = true
}
const handleBatchDelete = () => {
  const batch_comment_id = data.value.idsAll.map(item => item.id).join(',')
  dialogVisible.value = false
  loading.value = true
  requests
    .batchAction({
      comment_ids: batch_comment_id,
      status: 'DELETE',
      current_status: 'PASS'
    })
    .then(res => {
      if (res.code == 0) {
        loading.value = false
        ElMessage({
          type: 'success',
          message: '批量删除成功！'
        })
      } else {
        ElMessage({
          type: 'error',
          message: '批量删除失败！'
        })
      }
      search(searchBox.value.searchData)
    })
    .catch(error => {
      ElMessage({
        type: 'error',
        message: '请求失败'
      })
    })
}
// 删除
const deleteClick = row => {
  // 单个删除
  deleteAlertData.value.comment_id = row.comment_id
  deleteAlertData.value.comment_level = row.comment_level
  ;(deleteAlertData.value.article_id = row.article_id),
    (deleteAlertData.value.top_comment_id = row.top_comment_id)
  deleteAlertData.value.second_comment_id = row.second_comment_id
  // 显示弹窗
  deleteAlert.value.dialogShow = true
}
const deleteClickFun = () => {
  loading.value = true
  // 删除接口
  requests
    .batchAction({
      comment_ids: deleteAlertData.value.comment_id,
      status: 'DELETE',
      current_status: 'PASS',
      article_id: deleteAlertData.value.article_id,
      top_comment_id: deleteAlertData.value.top_comment_id,
      second_comment_id: deleteAlertData.value.second_comment_id,
      comment_level: deleteAlertData.value.comment_level
    })
    .then(res => {
      console.log(res)
      loading.value = false
      if (res.code == 0) {
        deleteAlert.value.commentTableShow = false

        deleteAlert.value.dialogShow = false

        ElMessage({
          type: 'success',
          message: '删除成功！'
        })
      } else {
        ElMessage({
          type: 'error',
          message: '删除失败！'
        })
      }
      search(searchBox.value.searchData)
    })
    .catch(error => {
      ElMessage({
        type: 'error',
        message: '删除失败！'
      })
    })
}
// 选择每页几条
const handleSizeChange = val => {
  searchBox.value.searchData.size = val
  searchBox.value.searchData.current = 1
  data.value.size = val
  data.value.current = 1
  search(searchBox.value.searchData)
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  searchBox.value.searchData.current = val
  search(searchBox.value.searchData)
}
// 回复接口
const replyClick = row => {
  replyData.value = row
  replyBox.value.drawer = true
}
const replySearch = () => {
  search(searchBox.value.searchData)
}
</script>

<style lang="scss" scoped>
/* 情感倾向 */
.emotion {
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 22px;
  position: relative;
  display: flex;
  align-items: center;
}

.emotion.unknow::before {
  content: ' ';
  width: 10px;
  height: 10px;
  background: #8a8a8a;
  border-radius: 50%;
  margin-right: 6px;
}

.emotion.centre::before {
  content: ' ';
  width: 10px;
  height: 10px;
  background: #177fef;
  border-radius: 50%;
  margin-right: 6px;
}

.emotion.straight::before {
  content: ' ';
  width: 10px;
  height: 10px;
  background: #27d6c5;
  border-radius: 50%;
  margin-right: 6px;
}

.emotion.burden::before {
  content: ' ';
  width: 10px;
  height: 10px;
  background: #fc4f0d;
  border-radius: 50%;
  margin-right: 6px;
}

::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}

.deleteAlert {
  .dialogTableVisibleTitle1,
  .dialogTableVisibleTitle2,
  .dialogTableVisibleTitle3 {
    span {
      display: inline-block;
      margin-left: 15px;
      color: #409eff;
      cursor: pointer;
    }
  }

  .dialogTableVisibleTitle2 {
    margin: 20px 0;
  }

  .dialogTableVisibleTitle3 {
    margin-bottom: 0;
    color: #409eff;
  }

  .dialogPage {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉

  .reply_info {
    font-size: 14px;
    line-height: 22px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 3px 5px;
    color: #666;
  }

  .replyHide {
    padding: 0 10px;
  }

  .reply {
    background: rgba(102, 177, 253, 0.23);
    border-radius: 2px;
    border: 1px solid #0b82fd;
    margin-right: 6px;
    font-size: 12px;
    color: #0b82fd;
    padding: 2px;
    font-family: 'Arial';
  }

  .reviewed {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
  }

  .elem:hover,
  .reviewed:hover {
    color: rgba(11, 130, 253, 0.7);
  }

  .elem {
    cursor: pointer;
  }
}

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.tip {
  i {
    color: #409eff;
    font-weight: bold;
    font-style: normal;
    margin: 0 10px;
  }

  b {
    color: red;
    font-style: normal;
    font-weight: bold;
    margin: 0 10px;
  }
}

.info {
  color: #f59a23;
}
</style>
