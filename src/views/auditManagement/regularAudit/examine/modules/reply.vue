<template>
  <el-drawer
    id="drawer" v-model="drawer" title="用户回复" :with-header="true" show-close="true" size="700px"
    @open="open" @close="close"
  >
    <el-form :model="form" label-width="120px" style="padding-right:12px;">
      <!-- 稿件 -->
      <el-form-item label="稿件标题：">
        <p class="text" v-html="props.replyData.article_title"></p>
      </el-form-item>
      <!-- 回复 -->
      <el-form-item label="回复评论：">
        <div>
          <p class="text" style="font-weight:bold" v-html="props.replyData.comment_account"></p>
          <p class="text" v-html="props.replyData.content"></p>
        </div>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="comment_content" type="textarea" maxlength="250" show-word-limit :rows="10" resize="none"
          placeholder="请输入回复内容" @input="change"
        />
        <div v-show="comment_content" class="endText">{{ sensitive_words }}</div>
        <svg-icon name="icon-face" style="width: 30px; height: 30px;" class="icon" @click="showEmojis = !showEmojis" />
        <!-- emoji表情 -->
        <Picker v-show="showEmojis" :data="emojiIndex" set="apple" :i18n="I18n" :show-preview="false" :show-search="false" :emoji-tooltip="false" :show-skin-tones="false" :auto-focus="true" @select="showEmoji" />
      </el-form-item>
      <!-- <el-form-item>
                <el-button type="primary">Create</el-button>
                <el-button>Cancel</el-button>
            </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button style="margin-right: 20px;" @click="drawer=false">取消</el-button>
        <el-button type="primary" @click="sureClick">
          确定
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup>
import { Picker, EmojiIndex } from 'emoj-vue-chao/src'
import datas from '@/assets/google.json'
import 'emoj-vue-chao/css/emoji-mart.css'
import {
  requests
} from '@/api/business/auditManagement'

import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  ref,
  defineExpose
} from 'vue'
// 组件传参声明方式
const props = defineProps({
  replyData: Object
})
const emojiIndex = new EmojiIndex(datas)
const showEmojis = ref(false)
const data = ref({
  article_title: '',
  comment_account: '',
  content: ''
})
const emit = defineEmits(['replySearch'])
const loading = ref(false)
const drawer = ref(false) // 界面显示
const comment_content = ref('') // 回复评论
const sensitive_words = ref(null) // 提示语句
onMounted(() => {

})
function showEmoji(emoji) {
  comment_content.value += emoji.native
}
// 打开弹窗时
const open = () => {
}
// 关闭时
const close = () => {
  comment_content.value = ''
}
const change = () => {
  sensitive_words.value = ''
}
// 点击确定
const sureClick = () => {
  // 敏感词接口
  requests.checkSensitiveWord({
    comment_content: comment_content.value
  }).then(res => {
    console.log(res)
    // 校验
    if (res.code == 0) {
      // 存在敏感词
      if (res.data.result.length) {
        // 提取敏感词数组
        let detectResult = res.data.result
        let arr = []
        for (let i = 0; i < detectResult.length; i++) {
          arr.push(detectResult[i].content)
        }
        sensitive_words.value = `发现敏感词：${arr.toString()}`
        // 如果有敏感词
        ElMessageBox.confirm(
          `经校验，发现<b style="color:red;">${arr.toString()}</b>为敏感词，是否确认继续提交当前回复！`,
          '提示', {
            cancelButtonText: '取消',
            confirmButtonText: '确定',
            type: 'Warning',
            dangerouslyUseHTMLString: true,
            center: true
          }
        )
          .then(() => {
            // 忽略敏感词 继续提交
            editorReplay()
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消！'
            })
          })
      } else {
        // 没有敏感词
        editorReplay()

      }
    } else {

      // ElMessage({
      //     type: 'error',
      //     message: '敏感词校验接口失败！'
      // })
    }
  })
    .catch(error => {
      // ElMessage({
      //     type: 'error',
      //     message: '敏感词校验接口失败！'
      // })
    })

}
// 小编回复接口
const editorReplay = () => {
  requests.editorReplay({
    replay_comment_id: props.replyData.id,
    replay_comment_content: comment_content.value,
    channel_id: props.replyData.channel_id,
    channel_name: props.replyData.channel_name

  }).then(res => {
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '回复成功！'
      })
      drawer.value = false
      setTimeout(() => {
        emit('replySearch')
      }, 1000)
    } else {
      ElMessage({
        type: 'error',
        message: '回复失败！'
      })
    }
  }).catch(error => {
    ElMessage({
      type: 'error',
      message: '回复失败！'
    })
  })
}
defineExpose({
  drawer
})

</script>
<style lang="scss" scoped>
    p {
        margin: 0;
    }

    .endText {
        width: 100%;
        color: red;
    }
    .icon{
        position: absolute;
        right: 20px;
        top: 230px;
        cursor: pointer;
    }
</style>
