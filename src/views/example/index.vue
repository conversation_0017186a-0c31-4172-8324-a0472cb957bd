<template>
  <AbsoluteContainer>
    <page-main>
      <headModel />
      <el-table
        :data="dataList"
        border
        stripe
        highlight-current-row
      >
        <el-table-column
          type="index"
          width="50"
        />
        <el-table-column
          prop="date"
          label="活动名称"
          width="180"
        />
        <el-table-column
          prop="name"
          label="活动开始时间"
          width="180"
        />
        <el-table-column
          prop="address"
          label="活动结束时间"
          width="180"
        />
      </el-table>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup name="cardList">
import headModel from './modules/headModule.vue'
import { computed, nextTick, ref } from 'vue'

const data = ref({
  dataList: [

  ],
  selectionDataList: []
})
onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementsByClassName('el-table__header-wrapper')[0].offsetHeight
    document.getElementsByClassName('el-table__body-wrapper')[0].style.height = `calc(100% - ${height}px)`
  })
})
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  :deep(.el-table) {
    height: 100%;
    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}
</style>
