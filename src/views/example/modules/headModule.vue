<template>
  <div class="head-box">
    <div class="left">
      <div class="moudle">
        <div class="label">
          标签名
        </div>
        <el-input
          v-model="input"
          size="small"
          placeholder="Please input"
        />
      </div>
    </div>
    <div class="right">
      <el-button
        :icon="Search"
        size="small"
        circle
      />
    </div>
  </div>
</template>
<script setup name="head">
import { Search } from '@element-plus/icons'

// 组件传参声明方式
const props = defineProps({
  foo: String
})
// 组件抛出事件
const emit = defineEmits(['change', 'delete'])
// 参数声明
const input = ref('')
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-end;
  .left {
    display: flex;
    flex-wrap: wrap;
    margin-right: 40px;
    .moudle {
      display: flex;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;
      .label {
        flex-shrink: 0;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }
    }
  }
  .right {
    min-width: 5%;
    margin-bottom: 10px;
  }
}
</style>
