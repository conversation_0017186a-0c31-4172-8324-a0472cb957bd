<template>
  <AbsoluteContainer>
    <page-main>
      <headModel ref="searchBox" @search="search" @openHistory="openHistory" />
      <div id="tabel-component">
        <el-table v-loading="loading" size="default" :data="tableData.records" :height="data.height">
          <el-table-column label="序号" width="50">
            <template #default="{ $index }">
              {{ (tableData.current - 1) * tableData.size +  $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="comment_user_id" label="用户ID" />
          <el-table-column prop="comment_user_name" label="用户昵称"></el-table-column>
          <el-table-column prop="opt_user_name" label="操作人" />
          <el-table-column prop="opt_at" label="操作时间" v-slot="scope">
            {{ handleTime(scope.row.created_at) }}</el-table-column
          >
          <el-table-column v-slot="scope" label="操作" width="100">
            <el-button
              v-auth="'spammer_user:handle'"
              text
              style="padding-left: 0"
              @click="setClick(scope.row)"
            >
              <span style="color: #0b82fd">移除</span>
            </el-button>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="page">
        <el-pagination
          background
          :page-size="tableData.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          :current-page="tableData.current"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <History ref="history" />
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import { requests } from '@/api/business/operationManagement'
import headModel from './modules/headModule.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { computed, nextTick, ref } from 'vue'
import History from "./modules/history.vue"
const loading = ref(false)
const searchBox = ref(null)
const history = ref(null)
// 表格数据
const data = ref({

})
const tableData = ref({
  total: 0, // 总条目数
  size: 10, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
onMounted(() => {
  let height = document.getElementById('tabel-component').offsetHeight
  let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight - 20
  data.value.height = height - bottom
  init()
})

function init() {
  let data = {
    ...tableData.value
  }
  delete data.records
  delete data.total
  requests
    .waterUserList(data)
    .then(res => {
      loading.value = false
      tableData.value.records = res.data.list.records
      tableData.value.total = res.data.list.total
      //   data.value.current = obj.current;
    })
    .catch(error => {
      console.log(error)
    })
}
// 搜索接口
const search = obj => {
  tableData.value = {
    ...tableData.value,
    ...obj,
    current: 1
  }
  init()
}
// 恢复
const setClick = val => {
  ElMessageBox.confirm('是否移除该灌水用户？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning'
  })
    .then(() => {
      // 恢复接口
      requests
        .waterUserDelete({
          type: 0,
          comment_user_id: val.comment_user_id,
          comment_user_name:val.comment_user_name
        })
        .then(res => {
          console.log(res)
          ElMessage({
            type: 'success',
            message: '移除成功'
          })
          init()
        })
        .catch(error => {
          console.log(error)
        })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消！'
      })
    })
}
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
// 打开历史记录
const openHistory = val => {
  history.value.drawer = val
  history.value.getList()
}
// 选择每页几条
const handleSizeChange = val => {
  tableData.value.size = val
  tableData.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  tableData.value.current = val
  init()
}
</script>

<style lang="scss" scoped>
.page-main {
  display: flex;
  flex-direction: column;
  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  height: calc(100% - 30px);
  .elem {
    color: #409eff;
  }
}
#tabel-component {
  height: 100%;
}
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
