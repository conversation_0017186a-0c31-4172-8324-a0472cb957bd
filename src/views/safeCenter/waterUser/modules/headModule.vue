<template>
  <div class="head-box">
    <div class="left"></div>
    <div class="right">
      <el-input
        v-model="searchData.keyword"
        placeholder="请输入关键词"
        class="input-with-select"
        clearable
      >
        <template #prepend>
          <el-select v-model="searchData.search_type" placeholder="请选择" style="width: 100px">
            <el-option
              v-for="item of searchTypeData"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
        <template #append>
          <el-button
            type="primary"
            :icon="Search"
            style="display: flex; align-items: center"
            @click="handSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
      <el-divider direction="vertical" />
      <el-button type="primary" plain v-auth="'spammer_opt_log:list'" @click="open"
        >操作日志</el-button
      >
    </div>
  </div>
</template>
<script setup>
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose } from 'vue'
// 组件接收事件
const emit = defineEmits(['search', 'openHistory'])
// 搜索类型
const searchTypeData = ref([
  {
    key: 0,
    label: '用户id'
  },
  {
    key: 1,
    label: '用户昵称'
  }
])
// 搜索参数
const searchData = ref({
  search_type: 0, //	搜索类型（1：用户id 2：用户昵称）
  keyword: '' // 关键词
})
const open = () => {
  emit('openHistory', true)
}
// 点击搜索 每次当前页码变为1
const handSearch = () => {
  let data = {
    comment_user_id: '',
    comment_user_name: ''
  }
  if (searchData.value.search_type === 0) {
    data.comment_user_id = searchData.value.keyword
  } else {
    data.comment_user_name = searchData.value.keyword
  }
  emit('search', data)
}
defineExpose({
  searchData
})
</script>
<style lang="scss" scoped>
.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .right {
    & > .el-select {
      width: 150px;
      margin-right: 10px;
    }

    .el-input-group {
      width: 400px;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}
</style>
