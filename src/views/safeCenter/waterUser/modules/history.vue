<template>
  <el-drawer v-model="drawer" direction="rtl" title="操作日志" size="800px">
    <el-table :data="tableData" style="width: 100%" height="80vh">
      <el-table-column prop="opt_at_str" label="操作时间" width="180" />
      <el-table-column prop="comment_user_id" label="用户ID" />
      <el-table-column prop="comment_user_name" label="用户昵称" />
      <el-table-column prop="opt_item" label="事项说明" />
      <el-table-column prop="opt_user_name" label="操作人" width="100" />
    </el-table>
    <div class="page">
      <el-pagination
        background
        :page-size="data.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.total"
        :current-page="data.current"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>

    <!-- <el-input v-model="input" placeholder="输入关键词" /> -->
  </el-drawer>
</template>
<script setup name="operationLog">
import { ref, onMounted } from 'vue'
import { requests } from '@/api/business/operationManagement'
const drawer = ref(false)
const tableData = ref([])
const message = ref({})

function sureClick() {
  drawer.value = false
}
const data = ref({
  total: 0, // 总条目数
  size: 10, // 每页显示条目
  current: 1 // 当前页码
})
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  getList()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  getList()
}
function getList() {
  requests.getHistory({ current:data.value.current,size:data.value.size }).then(res => {
    tableData.value = res.data.list.records
    data.value.total = res.data.list.total
    // if (res.data.list.length) {
    //   message.value.content = res.data.list[0].comment_content
    // } else {
    //   message.value.content = ''
    // }
  })
}
defineExpose({
  drawer,
  getList
})
</script>
<style lang="scss" scoped>
.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
