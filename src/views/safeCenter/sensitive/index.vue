<template>
  <AbsoluteContainer>
    <page-main>
      <div class="container">
        <div class="left">
          <div v-auth="'sensitive_word_lib:create'" class="rule-button-add" @click="onAddLibClick">
            <img class="plus-icon" :src="Plus" />
            <span>创建词库</span>
          </div>
          <div class="rule-list">
            <div
              v-for="(item, index) in libList"
              :key="index"
              class="group-button"
              :class="{ 'group-button-selected': selectedLib?.risk_sensitive_word_lib_id === item.risk_sensitive_word_lib_id }"
              @click="onLibItemClick(item)"
            >
              <span>{{ item.name }}</span>
              <div class="rule-container-right">
                <div class="rule-button-icon">
                  <Edit
                    style="width: 14px; height: 14px; margin-right: 4px"
                    @click.stop="onEditClick(item)"
                  />
                  <Delete
                    style="width: 14px; height: 14px"
                    @click.stop="onDeleteClick(item)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="page-divider"></div>
        <div class="right">
          <SensitiveTable :lib-id="selectedLib?.risk_sensitive_word_lib_id" v-if="selectedLib?.risk_sensitive_word_lib_id" />
        </div>
      </div>
      <!-- 添加 Dialog -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑词库' : '新建词库'"
        width="500px"
        :close-on-click-modal="false"
  >
    <el-form :model="formData" label-width="80px">
      <el-form-item label="词库名称" required>
        <el-input v-model="formData.name" placeholder="请输入词库名称"></el-input>
      </el-form-item>
      <el-form-item label="绑定配置" required>
          <el-input  v-model="formData.code" placeholder="请输入词库编号"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import Plus from '@/assets/images/plus.png'
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import SensitiveTable from './modules/SensitiveTable.vue'
import { requests } from '@/api/secureCenter/sensitive.js'
const selectedLib = ref(null)
const libList = ref([])

// 添加新的响应式变量
const dialogVisible = ref(false)
const isEdit = ref(false)
const formData = ref({
  name: '',
  code: '',
  uuId: ''
})

// 获取词库列表
const getLibList = async (uuId) => {
  try {
    const res = await requests.getLibList({ current: 1, size: 999 })
    libList.value = res.data.page.records
    if (uuId === "latest" && libList.value.length > 0) {
      selectedLib.value = libList.value[libList.value.length - 1]
    } else if (uuId) {
      selectedLib.value = libList.value.find(item => item.risk_sensitive_word_lib_id === uuId)
    } else if (libList.value.length > 0 && !selectedLib.value) {
      selectedLib.value = libList.value[0]
    }
  } catch (err) {
    console.error(err)
  }
}

// 添加词库的点击事件
const onAddLibClick = () => {
  isEdit.value = false
  dialogVisible.value = true
  formData.value = {
    name: '',
    code: '',
    uuId: ''
  }
}

// 添加提交方法
const handleSubmit = async () => {
  if (!formData.value.name || (!isEdit.value && !formData.value.code)) {
    ElMessage.error('必填项不能为空')
    return
  }

  try {
    if (isEdit.value) {
      await requests.editLib({
        uuId: formData.value.uuId,
        name: formData.value.name,
        code: formData.value.code
      })
      getLibList(formData.value.uuId)
    } else {
      await requests.addLib({
        code: formData.value.code,
        name: formData.value.name
      })
      getLibList("latest")
    }
    ElMessage.success(isEdit.value ? '编辑成功' : '创建成功')
    dialogVisible.value = false
  } catch (error) {
    console.error(error)
  }
}

// 删除词库
const onDeleteClick = (item) => {
  ElMessageBox.confirm(`确定删除词库${item.name}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    requests.deleteLib({ uuId: item.risk_sensitive_word_lib_id }).then(() => {
      ElMessage.success('删除成功')
      if (selectedLib.value?.risk_sensitive_word_lib_id === item.risk_sensitive_word_lib_id) {
        getLibList('latest') // 删除当前选中的词库
      }else {
        getLibList() // 删除非当前选中的词库
      }

    })
  }).catch(() => {})
}

// 编辑词库
const onEditClick = (item) => {
  isEdit.value = true
  dialogVisible.value = true
  formData.value = {
    name: item.name,
    code: item.code,
    uuId: item.risk_sensitive_word_lib_id
  }
}

const onLibItemClick = (item) => {
  selectedLib.value = item
}

onMounted(() => {
  getLibList()
})
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .left {
    display: flex;
    flex-direction: column;
    justify-content: start;
    gap: 10px;
    height: 100%;
    overflow-y: auto;
    align-items: center;
    font-size: 14px;
    padding-top: 15px;
    padding-bottom: 15px;
    margin-right: 20px;

    .rule-button-add {
      width: 200px;
      height: 32px;
      border-style: dashed;
      /* 设置边框为虚线 */
      border-color: #0b82fd;
      /* 设置边框颜色为element-plus的主色蓝色 */
      border-radius: 6px;
      /* 设置圆角 */
      border-width: 1px;
      color: #0b82fd;
      /* 设置文字颜色为蓝色 */
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .plus-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }

    .rule-list {
      display: flex;
      flex-direction: column;
      flex: 1;
      gap: 10px;
      overflow-y: auto;
      height: 100%;

      .group-button {
        width: 200px;
        height: 32px;
        cursor: pointer;
        /* 设置鼠标样式为手型 */
        display: flex;
        /* 设置为内联块元素 */
        justify-content: space-between;
        align-items: center;
        text-align: center;
        /* 文字居中对齐 */
        line-height: 32px;
        /* 设置行高与高度一致，实现垂直居中 */
        padding: 0 15px;
        /* 设置内边距 */
        padding-right: 4px;
        border-radius: 4px;
        /* 设置圆角 */
        background-color: #f8f8f8;
        color: #333333;
        transition:
          background-color 0.3s,
          border-color 0.3s,
          color 0.3s;
        /* 设置过渡效果 */

        .rule-container-right {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 4px;

          .rule-right-switch-container {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .rule-button-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            visibility: hidden; // 初始隐藏
          }
        }
      }

      .group-button:hover {
        background-color: #cee6ff;
        /* 鼠标悬停时背景颜色变化为浅蓝色 */
        color: #0b82fd;

        /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
        .rule-button-icon {
          visibility: visible; // 鼠标悬停时显示
        }
      }

      .group-button-selected {
        background-color: #cee6ff;
        /* 鼠标悬停时背景颜色变化为浅蓝色 */
        color: #0b82fd;

        /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
        .rule-button-icon {
          visibility: visible; // 鼠标悬停时显示
        }
      }
    }
  }

  .operation-log {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .operation-log-divider {
      width: 100%;
      height: 1px;
      background-color: #efefef;
    }

    .log-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      margin-bottom: 2px;
      gap: 4px;
      cursor: pointer;

      .operation-log-icon {
        width: 14px;
        height: 14px;
      }

      .operation-log-title {
        font-size: 14px;
        color: #333333;
      }
    }
  }

  .page-divider {
    width: 1px;
    background-color: #efefef;
    height: 100%;
  }

  .right {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: scroll;
  }
}

</style>
