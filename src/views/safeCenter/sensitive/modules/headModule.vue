<template>
  <div class="head-box">
    <div class="left">
      <el-button v-auth="'sensitive_word:add'" color="#0B82FD" type="primary" @click="open"
        >添加敏感词</el-button
      >
      <el-button
        v-auth="'sensitive_word:delete'"
        type="danger"
        :disabled="disabled"
        @click="emit('deleteClick')"
      >
        批量删除
      </el-button>
    </div>
    <div class="right">
      <!-- 全部频道 筛选-->
      <el-input
        v-model="searchData.searchword"
        placeholder="请输入关键词"
        class="input-with-select"
        clearable
        @keyup.enter="handSearch"
      >
        <template #prepend>
          <el-select
            v-model="searchData.comment_search_type"
            placeholder="请选择"
            style="width: 100px"
            @change="typeChange"
          >
            <el-option
              v-for="item of commentSearchType"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </template>
        <template #append>
          <el-button
            type="primary"
            :icon="Search"
            color="#0B82FD"
            style="display: flex; align-items: center"
            @click="handSearch"
          >
            搜索
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>
<script setup>
import { requests } from '@/api/business/auditManagement'
import { Search } from '@element-plus/icons'
import { ref, onMounted, defineEmits, defineExpose } from 'vue'
// 组件传参声明方式
const props = defineProps({
  selectData: {
    type: Array
  },
  disabled: {
    type: Boolean,
    default: true
  }
})
// 组件接收事件
const emit = defineEmits(['search', 'openEdit', 'deleteClick'])
// 全部频道
const cascadeValue = ref(null)
const options = ref([])
const optionsProps = ref({
  value: 'category_id',
  label: 'name',
  multiple: true
})

// 稿件标题时的关键字
const title_search_data = ref(null)
// 搜索类型
const commentSearchType = ref([
  {
    key: 'CONTENT',
    label: '敏感词'
  }
])
// 搜索参数
const searchData = ref({
  comment_search_type: 'CONTENT', // 搜索类型（ALL：全部  CONTENT：评论内容 COMMENT_SOURCE：评论来源 ARTICLE_TITLE：稿件标题 COMMENT_PERSON：评论人）
  searchword: '' // 关键词
})

const typeChange = () => {
  title_search_data.value = ''
  searchData.value.searchword = ''
}

const open = () => {
  emit('openEdit', true)
}
// 点击搜索 每次当前页码变为1
const handSearch = () => {
  emit('search', searchData.value.searchword)
}

defineExpose({
  searchData,
  title_search_data
})
</script>
<style lang="scss" scoped>
::v-deep .selectElem {
  width: 300px;
}

::v-deep .selectElem .el-select-dropdown__item {
  white-space: pre-wrap;
  height: auto;
  line-height: 24px;
  padding: 5px 16px;
}

::v-deep .selectElem .el-select-dropdown__item.hover,
.selectElem .el-select-dropdown__item:hover {
  background-color: #ebebeb;
}

.head-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .right {
    & > .el-select {
      width: 150px;
    }

    .el-input-group {
      width: 400px;
    }
  }
}
</style>
<style>
.input-with-select .el-input-group__append {
  background-color: #1890ff;
  color: #fff;
}

.el-cascader {
  margin-right: 12px;
}
</style>
