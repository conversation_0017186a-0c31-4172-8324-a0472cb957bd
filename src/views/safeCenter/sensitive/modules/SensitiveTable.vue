<template>
  <div class="content-container">
    <headModel ref="searchBox" @search="search" @openEdit="openEdit" :disabled="delDisabled" @deleteClick="deleteClick"/>
    <div id="tabel-component" class="table-container">
        <el-table ref="table" fixed-header v-loading="loading" size="default" :data="data.records" :height="data.height" class="table"  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column width="80" label="序号">
            <template #default="scope">
              {{ (data.current - 1) * data.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="content" label="敏感词" />
          <el-table-column prop="type" label="分类" width="80" />
          <el-table-column prop="update_by_str" label="最后修改人" width="140" />

          <el-table-column prop="update_date_str" label="最后修改时间" width="200">
          </el-table-column>
          <el-table-column v-slot="scope" label="操作" fixed="right" width="140">
            <!-- v-auth="'comment_top:set_top'" -->
            <el-button v-auth="'sensitive_word:update'" text style="padding-left: 0" @click="edit(scope.row)">
              <span style="color:#0B82FD;">编辑</span>
            </el-button>
            <el-button v-auth="'sensitive_word:delete'" text style="margin-left: -4px" @click="del(scope.row)">
              <span style="color:#0B82FD;">删除</span>
            </el-button>
            <!-- v-auth="'comment_top:set_top'" -->
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="page">
        <el-pagination small background layout="total, sizes, prev, pager, next, jumper" :total="data.total"
          :page-size="data.size" :current-page="data.current" :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
      <el-drawer v-model="editBox" direction="rtl" :title="isEdit ? '编辑敏感词' : '添加敏感词'" :size="600"
        close-on-click-moda="false">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm" status-icon>
          <el-form-item label="敏感词名称" prop="content">
            <el-input v-model="ruleForm.content" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item label="敏感词类型" prop="type">
            <el-select v-model="ruleForm.type" placeholder="请选择权限类型">
              <el-option label="涉政" value="1" />
              <el-option label="涉黄" value="2" />
              <el-option label="涉恐" value="3" />
              <el-option label="广告" value="4" />
              <el-option label="谩骂" value="5" />
              <el-option label="其他" value="6" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div style="flex: auto;">
            <el-button @click="editBox = false">取消</el-button>
            <el-button :loading="loading2" type="primary" @click="submitForm('ruleFormRef')">确认</el-button>
          </div>
        </template>
      </el-drawer>
  </div>
</template>

<script setup>
import {
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  ref,
  onMounted
} from 'vue'
import { requests } from '@/api/secureCenter/sensitive.js'
import headModel from '../modules/headModule.vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  libId: {
    type: String,
    default: ''
  }
})

watch(
  () => props.libId,
  (newVal) => {
    if (newVal) {
      // 重置分页数据
      data.value.current = 1
      data.value.content = ''
      // 重新加载列表
      init()
      // 设置词库id
      ruleForm.value.risk_sensitive_word_lib_id = newVal
    }
  }
)

const table = ref(null)
const loading = ref(false)
const loading2 = ref(false)
const editBox = ref(false)
const ruleFormRef = ref(null)
const isEdit = ref(false)
const delDisabled = ref(true)
// 表格数据
const data = ref({
  content: '',
  height: null,
  total: 0, // 总条目数
  size: 100, // 每页显示条目
  current: 1, // 当前页码
  // 列表数据
  records: []
})
// 添加/编辑
const ruleForm = ref({
  risk_sensitive_word_lib_id: props.libId,
  content: '',
  type: '涉政'
})
const rules = ref({
  content: [
    { required: true, message: '请输入敏感词名称', trigger: 'blur' }
  ]
})
onMounted(() => {
  init()
})
// 时间转换
function handleTime(time) {
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
  return Y + M + D + h + m + s
}

// 多选
function handleSelectionChange(val){
  if(val.length){
    delDisabled.value = false
  }else{
    delDisabled.value = true
  }
}
// 批量删除
function deleteClick() {
  let checkList = table.value.getSelectionRows()
  let ids = []
  checkList.map(v => {
    ids.push(v.risk_sensitive_word_id)
  })
  ElMessageBox.confirm(
    '是否批量删除敏感词？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  }).then(() => {
    requests.allDel({
      uuIds: ids.join(',')
    }).then(res => {
      init()
      ElMessage({
        type: 'success',
        message: '删除成功！'
      })
    }).catch(error => {
      loading.value = false
      console.log(error)
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '已取消！'
    })
  })
}
// 删除
function del(row) {
  ElMessageBox.confirm(
    '是否删除该敏感词？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    icon: markRaw(Delete),
    type: 'Warning'
  }).then(() => {
    requests.del({
      uuId: row.risk_sensitive_word_id
    }).then(res => {
      init()
      ElMessage({
        type: 'success',
        message: '删除成功！'
      })
    }).catch(error => {
      loading.value = false
      console.log(error)
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '已取消！'
    })
  })
}

function search(val) {
  data.value.content = val
  data.value.current = 1
  init()
}

// 搜索接口
const init = val => {
  // 如果是稿件标题
  let newData = {
    risk_sensitive_word_lib_id: props.libId,
    content: data.value.content,
    current: data.value.current,
    size: data.value.size
  }
  loading.value = true
  requests.sensitiveList(newData)
    .then(res => {
      console.log('res', res)
      loading.value = false
      data.value.records = res.data.page.records
      data.value.total = res.data.page.total
    })
    .catch(error => {
      loading.value = false
      console.log(error)
    })
}

function getTypeNumber(type) {
  switch (type) {
    case '涉政': return '1'
    case '涉黄': return '2'
    case '涉恐': return '3'
    case '广告': return '4'
    case '谩骂': return '5'
    case '其他': return '6'
    default: return '6'
  }
}

// 新增/编辑
const openEdit = val => {
  ruleForm.value.type = getTypeNumber('涉政')
  ruleForm.value.content = ''
  isEdit.value = false
  editBox.value = true
}
function edit(val) {
  ruleForm.value.uuId = val.risk_sensitive_word_id
  ruleForm.value.content = val.content
  ruleForm.value.type = getTypeNumber(val.type)
  isEdit.value = true
  editBox.value = true
}
function submitForm(formName) {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      loading2.value = false
      let funName
      if (isEdit.value) {
        funName = 'edit'
      } else {
        funName = 'add'
      }
      //   formModel.value.exchangeRatio = formModel.value.exchangeRatio1 + ':' + formModel.value.exchangeRatio2
      requests[funName](ruleForm.value).then(res => {
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        editBox.value = false
        loading2.value = false
        init()
      }).catch(v => {
        loading2.value = false
      })
    } else {
      return false
    }
  })
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}

</script>

<style lang="scss" scoped>

#tabel-component {
  height: 100%;
}

.page {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.content-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.table {
  height: 100%;
}
</style>
