<template>
  <el-tooltip
    class="box-item"
    effect="dark"
    content="已关联其他规则"
    placement="top"
    :disabled="!tipVisible"
  >
    <el-tag class="tag-container" :style="tagStyle" @click="handleClick" >
      {{ text }}
    </el-tag>
  </el-tooltip>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElTag } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0 // 0 未选 1 已选 2 不可用
  },
  text: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const tipVisible = computed(() => {
  return alreadyBinding.value && !props.disabled
})

const emit = defineEmits(['update:modelValue'])

const alreadyBinding = computed(() => {
  return props.modelValue === 2
})

const alreadySelected = computed(() => {
  return props.modelValue === 1
})

const handleClick = () => {
  if (alreadyBinding.value) {
    return
  }
  if (props.disabled) {
    return
  }
  emit('update:modelValue', alreadySelected.value ? 0 : 1)
}

const tagStyle = computed(() => {
  if (alreadyBinding.value) {
    return {
      color: '#c0c4cc',
      borderColor: '#dcdfe6',
      backgroundColor: '#ffffff',
      cursor: 'not-allowed'
    }
  }
  if (alreadySelected.value) {
    return {
      color: '#0B82FD',
      borderColor: 'transparent',
      backgroundColor: '#EEF5FF',
      cursor: props.disabled ? 'not-allowed' : 'pointer'
    }
  }
  return {
    color: '#606266',
    borderColor: '#dcdfe6',
    backgroundColor: '#ffffff',
    cursor: props.disabled ? 'not-allowed' : 'pointer'
  }
})
</script>

<style scoped>
.tag-container {
  padding: 12px 12px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  cursor: pointer;
}
</style>
