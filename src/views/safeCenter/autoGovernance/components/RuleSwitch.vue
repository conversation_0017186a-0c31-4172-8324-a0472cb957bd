<template>
  <div
    class="custom-switch"
    :class="{ 'custom-switch-on': props.value === 1, 'custom-switch-off': props.value === 0 }"
  ></div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  value: {
    type: Number,
    required: true
  }
})
</script>

<style scoped>
.custom-switch {
  display: inline-block;
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: #d8d8d8;
  position: relative;
  cursor: pointer;
}

.custom-switch::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  transition: transform 0.3s;
}

.custom-switch-on {
  background-color: #0B82FD;
}

.custom-switch-on::before {
  transform: translateX(18px);
}

.custom-switch-off::before {
  transform: translateX(0px);
}
</style>
