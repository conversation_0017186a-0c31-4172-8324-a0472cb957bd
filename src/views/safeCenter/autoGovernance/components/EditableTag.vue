<script setup>
import { ref, nextTick, watch, onMounted } from 'vue'
import { ElInput, ElMessage } from 'element-plus'

const props = defineProps({
  label: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: Infinity
  }
})

onMounted(() => {
  if (!props.label) {
    startEditing()
  }
})

const emit = defineEmits(['delete', 'update:label'])

const isEditing = ref(false)
const editValue = ref(props.label)
const inputRef = ref(null)
const isDeleting = ref(false)
const tagWidth = ref(0)
const MIN_WIDTH = 100

watch(
  () => props.label,
  newValue => {
    if (!isEditing.value) {
      editValue.value = newValue
    }
  }
)

const startEditing = (event) => {
  if (props.disabled) {
    return
  }
  if (event) {
    // 获取点击的标签宽度
    tagWidth.value = Math.max(MIN_WIDTH, event.target.offsetWidth)
  } else {
    tagWidth.value = MIN_WIDTH
  }
  isEditing.value = true
  editValue.value = props.label
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
      // 设置输入框宽度与标签相同
      inputRef.value.$el.style.width = `${tagWidth.value}px`
    }
  })
}

const finishEditing = () => {
  if (isDeleting.value) {
    isDeleting.value = false
    return
  }
  if (!editValue.value) {
    ElMessage.error('标签不能为空')
    return
  }
  if (props.maxLength && editValue.value.length > props.maxLength) {
    ElMessage.error(`标签不能超过${props.maxLength}个字`)
    return
  }
  isEditing.value = false
  emit('update:label', editValue.value)
}

const deleteTag = () => {
  if (props.disabled) {
    return
  }
  emit('delete')
}
</script>

<template>
  <div class="editable-tag">
    <span
      v-if="!isEditing"
      :class="['tag-text', disabled ? 'tag-text-disable' : '']"
      @click="startEditing($event)"
    >{{ label }}</span>
    <el-input
      v-else
      v-model="editValue"
      ref="inputRef"
      @blur="finishEditing"
      @keyup.enter="finishEditing"
      class="input-edit-mode"
    />
    <CloseBold
      :class="['close-icon', disabled ? 'close-icon-disable' : '']"
      :color="disabled ? '#c0c4cc' : '#333333'"
      @mousedown="isDeleting = true"
      @click="deleteTag"
    ></CloseBold>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input--small .el-input__wrapper) {
  box-shadow: none;
}

.editable-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px;
  background-color: #f8f8f8;
  border-radius: 6px;
}

.close-icon {
  width: 10px;
  height: 10px;
  cursor: pointer;
  margin-left: 4px;
}

.close-icon-disable {
  cursor: not-allowed;
}

.tag-text {
  font-size: 14px;
  color: #000000;
  cursor: pointer;
  white-space: nowrap;
}

.tag-text-disable {
  color: #c0c4cc;
  cursor: not-allowed;
}

.input-edit-mode {
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  padding: 0 4px;
  background-color: transparent;
}

:deep(.el-input__inner) {
  border: none;
  background-color: transparent;
  padding: 0;
}
</style>
