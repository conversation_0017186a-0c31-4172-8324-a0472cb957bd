<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import EditableTag from '@/views/safeCenter/autoGovernance/components/EditableTag.vue'
import Plus from '@/assets/images/plus.png'
import PlusGray from '@/assets/images/plus-gray.png'
import ChannelTag from '@/views/safeCenter/autoGovernance/components/ChannelTag.vue'
import { requests } from '@/api/secureCenter/autoGovernance'
import { ElMessage } from 'element-plus'

const props = defineProps({
  rule: {
    type: Object,
    default: () => {}
  }
})

onMounted(() => {
  requests.ruleChannelListApi(props.rule?.id || '').then(res => {
    // 过滤 城市 和 中心频道 两个Channel
    channels.value = res.data.category_list?.filter(
      item => item.name !== '城市' && item.name !== '中心频道'
    )
  })
})

const isDisableEdit = ref(!!props.rule?.used)

// 规则名称
const ruleName = ref(props.rule?.rule_name || '')
// 无汉字勾选
const noCharacters = ref(!!props.rule?.no_characters || false)
// 纯数字勾选
const allSymbols = ref(!!props.rule?.all_symbols || false)
// 语句不通顺勾选
const smooth = ref(!!props.rule?.smooth || false)
// 纯字数，数字数大于等于（勾选）
const pureDigitalChecked = ref(!!props.rule?.pure_digital || false)
// 纯字数，数字数大于等于（数量）
const pureDigitalCount = ref(props.rule?.pure_digital || '')
// 纯表情，表情数大于等于（勾选）
const pureExpressionChecked = ref(!!props.rule?.pure_expression || false)
// 纯表情，表情数大于等于（数量）
const pureExpressionCount = ref(props.rule?.pure_expression || '')
// 文本重复率（勾选）
const textRepetitionRateChecked = ref(!!props.rule?.text_repetition || false)
// 文本重复率（数量）
const textRepetitionRateCount = ref(props.rule?.text_repetition || '')
// 自定义过滤文本（勾选）
const customFilterTextChecked = ref(!!props.rule?.text_filter || false)
// 自定义过滤文本（标签）
const textFilterTags = ref(props.rule?.text_filter?.split(',').filter(Boolean) || [])
const deleteTag = index => {
  textFilterTags.value.splice(index, 1)
  isEditTagMode.value = false
}

const isEditTagMode = ref(false)
const updateTag = (index, value) => {
  textFilterTags.value[index] = value
  isEditTagMode.value = false
}

const onAddFilterTagClick = () => {
  if (filterTextAddDisabled.value) {
    return
  }
  // 最多限制三个标签
  if (textFilterTags.value.length >= 8) {
    ElMessage.error('最多添加8个标签')
    return
  }
  textFilterTags.value.push('')
  isEditTagMode.value = true
}
// 单用户同稿件评论数大于等于（勾选）
const sameArtCommCountChecked = ref(!!props.rule?.same_art_comm_count || false)
// 单用户同稿件评论数大于等于（数量）
const sameArtCommCount = ref(props.rule?.same_art_comm_count || '')
// 单用户同稿件评论时间间隔（勾选）
const sameArtCommIntervalChecked = ref(!!props.rule?.same_art_comm_interval || false)
// 单用户同稿件评论时间间隔（数量）
const sameArtCommIntervalCount = ref(props.rule?.same_art_comm_interval || '')

// 单用户在指定时间内发布的总评论数（勾选）
const multiCommentDeleteChecked = ref(!!props.rule?.same_comm_interval_count || false)
// 单用户在指定时间内发布的总评论数（时间和数量）
const [multiCommentDeleteTime, multiCommentDeleteCount] = (
  props.rule?.same_comm_interval_count || ','
).split(',')
const multiCommentDeleteTimeRef = ref(multiCommentDeleteTime || '')
const multiCommentDeleteCountRef = ref(multiCommentDeleteCount || '')

// 一键勾选所有频道
const allChannelsChecked = ref(false)

// 监听allChannelsChecked的变化
// 立即执行的 watch
watch(allChannelsChecked, (newValue, oldValue) => {
  console.log('allChannelsChecked changed', newValue)
  // 只有binding不等于2的频道，才更新binding
  channels.value.forEach(channel => {
    if (channel.binding !== 2) {
      channel.binding = newValue ? 1 : 0
    }
  })
  // channels.value.forEach(channel => channel.binding = newValue ? 1 : 0)
})

const emit = defineEmits(['onRuleSaveSuccess'])

const onSaveClick = () => {
  console.log('save')
  const data = {}
  if (!ruleName.value) {
    ElMessage.error('规则名称不能为空')
    return
  }

  // 规则名称
  data.rule_name = ruleName.value
  // 无汉字
  data.no_characters = noCharacters.value ? 1 : 0
  // 纯符号
  data.all_symbols = allSymbols.value ? 1 : 0
  // 语句不通顺
  data.smooth = smooth.value ? 1 : 0
  // 纯数字
  if (pureDigitalChecked.value && !pureDigitalCount.value) {
    ElMessage.error('请输入纯数字，数字数大于等于的数量')
    return
  }
  data.pure_digital = pureDigitalChecked.value ? pureDigitalCount.value : 0
  // 纯表情
  if (pureExpressionChecked.value && !pureExpressionCount.value) {
    ElMessage.error('请输入纯表情，表情数大于等于的数量')
    return
  }
  data.pure_expression = pureExpressionChecked.value ? pureExpressionCount.value : 0
  // 文本重复率
  if (textRepetitionRateChecked.value && !textRepetitionRateCount.value) {
    ElMessage.error('请输入文本重复率的数量')
    return
  }
  data.text_repetition = textRepetitionRateChecked.value ? textRepetitionRateCount.value : 0
  // 自定义过滤文本
  if (customFilterTextChecked.value && !textFilterTags.value.length) {
    ElMessage.error('请输入自定义过滤文本')
    return
  }
  data.text_filter = customFilterTextChecked.value ? textFilterTags.value.join(',') : ''
  // 评论含链接
  if (commentContainsLinkChecked.value && !commentLinkTags.value.length) {
    ElMessage.error('请输入至少一个链接')
    return
  }
  data.url_filter = commentContainsLinkChecked.value ? commentLinkTags.value.join(',') : ''
  // 单用户同稿件评论数
  if (sameArtCommCountChecked.value && !sameArtCommCount.value) {
    ElMessage.error('请输入单用户同稿件评论数大于等于的数量')
    return
  }
  data.same_art_comm_count = sameArtCommCountChecked.value ? sameArtCommCount.value : 0
  // 单用户同稿件评论时间间隔
  if (sameArtCommIntervalChecked.value && !sameArtCommIntervalCount.value) {
    ElMessage.error('请输入单用户同稿件评论时间间隔的数量')
    return
  }
  data.same_art_comm_interval = sameArtCommIntervalChecked.value
    ? sameArtCommIntervalCount.value
    : 0
  // 单用户在指定时间内发布的总评论数
  if (
    multiCommentDeleteChecked.value &&
    (!multiCommentDeleteTimeRef.value || !multiCommentDeleteCountRef.value)
  ) {
    ElMessage.error('请输入单用户评论的时间和数量')
    return
  }
  data.same_comm_interval_count = multiCommentDeleteChecked.value
    ? `${multiCommentDeleteTimeRef.value},${multiCommentDeleteCountRef.value}`
    : ''
  // 机审涉敏
  // data.sensitivity_related = sensitivityRelatedChecked.value ? 1 : 0
  // 适用范围
  // let rangeApplication = 0
  // if (firstLevelCommentChecked.value && subLevelCommentChecked.value) {
  //   rangeApplication = 3
  // } else if (firstLevelCommentChecked.value) {
  //   rangeApplication = 1
  // } else if (subLevelCommentChecked.value) {
  //   rangeApplication = 2
  // }
  // data.range_application = rangeApplication
  // 适用频道
  data.channel_ids = channels.value
    .filter(item => item.binding === 1)
    .map(item => item.category_id)
    .join(',')

  // 判断频道和规则是否为空
  const isRuleConfigEmpty = [
    noCharacters.value,
    allSymbols.value,
    smooth.value,
    pureDigitalChecked.value,
    pureExpressionChecked.value,
    textRepetitionRateChecked.value,
    customFilterTextChecked.value,
    sameArtCommCountChecked.value,
    sameArtCommIntervalChecked.value,
    multiCommentDeleteChecked.value,
    // sensitivityRelatedChecked.value,
    commentContainsLinkChecked.value,
  ].every(item => !item)
  const isChannelEmpty = channels.value.every(item => item.binding !== 1)

  if (isRuleConfigEmpty) {
    ElMessage.error('请至少勾选一个规则')
    return
  }
  if (isChannelEmpty) {
    ElMessage.error('请至少选择一个频道')
    return
  }
  // 更新或者创建
  if (props.rule?.id) {
    // 编辑
    data.id = props.rule.id
    updateRule(data)
  } else {
    // 新增
    createRule(data)
  }
}

const updateRule = data => {
  requests.updateRuleApi(data).then(res => {
    ElMessage.success('保存成功')
    emit('onRuleSaveSuccess', data)
  })
}

const createRule = data => {
  requests.createRuleApi(data).then(res => {
    ElMessage.success('保存成功')
    emit('onRuleSaveSuccess', data)
  })
}

// 频道列表
const channels = ref([])

const filterTextAddDisabled = computed(() => {
  // 未勾选
  if (!customFilterTextChecked.value) {
    return true
  }
  // 编辑模式,正在添加中
  if (isEditTagMode.value) {
    return true
  }
  // 当前状态启用
  if (isDisableEdit.value) {
    return true
  }
  return false
})

function isNumeric(str) {
  return /^-?\d+(\.\d+)?$/.test(str)
}

// blur校验
const onBlurCheckNumber = (checkedNumber = '') => {
  if (!checkedNumber) {
    ElMessage.warning('请输入整数')
    return
  }
  if (!isNumeric(checkedNumber)) {
    ElMessage.warning('请输入整数')
    return
  }
}

// ===============评论含链接开始=======================
// 评论含链接勾选
const commentContainsLinkChecked = ref(!!props.rule?.url_filter)
// 评论含链接标签
const commentLinkTags = ref(props.rule?.url_filter?.split(',').filter(Boolean) || [])

const deleteLinkTag = index => {
  commentLinkTags.value.splice(index, 1)
}

const updateLinkTag = (index, value) => {
  commentLinkTags.value[index] = value
}

const onAddLinkTagClick = () => {
  if (linkAddDisabled.value) {
    return
  }
  if (commentLinkTags.value.length >= 10) {
    ElMessage.error('最多添加10个链接')
    return
  }
  commentLinkTags.value.push('')
}

const linkAddDisabled = computed(() => {
  return !commentContainsLinkChecked.value || isDisableEdit.value
})
// ===============评论含链接结束=======================
// 机审涉敏勾选
// const sensitivityRelatedChecked = ref(!!props.rule?.sensitivity_related || false)

// ===============适用范围开始=================
// const firstLevelCommentChecked = ref(
//   props.rule?.range_application === 1 || props.rule?.range_application === 3
// )
// const subLevelCommentChecked = ref(
//   props.rule?.range_application === 2 || props.rule?.range_application === 3
// )
// ===============适用范围结束=================
</script>

<template>
  <div class="rule-container">
    <div class="line-container">
      <div class="divider-tag" />
      <div :class="['required-label', 'line-container-title']">规则名称</div>
    </div>
    <div class="name-input">
      <el-input v-model="ruleName" :disabled="isDisableEdit" maxlength="10" show-word-limit />
    </div>
    <div class="line-divider" />
    <div class="line-container">
      <div class="divider-tag" />
      <div class="line-container-title">属性规则</div>
      <span class="text-hint">（多个选项之间为“或”关系）</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="customFilterTextChecked"
        :disabled="isDisableEdit"
        label="自定义过滤文本"
      />
      <div class="text-filter-tag-container">
        <EditableTag
          v-for="(tag, index) in textFilterTags"
          :disabled="isDisableEdit"
          :maxLength="3"
          :key="index"
          :label="tag"
          @delete="deleteTag(index)"
          @update:label="updateTag(index, $event)"
        />
      </div>
      <div
        :class="['filter-button-add', filterTextAddDisabled ? 'filter-button-add-disable' : '']"
        @click="onAddFilterTagClick"
      >
        <img v-if="!filterTextAddDisabled" class="plus-icon" :src="Plus" />
        <img v-else class="plus-icon" :src="PlusGray" />
        <span>添加</span>
      </div>
    </div>
    <div class="line-check-box-container">
      <el-checkbox v-model="noCharacters" :disabled="isDisableEdit" label="无汉字" />
    </div>
    <div class="line-check-box-container">
      <el-checkbox v-model="allSymbols" :disabled="isDisableEdit" label="纯符号" />
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="pureDigitalChecked"
        :disabled="isDisableEdit"
        label="纯数字，数字数量大于等于"
      />
      <el-input
        v-model="pureDigitalCount"
        :disabled="isDisableEdit || !pureDigitalChecked"
        placeholder="请输入整数"
        style="margin-left: 10px; margin-right: 10px"
        @blur="onBlurCheckNumber(pureDigitalCount)"
      />
      <span>个</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="pureExpressionChecked"
        :disabled="isDisableEdit"
        label="纯表情，表情数量大于等于"
      />
      <el-input
        v-model="pureExpressionCount"
        :disabled="isDisableEdit || !pureExpressionChecked"
        placeholder="请输入整数"
        @blur="onBlurCheckNumber(pureExpressionCount)"
        style="margin-left: 10px; margin-right: 10px"
      />
      <span>个</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="textRepetitionRateChecked"
        :disabled="isDisableEdit"
        label="文本重复率大于等于"
      />
      <el-input
        v-model="textRepetitionRateCount"
        :disabled="isDisableEdit || !textRepetitionRateChecked"
        placeholder="请输入整数"
        style="margin-left: 10px; margin-right: 10px"
        @blur="onBlurCheckNumber(textRepetitionRateCount)"
      />
      <span>%</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="commentContainsLinkChecked"
        :disabled="isDisableEdit"
        label="评论含链接"
      />
      <div
        :class="['filter-button-add', linkAddDisabled ? 'filter-button-add-disable' : '']"
        @click="onAddLinkTagClick"
        style="margin-left: 10px"
      >
        <img v-if="!linkAddDisabled" class="plus-icon" :src="Plus" />
        <img v-else class="plus-icon" :src="PlusGray" />
        <span>添加</span>
      </div>
    </div>
    <div class="text-links-tag-container">
      <EditableTag
        :maxLength="100"
        v-for="(link, index) in commentLinkTags"
        :key="index"
        :disabled="isDisableEdit"
        :label="link"
        @delete="deleteLinkTag(index)"
        @update:label="updateLinkTag(index, $event)"
      />
    </div>
    <div class="line-check-box-container">
      <el-checkbox v-model="smooth" :disabled="isDisableEdit" label="语句不通顺" />
    </div>
    <!-- <div class="line-check-box-container">
      <el-checkbox v-model="sensitivityRelatedChecked" :disabled="isDisableEdit" label="机审涉敏" />
    </div> -->
    <div class="line-container" style="margin-top: 40px">
      <div class="divider-tag" />
      <div :class="['line-container-title']">行为规则</div>
      <span class="text-hint">（多个选项之间为“或”关系）</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="sameArtCommCountChecked"
        :disabled="isDisableEdit"
        label="单用户同稿件评论数量大于等于"
      />
      <el-input
        :disabled="isDisableEdit || !sameArtCommCountChecked"
        v-model="sameArtCommCount"
        placeholder="请输入整数"
        style="margin-left: 10px; margin-right: 10px"
        @blur="onBlurCheckNumber(sameArtCommCount)"
      />
      <span>条</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        v-model="sameArtCommIntervalChecked"
        :disabled="isDisableEdit"
        label="单用户同稿件评论时间间隔小于等于"
      />
      <el-input
        v-model="sameArtCommIntervalCount"
        :disabled="isDisableEdit || !sameArtCommIntervalChecked"
        placeholder="请输入整数"
        style="margin-left: 10px; margin-right: 10px"
        @blur="onBlurCheckNumber(sameArtCommIntervalCount)"
      />
      <span>秒</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox
        label="单用户"
        v-model="multiCommentDeleteChecked"
        :disabled="isDisableEdit"
        class="checkbox-1"
      >
      </el-checkbox>
      <el-input
        v-model="multiCommentDeleteTimeRef"
        :disabled="isDisableEdit || !multiCommentDeleteChecked"
        placeholder="请输入正整数"
        style="width: 120px; margin: 0 10px"
        @blur="onBlurCheckNumber(multiCommentDeleteTimeRef)"
      />
      <el-checkbox
        id="multiCommentDeleteChecked"
        :model-value="multiCommentDeleteChecked"
        :disabled="isDisableEdit"
        @change="() => {}"
        class="no-checkbox"
      >
        <template #default>
          <span>秒内，发布的总评论大于等于</span>
        </template>
      </el-checkbox>
      <el-input
        v-model="multiCommentDeleteCountRef"
        :disabled="isDisableEdit || !multiCommentDeleteChecked"
        placeholder="请输入正整数"
        style="width: 120px; margin: 0 10px"
        @blur="onBlurCheckNumber(multiCommentDeleteCountRef)"
      />
      <span>条</span>
    </div>
    <!-- <div class="line-divider" />
    <div class="line-container" style="margin-top: 20px">
      <div class="divider-tag" />
      <div :class="['line-container-title']">适用范围</div>
      <span class="multiple-select-channel-text">（多选）</span>
    </div>
    <div class="line-check-box-container">
      <el-checkbox v-model="firstLevelCommentChecked" :disabled="isDisableEdit" label="一级评论" />
      <el-checkbox v-model="subLevelCommentChecked" :disabled="isDisableEdit" label="子级评论" />
    </div> -->
    <div class="line-divider" />
    <div class="line-container" style="margin-top: 20px">
      <div class="divider-tag" />
      <div :class="['line-container-title']">适用频道</div>
      <span class="multiple-select-channel-text">（多选）</span>
      <el-checkbox
        v-model="allChannelsChecked"
        :disabled="isDisableEdit"
        label="一键勾选"
        style="margin-left: 10px"
      />
    </div>
    <div class="line-container" style="margin-top: 20px">
      <el-row :gutter="5" class="channel-container">
        <el-col v-for="(item, index) in channels" :key="index" :span="4" class="channel-col">
          <ChannelTag v-model="item.binding" :text="item.name" :disabled="isDisableEdit" />
        </el-col>
      </el-row>
    </div>
    <div v-auth="'governance:create'" class="save-container">
      <el-button
        size="large"
        style="min-width: 120px; height: 32px"
        type="primary"
        :disabled="isDisableEdit"
        @click="onSaveClick"
        >{{ isDisableEdit ? '自动治理开启，不支持编辑' : '保存' }}</el-button
      >
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #0b82fd;
  border-color: #0b82fd;
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #0b82fd;
}
:deep(.el-checkbox__input.is-disabled + span.el-checkbox__label) {
  color: #a1a1a1;
}
:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #d3d3d3;
  border-color: #c0c4cc;
}

:deep(.el-button) {
  background-color: #0b82fd;
}

:deep(.el-button.is-disabled) {
  background-color: #d3d3d3; /* 设置禁用状态下的背景颜色 */
  border-color: #b5b5b5; /* 设置禁用状态下的边框颜色 */
  color: #a1a1a1; /* 设置禁用状态下的文字颜色 */
}

.channel-col {
  margin-bottom: 15px;
}

.line-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  margin-top: 20px;
}
.divider-tag {
  width: 4px;
  height: 14px;
  background: linear-gradient(180deg, #1ab8ff 0%, #0b82fd 100%);
  border-radius: 3px;
  margin-right: 6px;
}
.required-label {
  position: relative;
  display: inline-block;
}

.line-container-title {
  font-weight: 500;
  font-size: 14px;
  color: #333333;
}

.required-label::after {
  content: '*';
  color: red;
  position: absolute;
  top: 0;
  right: -10px;
  font-size: 16px;
  line-height: 1;
}

.rule-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
}

.name-input {
  width: 348px;
  margin-top: 14px;
}

.line-divider {
  width: 100%;
  height: 1px;
  border: 1px solid #efefef;
  margin-top: 14px;
}

.line-check-box-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20px;
}

.text-filter-tag-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-left: 6px;
  margin-right: 6px;
}

.text-links-tag-container {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  margin-left: 6px;
  margin-right: 6px;
}

.filter-button-add {
  width: 66px;
  height: 32px;
  border-style: dashed; /* 设置边框为虚线 */
  border-color: #0b82fd; /* 设置边框颜色为element-plus的主色蓝色 */
  border-width: 1px;
  color: #0b82fd; /* 设置文字颜色为蓝色 */
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  .plus-icon {
    width: 12px;
    height: 12px;
    margin-right: 2px;
  }
}

.filter-button-add-disable {
  border-color: #c0c4cc;
  cursor: not-allowed;
  color: #bdbdbd;
}

.line-behavior-rule {
  margin-top: 40px;
}
.multiple-select-channel-text {
  font-size: 12px;
  color: #999999;
}

.save-container {
  align-self: center;
  position: absolute;
  bottom: 40px;
}

.channel-container {
  width: 900px;
}

.text-hint {
  font-size: 14px;
  color: #999;
}

.custom-checkbox_-label {
  color: #a1a1a1;
  font-size: 14px;
  font-weight: 500;
  font-family: Lato, 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #0b82fd;
}

.no-checkbox {
  :deep(.el-checkbox__input) {
    display: none;
  }

  :deep(.el-checkbox__label) {
    padding-left: 0;
  }
}

.checkbox-1 {
  margin-right: 0px;
}
</style>
