<template>
  <div class="operation-log">
    <div class="header">
      <span class="log-title">操作日志</span>
      <el-button class="close-button" icon="Close" @click="closeDrawer" />
    </div>
    <el-table :data="logData" style="width: 100%">
      <el-table-column prop="created_at" label="操作时间" width="180">
        <template #default="scope">
          {{ formatTimestamp(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="operation_item" label="操作事项" />
      <el-table-column prop="operation_desc" label="事项说明" />
      <el-table-column prop="operation_person" label="操作人" width="120" />
    </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        @current-change="handleCurrentChange"
        layout="prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElPagination, ElButton } from 'element-plus'
import { requests } from '@/api/secureCenter/autoGovernance.js'
import { useDrawer } from '@/hooks/useDrawer.js'
import dayjs from 'dayjs'
const { closeDrawer } = useDrawer()
const logData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const props = defineProps({
  ruleId: {
    type: String,
    required: true
  }
})

const formatTimestamp = timestamp => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const fetchLogData = async (page, size) => {
  try {
    const response = await requests.operationLogApi(props.ruleId, page, size)
    logData.value = response.data.list.records
    total.value = response.data.list.total
  } catch (error) {
    console.error('获取操作日志失败：', error)
  }
}

const handleCurrentChange = newPage => {
  currentPage.value = newPage
  fetchLogData(newPage, pageSize.value)
}

const handleSizeChange = newSize => {
  pageSize.value = newSize
  fetchLogData(currentPage.value, newSize)
}

onMounted(() => {
  fetchLogData(currentPage.value, pageSize.value)
})

// 定义 emit 以便可以向父组件发送事件
const emit = defineEmits(['close'])
</script>

<style lang="scss" scoped>
.operation-log {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 44px;

  .log-title {
    color: #333333;
    font-size: 20px;

  }
}

.close-button {
  padding: 8px;
  font-size: 20px;
  color: #333333;
  border: none; /* 移除边框 */
  background: none; /* 移除背景 */
}

.pagination-container {
  margin-top: auto;
  padding-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
