<template>
  <AbsoluteContainer>
    <page-main>
      <div class="container">
        <div class="left">
          <div v-auth="'governance:create'" class="rule-button-add" @click="onAddRuleClick">
            <img class="plus-icon" :src="Plus" />
            <span>添加规则</span>
          </div>
          <div class="rule-list">
            <div
              v-for="(item, index) in ruleList"
              :key="index"
              class="group-button"
              :class="{ 'group-button-selected': selectedRule === item }"
              @click="onRuleItemClick(item)"
            >
              <span>{{ item.rule_name }}</span>
              <div class="rule-container-right">
                <div
                  v-auth="'governance:delete'"
                  class="rule-right-switch-container"
                  @click="onSwitchChange(item)"
                  @click.stop
                >
                  <RuleSwitch :value="item.used" />
                </div>
                <div class="rule-button-icon">
                  <Delete
                    v-auth="'governance:delete'"
                    v-if="shouldShowDeleteIcon(item)"
                    style="width: 14px; height: 14px"
                    @click.stop="onDeleteClick(item)"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="operation-log" v-auth="'governance:log'">
            <div class="operation-log-divider"></div>
            <div class="log-content">
              <img class="operation-log-icon" :src="Icon" />
              <div class="operation-log-title" @click="onOperationLogClick">操作日志</div>
            </div>
          </div>
        </div>
        <div class="page-divider"></div>
        <div class="right">
          <RuleEditComponent
            v-if="showEditView"
            :rule="selectedRule"
            @on-rule-save-success="onRuleSaveSuccess"
          />
        </div>
      </div>
    </page-main>
  </AbsoluteContainer>
</template>

<script setup>
import Plus from '@/assets/images/plus.png'
import { nextTick, onMounted, ref } from 'vue'
import RuleEditComponent from '@/views/safeCenter/autoGovernance/components/RuleEditComponent.vue'
import { ElDialog, ElMessage, ElMessageBox } from 'element-plus'
import { requests } from '@/api/secureCenter/autoGovernance.js'
import RuleSwitch from '@/views/safeCenter/autoGovernance/components/RuleSwitch.vue'
import { useDrawer } from '@/hooks/useDrawer'
import OperationLog from '@/views/safeCenter/autoGovernance/components/OperationLog.vue'
import Icon from '@/assets/images/log_auto_govern_log.png'
const { openDrawer } = useDrawer()
const selectedRule = ref({})
const showEditView = ref(true)

const shouldShowDeleteIcon = item => {
  return !item.used
}

const getRuleList = () => {
  requests
    .ruleListApi()
    .then(res => {
      ruleList.value = res.data.rule_list
    })
    .catch(err => {
      console.error(err)
    })
}

onMounted(() => {
  requests
    .ruleListApi()
    .then(res => {
      ruleList.value = res.data.rule_list
      if (ruleList.value.length > 0) {
        selectedRule.value = ruleList.value[0]
        refreshEditView()
      }
    })
    .catch(err => {
      console.error(err)
    })
})

const ruleList = ref([])

const refreshEditView = () => {
  showEditView.value = false
  nextTick(() => {
    showEditView.value = true
  })
}

const onAddRuleClick = () => {
  console.log('onAddRuleClick')
  if (Object.keys(selectedRule.value).length === 0) {
    ElMessage.error('请先保存当前规则')
    return
  }
  selectedRule.value = {}
  refreshEditView()
}

const onSwitchChange = async item => {
  console.log('onSwitchChange', item)
  if (item.used) {
    ElMessageBox.confirm(`确定关闭【${item.rule_name}】？`, '关闭规则', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        switchRule(item)
      })
      .catch(e => {
        console.log(e)
      })
  } else {
    switchRule(item)
  }
}

const switchRule = async item => {
  try {
    await requests.enableRuleApi(item.id, item.used ? 0 : 1)
  } catch (e) {
    console.error(e)
  }
  requests
    .ruleListApi()
    .then(res => {
      ruleList.value = res.data.rule_list
      selectedRule.value = ruleList.value.find(rule => item.id === rule.id)
      refreshEditView()
    })
    .catch(err => {
      console.error(err)
    })
}

const onRuleItemClick = item => {
  console.log('onRuleItemClick', item)
  selectedRule.value = item
  refreshEditView()
}

const onRuleSaveSuccess = rule => {
  console.log('onRuleSaveSuccess')
  requests
    .ruleListApi()
    .then(res => {
      ruleList.value = res.data.rule_list
      selectedRule.value = ruleList.value.find(item => item.id === rule.id)
      refreshEditView()
    })
    .catch(err => {
      console.error(err)
    })
}

const onDeleteClick = async item => {
  console.log('onDeleteClick', item)
  ElMessageBox.confirm(`确定删除【${item.rule_name}】？`, '删除规则', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'error'
  })
    .then(() => {
      deleteRule(item)
    })
    .catch(e => {
      console.log(e)
    })
}

const deleteRule = async item => {
  await requests.deleteRuleApi(item.id)
  requests
    .ruleListApi()
    .then(res => {
      ruleList.value = res.data.rule_list
      if (ruleList.value.length > 0) {
        selectedRule.value = ruleList.value[0]
        refreshEditView()
      } else {
        selectedRule.value = {}
        refreshEditView()
      }
    })
    .catch(err => {
      console.error(err)
    })
}

const onOperationLogClick = () => {
  openDrawer(OperationLog, '650px', { })
}
</script>

<style lang="scss" scoped>

.page {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .left {
    display: flex;
    flex-direction: column;
    justify-content: start;
    gap: 10px;
    height: 100%;
    overflow-y: auto;
    align-items: center;
    font-size: 14px;
    padding-top: 15px;
    padding-bottom: 15px;

    .rule-button-add {
      margin-left: 15px;
      margin-right: 15px;
      width: 250px;
      height: 32px;
      border-style: dashed;
      /* 设置边框为虚线 */
      border-color: #0b82fd;
      /* 设置边框颜色为element-plus的主色蓝色 */
      border-radius: 6px;
      /* 设置圆角 */
      border-width: 1px;
      color: #0b82fd;
      /* 设置文字颜色为蓝色 */
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .plus-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }

    .rule-list {
      display: flex;
      flex-direction: column;
      flex: 1;
      gap: 10px;
      overflow-y: auto;
      height: 100%;

      .group-button {
        width: 250px;
        height: 32px;
        cursor: pointer;
        /* 设置鼠标样式为手型 */
        display: flex;
        /* 设置为内联块元素 */
        justify-content: space-between;
        align-items: center;
        text-align: center;
        /* 文字居中对齐 */
        line-height: 32px;
        /* 设置行高与高度一致，实现垂直居中 */
        padding: 0 15px;
        /* 设置内边距 */
        padding-right: 4px;
        border-radius: 4px;
        /* 设置圆角 */
        background-color: #f8f8f8;
        color: #333333;
        transition:
          background-color 0.3s,
          border-color 0.3s,
          color 0.3s;
        /* 设置过渡效果 */

        .rule-container-right {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 4px;

          .rule-right-switch-container {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .rule-button-icon {
            width: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            visibility: hidden; // 初始隐藏
          }
        }
      }

      .group-button:hover {
        background-color: #cee6ff;
        /* 鼠标悬停时背景颜色变化为浅蓝色 */
        color: #0b82fd;

        /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
        .rule-button-icon {
          visibility: visible; // 鼠标悬停时显示
        }
      }

      .group-button-selected {
        background-color: #cee6ff;
        /* 鼠标悬停时背景颜色变化为浅蓝色 */
        color: #0b82fd;

        /* 鼠标悬停时文字颜色变化为element-plus的主色蓝色 */
        .rule-button-icon {
          visibility: visible; // 鼠标悬停时显示
        }
      }
    }
  }

  .operation-log {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .operation-log-divider {
      width: 100%;
      height: 1px;
      background-color: #efefef;
    }

    .log-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      margin-bottom: 2px;
      gap: 4px;
      cursor: pointer;

      .operation-log-icon {
        width: 14px;
        height: 14px;
      }

      .operation-log-title {
        font-size: 14px;
        color: #333333;
      }
    }
  }

  .page-divider {
    width: 1px;
    background-color: #efefef;
    height: 100%;
  }

  .right {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: scroll;
    margin-bottom: 80px;
  }
}

.page-main {
  display: flex;
  flex-direction: column;
  padding: 0 !important;
}
</style>
