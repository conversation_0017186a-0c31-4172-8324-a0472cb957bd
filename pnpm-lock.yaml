lockfileVersion: 5.3

specifiers:
  '@element-plus/icons': ^0.0.11
  '@tinymce/tinymce-vue': ^4.0.5
  '@vitejs/plugin-vue': ^1.10.0
  '@vue/compiler-sfc': ^3.2.22
  axios: ^0.24.0
  dayjs: ^1.10.7
  element-plus: ^1.2.0-beta.3
  eslint: ^8.3.0
  eslint-plugin-vue: ^8.1.1
  hotkeys-js: ^3.8.7
  http-server: ^14.0.0
  husky: ^7.0.4
  js-cookie: ^3.0.1
  lint-staged: ^12.1.2
  mavon-editor: ^3.0.0-beta
  mitt: ^3.0.0
  mockjs: ^1.1.0
  naming-style: ^1.0.1
  nprogress: ^0.2.0
  path-browserify: ^1.0.1
  path-to-regexp: ^6.2.0
  plop: ^2.7.6
  postcss-html: ^1.3.0
  postcss-scss: ^4.0.2
  qs: ^6.10.1
  sass: ^1.43.5
  screenfull: ^6.0.0
  stylelint: ^14.1.0
  stylelint-config-html: ^1.0.0
  stylelint-config-recommended: ^6.0.0
  stylelint-config-recommended-scss: ^5.0.2
  stylelint-config-recommended-vue: ^1.0.0
  stylelint-config-standard: ^24.0.0
  stylelint-scss: ^4.0.0
  svgo: ^2.8.0
  tinymce: ^5.10.2
  unplugin-auto-import: ^0.4.18
  unplugin-vue-components: ^0.17.2
  vite: ^2.6.14
  vite-plugin-banner: ^0.1.3
  vite-plugin-compression: ^0.3.6
  vite-plugin-html: ^2.1.1
  vite-plugin-mock: ^2.9.6
  vite-plugin-restart: ^0.0.2
  vite-plugin-spritesmith: ^0.1.1
  vite-plugin-svg-icons: ^1.0.5
  vite-plugin-vue-setup-extend: ^0.1.0
  vue: ^3.2.22
  vue-eslint-parser: ^8.0.1
  vue-router: ^4.0.12
  vuex: ^4.0.2

dependencies:
  '@element-plus/icons': 0.0.11
  '@tinymce/tinymce-vue': 4.0.5_vue@3.2.22
  axios: 0.24.0
  dayjs: 1.10.7
  element-plus: 1.2.0-beta.3_vue@3.2.22
  hotkeys-js: 3.8.7
  js-cookie: 3.0.1
  mavon-editor: 3.0.0-beta
  mitt: 3.0.0
  mockjs: 1.1.0
  naming-style: 1.0.1
  nprogress: 0.2.0
  path-browserify: 1.0.1
  path-to-regexp: 6.2.0
  qs: 6.10.1
  screenfull: 6.0.0
  tinymce: 5.10.2
  vue: 3.2.22
  vue-router: 4.0.12_vue@3.2.22
  vuex: 4.0.2_vue@3.2.22

devDependencies:
  '@vitejs/plugin-vue': 1.10.0_vite@2.6.14
  '@vue/compiler-sfc': 3.2.22
  eslint: 8.3.0
  eslint-plugin-vue: 8.1.1_eslint@8.3.0
  http-server: 14.0.0
  husky: 7.0.4
  lint-staged: 12.1.2
  plop: 2.7.6
  postcss-html: 1.3.0
  postcss-scss: 4.0.2
  sass: 1.43.5
  stylelint: 14.1.0
  stylelint-config-html: 1.0.0_c1fe332c2f0bd1acbb28be685cc2d480
  stylelint-config-recommended: 6.0.0_stylelint@14.1.0
  stylelint-config-recommended-scss: 5.0.2_stylelint@14.1.0
  stylelint-config-recommended-vue: 1.0.0_c1fe332c2f0bd1acbb28be685cc2d480
  stylelint-config-standard: 24.0.0_stylelint@14.1.0
  stylelint-scss: 4.0.0_stylelint@14.1.0
  svgo: 2.8.0
  unplugin-auto-import: 0.4.18_vite@2.6.14
  unplugin-vue-components: 0.17.2_vite@2.6.14+vue@3.2.22
  vite: 2.6.14_sass@1.43.5
  vite-plugin-banner: 0.1.3
  vite-plugin-compression: 0.3.6_vite@2.6.14
  vite-plugin-html: 2.1.1_vite@2.6.14
  vite-plugin-mock: 2.9.6_mockjs@1.1.0+vite@2.6.14
  vite-plugin-restart: 0.0.2_vite@2.6.14
  vite-plugin-spritesmith: 0.1.1
  vite-plugin-svg-icons: 1.0.5_vite@2.6.14
  vite-plugin-vue-setup-extend: 0.1.0_vite@2.6.14
  vue-eslint-parser: 8.0.1_eslint@8.3.0

packages:

  /@antfu/utils/0.3.0:
    resolution: {integrity: sha1-YwbEO1Kog72Olz4+2N1kJIQYvMQ=, tarball: '@antfu/utils/download/@antfu/utils-0.3.0.tgz'}
    dependencies:
      '@types/throttle-debounce': 2.1.0
    dev: true

  /@babel/code-frame/7.15.8:
    resolution: {integrity: sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=, tarball: '@babel/code-frame/download/@babel/code-frame-7.15.8.tgz'}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.14.5
    dev: true

  /@babel/helper-validator-identifier/7.15.7:
    resolution: {integrity: sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=, tarball: '@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz'}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/highlight/7.14.5:
    resolution: {integrity: sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=, tarball: '@babel/highlight/download/@babel/highlight-7.14.5.tgz'}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.15.7
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser/7.15.8:
    resolution: {integrity: sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=, tarball: '@babel/parser/download/@babel/parser-7.15.8.tgz'}
    engines: {node: '>=6.0.0'}
    hasBin: true

  /@babel/runtime-corejs3/7.15.4:
    resolution: {integrity: sha1-QDE5ryYrmm6Pm6BKb9zr+N5pK/E=, tarball: '@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.15.4.tgz'}
    engines: {node: '>=6.9.0'}
    dependencies:
      core-js-pure: 3.18.3
      regenerator-runtime: 0.13.9
    dev: true

  /@element-plus/icons/0.0.11:
    resolution: {integrity: sha1-mxh8ACd0VIuRGFDRf6X8L5pRX1c=, tarball: '@element-plus/icons/download/@element-plus/icons-0.0.11.tgz'}
    dev: false

  /@eslint/eslintrc/1.0.4:
    resolution: {integrity: sha1-3+D/e6JwhI0Qxa3QcV4ElkwDSzE=, tarball: '@eslint/eslintrc/download/@eslint/eslintrc-1.0.4.tgz'}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.3.2
      espree: 9.1.0
      globals: 13.11.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.0.4
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/config-array/0.6.0:
    resolution: {integrity: sha1-tWIf2zsyMJ0tFldUVsvCd/qPAho=, tarball: '@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.6.0.tgz'}
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': 1.2.0
      debug: 4.3.2
      minimatch: 3.0.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/object-schema/1.2.0:
    resolution: {integrity: sha1-h956+cIxgm/daKxyWPd8Qp4OX88=, tarball: '@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.0.tgz'}
    dev: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: '@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz'}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: '@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz'}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: '@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz'}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.13.0
    dev: true

  /@popperjs/core/2.10.2:
    resolution: {integrity: sha1-B5jAM1Hw3qGlpMq93yalWny+5ZA=, tarball: '@popperjs/core/download/@popperjs/core-2.10.2.tgz'}
    dev: false

  /@rollup/plugin-node-resolve/13.0.5:
    resolution: {integrity: sha1-AWq+WHlqT/VE1r6seBiSHj03d/w=, tarball: '@rollup/plugin-node-resolve/download/@rollup/plugin-node-resolve-13.0.5.tgz'}
    engines: {node: '>= 10.0.0'}
    peerDependencies:
      rollup: ^2.42.0
    dependencies:
      '@rollup/pluginutils': 3.1.0
      '@types/resolve': 1.17.1
      builtin-modules: 3.2.0
      deepmerge: 4.2.2
      is-module: 1.0.0
      resolve: 1.20.0
    dev: true

  /@rollup/pluginutils/3.1.0:
    resolution: {integrity: sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=, tarball: '@rollup/pluginutils/download/@rollup/pluginutils-3.1.0.tgz'}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0
    dependencies:
      '@types/estree': 0.0.39
      estree-walker: 1.0.1
      picomatch: 2.3.0
    dev: true

  /@rollup/pluginutils/4.1.1:
    resolution: {integrity: sha1-HU2obdTt7RVlalfZM/2iuaCNR+w=, tarball: '@rollup/pluginutils/download/@rollup/pluginutils-4.1.1.tgz'}
    engines: {node: '>= 8.0.0'}
    dependencies:
      estree-walker: 2.0.2
      picomatch: 2.3.0
    dev: true

  /@tinymce/tinymce-vue/4.0.5_vue@3.2.22:
    resolution: {integrity: sha512-PutMhPVgR+NPeu20pvTmFBY+3zKwTJqK0JPDb6QuK8u1g6Av4RaiUVS58Rft6mhoxVE99tIofdr69wOH/X0swA==, tarball: '@tinymce/tinymce-vue/download/@tinymce/tinymce-vue-4.0.5.tgz'}
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      tinymce: 5.10.2
      vue: 3.2.22
    dev: false

  /@trysound/sax/0.2.0:
    resolution: {integrity: sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=, tarball: '@trysound/sax/download/@trysound/sax-0.2.0.tgz'}
    engines: {node: '>=10.13.0'}
    dev: true

  /@types/estree/0.0.39:
    resolution: {integrity: sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=, tarball: '@types/estree/download/@types/estree-0.0.39.tgz'}
    dev: true

  /@types/fined/1.1.3:
    resolution: {integrity: sha1-g/A+jwqNNnPfyvsY/ONXH2JQ4bw=, tarball: '@types/fined/download/@types/fined-1.1.3.tgz'}
    dev: true

  /@types/glob/7.2.0:
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: '@types/glob/download/@types/glob-7.2.0.tgz?cache=0&sync_timestamp=1634887666871&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fglob%2Fdownload%2F%40types%2Fglob-7.2.0.tgz'}
    dependencies:
      '@types/minimatch': 3.0.5
      '@types/node': 16.11.4
    dev: true

  /@types/inquirer/6.5.0:
    resolution: {integrity: sha1-uDsL8wuIuL5yRtQOUdMv6dEOCb4=, tarball: '@types/inquirer/download/@types/inquirer-6.5.0.tgz?cache=0&sync_timestamp=1634183876463&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Finquirer%2Fdownload%2F%40types%2Finquirer-6.5.0.tgz'}
    dependencies:
      '@types/through': 0.0.30
      rxjs: 6.6.7
    dev: true

  /@types/interpret/1.1.1:
    resolution: {integrity: sha1-sb+FsEIOJBS5ic4jdlitINwDcZs=, tarball: '@types/interpret/download/@types/interpret-1.1.1.tgz'}
    dependencies:
      '@types/node': 16.10.9
    dev: true

  /@types/liftoff/2.5.1:
    resolution: {integrity: sha1-LrTB+G6dXuhVceVtsAhLJq8SnO0=, tarball: '@types/liftoff/download/@types/liftoff-2.5.1.tgz'}
    dependencies:
      '@types/fined': 1.1.3
      '@types/interpret': 1.1.1
      '@types/node': 16.10.9
    dev: true

  /@types/minimatch/3.0.5:
    resolution: {integrity: sha1-EAHMXmo3BLg8I2An538vWOoBD0A=, tarball: '@types/minimatch/download/@types/minimatch-3.0.5.tgz'}
    dev: true

  /@types/minimist/1.2.2:
    resolution: {integrity: sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=, tarball: '@types/minimist/download/@types/minimist-1.2.2.tgz'}
    dev: true

  /@types/mockjs/1.0.4:
    resolution: {integrity: sha1-5waVHV4ztPCku3Ox+LEk4m8IHeA=, tarball: '@types/mockjs/download/@types/mockjs-1.0.4.tgz'}
    dev: true

  /@types/node/16.10.9:
    resolution: {integrity: sha1-jxzdUXly92o7koKY9MB0fNb+8lo=, tarball: '@types/node/download/@types/node-16.10.9.tgz'}
    dev: true

  /@types/node/16.11.4:
    resolution: {integrity: sha1-kHcRJIItZmOBT3wcm0WmZU2P2WQ=, tarball: '@types/node/download/@types/node-16.11.4.tgz'}
    dev: true

  /@types/normalize-package-data/2.4.1:
    resolution: {integrity: sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=, tarball: '@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz'}
    dev: true

  /@types/parse-json/4.0.0:
    resolution: {integrity: sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=, tarball: '@types/parse-json/download/@types/parse-json-4.0.0.tgz'}
    dev: true

  /@types/resolve/1.17.1:
    resolution: {integrity: sha1-Ov1q2JZ8d+Q3bFmKgt3Vj0bsRdY=, tarball: '@types/resolve/download/@types/resolve-1.17.1.tgz'}
    dependencies:
      '@types/node': 16.10.9
    dev: true

  /@types/svgo/2.6.0:
    resolution: {integrity: sha1-8MUO7ShkIvY7TjNG2KMJsOpn1Yo=, tarball: '@types/svgo/download/@types/svgo-2.6.0.tgz'}
    dependencies:
      '@types/node': 16.10.9
    dev: true

  /@types/throttle-debounce/2.1.0:
    resolution: {integrity: sha1-HD32JL/Eti+ZLTASuExW1B6rN3Y=, tarball: '@types/throttle-debounce/download/@types/throttle-debounce-2.1.0.tgz'}
    dev: true

  /@types/through/0.0.30:
    resolution: {integrity: sha1-4OQs536Je9aurW9upirrE1uKOJU=, tarball: '@types/through/download/@types/through-0.0.30.tgz'}
    dependencies:
      '@types/node': 16.11.4
    dev: true

  /@vitejs/plugin-vue/1.10.0_vite@2.6.14:
    resolution: {integrity: sha512-XkSN7lduhQ3z/WMXv2spqt9TCS0znCjnvIGmbud2bxViWWWR4JWXE+x/cQZ/klR0Ug4Ts9eubL7LXAysON5Uvg==, tarball: '@vitejs/plugin-vue/download/@vitejs/plugin-vue-1.10.0.tgz'}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      vite: ^2.5.10
    dependencies:
      vite: 2.6.14_sass@1.43.5
    dev: true

  /@vue/compiler-core/3.2.22:
    resolution: {integrity: sha512-uAkovrVeTcjzpiM4ECmVaMrv/bjdgAaLzvjcGqQPBEyUrcqsCgccT9fHJ/+hWVGhyMahmBwLqcn4guULNx7sdw==, tarball: '@vue/compiler-core/download/@vue/compiler-core-3.2.22.tgz'}
    dependencies:
      '@babel/parser': 7.15.8
      '@vue/shared': 3.2.22
      estree-walker: 2.0.2
      source-map: 0.6.1

  /@vue/compiler-dom/3.2.22:
    resolution: {integrity: sha512-VZdsw/VuO1ODs8K7NQwnMQzKITDkIFlYYC03SVnunuf6eNRxBPEonSyqbWNoo6qNaHAEBTG6VVcZC5xC9bAx1g==, tarball: '@vue/compiler-dom/download/@vue/compiler-dom-3.2.22.tgz'}
    dependencies:
      '@vue/compiler-core': 3.2.22
      '@vue/shared': 3.2.22

  /@vue/compiler-sfc/3.2.22:
    resolution: {integrity: sha512-tWRQ5ge1tsTDhUwHgueicKJ8rYm6WUVAPTaIpFW3GSwZKcOEJ2rXdfkHFShNVGupeRALz2ET2H84OL0GeRxY0A==, tarball: '@vue/compiler-sfc/download/@vue/compiler-sfc-3.2.22.tgz'}
    dependencies:
      '@babel/parser': 7.15.8
      '@vue/compiler-core': 3.2.22
      '@vue/compiler-dom': 3.2.22
      '@vue/compiler-ssr': 3.2.22
      '@vue/ref-transform': 3.2.22
      '@vue/shared': 3.2.22
      estree-walker: 2.0.2
      magic-string: 0.25.7
      postcss: 8.3.9
      source-map: 0.6.1

  /@vue/compiler-ssr/3.2.22:
    resolution: {integrity: sha512-Cl6aoLJtXzzBkk1sKod8S0WBJLts3+ugVC91d22gGpbkw/64WnF12tOZi7Rg54PPLi1NovqyNWPsLH/SAFcu+w==, tarball: '@vue/compiler-ssr/download/@vue/compiler-ssr-3.2.22.tgz'}
    dependencies:
      '@vue/compiler-dom': 3.2.22
      '@vue/shared': 3.2.22

  /@vue/devtools-api/6.0.0-beta.19:
    resolution: {integrity: sha1-+OiAWdqkJFFZkkJqDH6lzeB+mb8=, tarball: '@vue/devtools-api/download/@vue/devtools-api-6.0.0-beta.19.tgz'}
    dev: false

  /@vue/reactivity/3.2.22:
    resolution: {integrity: sha512-xNkLAItjI0xB+lFeDgKCrSItmrHTaAzSnt8LmdSCPQnDyarmzbi/u4ESQnckWvlL7lSRKiEaOvblaNyqAa7OnQ==, tarball: '@vue/reactivity/download/@vue/reactivity-3.2.22.tgz'}
    dependencies:
      '@vue/shared': 3.2.22
    dev: false

  /@vue/ref-transform/3.2.22:
    resolution: {integrity: sha512-qalVWbq5xWWxLZ0L9OroBg/JZhzavQuCcDXblfErxyDEH6Xc5gIJ4feo1SVCICFzhAUgLgQTdSFLpgjBawbFpw==, tarball: '@vue/ref-transform/download/@vue/ref-transform-3.2.22.tgz'}
    dependencies:
      '@babel/parser': 7.15.8
      '@vue/compiler-core': 3.2.22
      '@vue/shared': 3.2.22
      estree-walker: 2.0.2
      magic-string: 0.25.7

  /@vue/runtime-core/3.2.22:
    resolution: {integrity: sha512-e7WOC55wmHPvmoVUk9VBe/Z9k5bJfWJfVIlkUkiADJn0bOgQD29oh/GS14Kb3aEJXIHLI17Em6+HxNut1sIh7Q==, tarball: '@vue/runtime-core/download/@vue/runtime-core-3.2.22.tgz'}
    dependencies:
      '@vue/reactivity': 3.2.22
      '@vue/shared': 3.2.22
    dev: false

  /@vue/runtime-dom/3.2.22:
    resolution: {integrity: sha512-w7VHYJoliLRTLc5beN77wxuOjla4v9wr2FF22xpZFYBmH4U1V7HkYhoHc1BTuNghI15CXT1tNIMhibI1nrQgdw==, tarball: '@vue/runtime-dom/download/@vue/runtime-dom-3.2.22.tgz'}
    dependencies:
      '@vue/runtime-core': 3.2.22
      '@vue/shared': 3.2.22
      csstype: 2.6.18
    dev: false

  /@vue/server-renderer/3.2.22_vue@3.2.22:
    resolution: {integrity: sha512-jCwbQgKPXiXoH9VS9F7K+gyEvEMrjutannwEZD1R8fQ9szmOTqC+RRbIY3Uf2ibQjZtZ8DV9a4FjxICvd9zZlQ==, tarball: '@vue/server-renderer/download/@vue/server-renderer-3.2.22.tgz'}
    peerDependencies:
      vue: 3.2.22
    dependencies:
      '@vue/compiler-ssr': 3.2.22
      '@vue/shared': 3.2.22
      vue: 3.2.22
    dev: false

  /@vue/shared/3.2.22:
    resolution: {integrity: sha512-qWVav014mpjEtbWbEgl0q9pEyrrIySKum8UVYjwhC6njrKzknLZPvfuYdQyVbApsqr94tf/3dP4pCuZmmjdCWQ==, tarball: '@vue/shared/download/@vue/shared-3.2.22.tgz'}

  /@vueuse/core/6.7.5_vue@3.2.22:
    resolution: {integrity: sha1-w9jtushAiwe8KCUckop6SymigyU=, tarball: '@vueuse/core/download/@vueuse/core-6.7.5.tgz'}
    peerDependencies:
      '@vue/composition-api': ^1.1.0
      vue: ^2.6.0 || ^3.2.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      vue:
        optional: true
    dependencies:
      '@vueuse/shared': 6.7.5_vue@3.2.22
      vue: 3.2.22
      vue-demi: 0.12.1_vue@3.2.22
    dev: false

  /@vueuse/shared/6.7.5_vue@3.2.22:
    resolution: {integrity: sha1-CmBMBkc9tUReRt4Ysde7bZraS+k=, tarball: '@vueuse/shared/download/@vueuse/shared-6.7.5.tgz'}
    peerDependencies:
      '@vue/composition-api': ^1.1.0
      vue: ^2.6.0 || ^3.2.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      vue:
        optional: true
    dependencies:
      vue: 3.2.22
      vue-demi: 0.12.1_vue@3.2.22
    dev: false

  /acorn-jsx/5.3.2_acorn@8.6.0:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.6.0
    dev: true

  /acorn/8.6.0:
    resolution: {integrity: sha512-U1riIR+lBSNi3IbxtaHOIKdH8sLFv3NYfNv8sg7ZsNhcfl4HF2++BfqqrNAxoCLQW1iiylOj76ecnaUxz+z9yw==, tarball: acorn/download/acorn-8.6.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /aggregate-error/3.1.0:
    resolution: {integrity: sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=, tarball: aggregate-error/download/aggregate-error-3.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ajv/6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: ajv/download/ajv-6.12.6.tgz}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ajv/8.8.0:
    resolution: {integrity: sha512-L+cJ/+pkdICMueKR6wIx3VP2fjIx3yAhuvadUv/osv9yFD7OVZy442xFF+Oeu3ZvmhBGQzoF6mTSt+LUWBmGQg==, tarball: ajv/download/ajv-8.8.0.tgz}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1
    dev: true

  /ansi-colors/4.1.1:
    resolution: {integrity: sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=, tarball: ansi-colors/download/ansi-colors-4.1.1.tgz}
    engines: {node: '>=6'}
    dev: true

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=, tarball: ansi-regex/download/ansi-regex-2.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex/4.1.0:
    resolution: {integrity: sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=, tarball: ansi-regex/download/ansi-regex-4.1.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /ansi-regex/5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex/6.0.1:
    resolution: {integrity: sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo=, tarball: ansi-regex/download/ansi-regex-6.0.1.tgz}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=, tarball: ansi-styles/download/ansi-styles-2.2.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles/6.1.0:
    resolution: {integrity: sha1-hzE8ECuBGKvVc3GvqzRhi/c1DtM=, tarball: ansi-styles/download/ansi-styles-6.1.0.tgz}
    engines: {node: '>=12'}
    dev: true

  /anymatch/3.1.2:
    resolution: {integrity: sha1-wFV8CWrzLxBhmPT04qODU343hxY=, tarball: anymatch/download/anymatch-3.1.2.tgz}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.0
    dev: true

  /argparse/2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: argparse/download/argparse-2.0.1.tgz}
    dev: true

  /arr-diff/4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=, tarball: arr-diff/download/arr-diff-4.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /arr-flatten/1.1.0:
    resolution: {integrity: sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=, tarball: arr-flatten/download/arr-flatten-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /arr-union/3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=, tarball: arr-union/download/arr-union-3.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-each/1.0.1:
    resolution: {integrity: sha1-p5SvDAWrF1KEbudTofIRoFugxE8=, tarball: array-each/download/array-each-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-slice/1.1.0:
    resolution: {integrity: sha1-42jqFfibxwaff/uJrsOmx9SsItQ=, tarball: array-slice/download/array-slice-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-union/2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=, tarball: array-union/download/array-union-2.1.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /array-unique/0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=, tarball: array-unique/download/array-unique-0.3.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /arrify/1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=, tarball: arrify/download/arrify-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /asn1/0.2.4:
    resolution: {integrity: sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=, tarball: asn1/download/asn1-0.2.4.tgz}
    dependencies:
      safer-buffer: registry.nlark.com/safer-buffer/2.1.2
    dev: true

  /assert-plus/1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=, tarball: assert-plus/download/assert-plus-1.0.0.tgz}
    engines: {node: '>=0.8'}
    dev: true

  /assign-symbols/1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=, tarball: assign-symbols/download/assign-symbols-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /astral-regex/2.0.0:
    resolution: {integrity: sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=, tarball: astral-regex/download/astral-regex-2.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /async-validator/4.0.7:
    resolution: {integrity: sha1-A0oP0hA6ay6/AQ2nUYO+wpkkev4=, tarball: async-validator/download/async-validator-4.0.7.tgz}
    dev: false

  /asynckit/0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: asynckit/download/asynckit-0.4.0.tgz}
    dev: true

  /atob/2.1.2:
    resolution: {integrity: sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=, tarball: atob/download/atob-2.1.2.tgz}
    engines: {node: '>= 4.5.0'}
    hasBin: true
    dev: true

  /aws-sign2/0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=, tarball: aws-sign2/download/aws-sign2-0.7.0.tgz}
    dev: true

  /aws4/1.11.0:
    resolution: {integrity: sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=, tarball: aws4/download/aws4-1.11.0.tgz}
    dev: true

  /axios/0.24.0:
    resolution: {integrity: sha1-gE5voeS5xSiFAd2d/1anoJQNINY=, tarball: axios/download/axios-0.24.0.tgz?cache=0&sync_timestamp=1635214243043&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Faxios%2Fdownload%2Faxios-0.24.0.tgz}
    dependencies:
      follow-redirects: registry.nlark.com/follow-redirects/1.14.4
    transitivePeerDependencies:
      - debug
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: balanced-match/download/balanced-match-1.0.2.tgz}
    dev: true

  /balanced-match/2.0.0:
    resolution: {integrity: sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=, tarball: balanced-match/download/balanced-match-2.0.0.tgz}
    dev: true

  /base/0.11.2:
    resolution: {integrity: sha1-e95c7RRbbVUakNuH+DxVi060io8=, tarball: base/download/base-0.11.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.0
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1
    dev: true

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=, tarball: bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz}
    dependencies:
      tweetnacl: 0.14.5
    dev: true

  /big.js/5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=, tarball: big.js/download/big.js-5.2.2.tgz}
    dev: true

  /bin-pack/1.0.2:
    resolution: {integrity: sha1-wqAU7b8L7XCjKSBi7UZXe5YSBnk=, tarball: bin-pack/download/bin-pack-1.0.2.tgz}
    dev: true

  /binary-extensions/2.2.0:
    resolution: {integrity: sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=, tarball: binary-extensions/download/binary-extensions-2.2.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /bluebird/3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=, tarball: bluebird/download/bluebird-3.7.2.tgz?cache=0&sync_timestamp=1634135117892&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbluebird%2Fdownload%2Fbluebird-3.7.2.tgz}
    dev: true

  /boolbase/1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: boolbase/download/boolbase-1.0.0.tgz}
    dev: true

  /brace-expansion/1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: brace-expansion/download/brace-expansion-1.1.11.tgz}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /braces/2.3.2:
    resolution: {integrity: sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=, tarball: braces/download/braces-2.3.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    dev: true

  /braces/3.0.2:
    resolution: {integrity: sha1-NFThpGLujVmeI23zNs2epPiv4Qc=, tarball: braces/download/braces-3.0.2.tgz}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=, tarball: buffer-from/download/buffer-from-1.1.2.tgz}
    dev: true

  /builtin-modules/3.2.0:
    resolution: {integrity: sha1-RdXbmefuXmvE82LgCL+RerUEmIc=, tarball: builtin-modules/download/builtin-modules-3.2.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /builtins/4.0.0:
    resolution: {integrity: sha1-qDRUIN6CBo/cTWVZ0EVkA6j7GQU=, tarball: builtins/download/builtins-4.0.0.tgz}
    dependencies:
      semver: 7.3.5
    dev: true

  /cache-base/1.0.1:
    resolution: {integrity: sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=, tarball: cache-base/download/cache-base-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.0
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0
    dev: true

  /call-bind/1.0.2:
    resolution: {integrity: sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=, tarball: call-bind/download/call-bind-1.0.2.tgz}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.1.1

  /callsites/3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /camel-case/3.0.0:
    resolution: {integrity: sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=, tarball: camel-case/download/camel-case-3.0.0.tgz}
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3
    dev: true

  /camel-case/4.1.2:
    resolution: {integrity: sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=, tarball: camel-case/download/camel-case-4.1.2.tgz}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.3.1
    dev: true

  /camelcase-keys/6.2.2:
    resolution: {integrity: sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=, tarball: camelcase-keys/download/camelcase-keys-6.2.2.tgz}
    engines: {node: '>=8'}
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=, tarball: camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1632825560555&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz}
    engines: {node: '>=6'}
    dev: true

  /caseless/0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=, tarball: caseless/download/caseless-0.12.0.tgz}
    dev: true

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=, tarball: chalk/download/chalk-1.1.3.tgz?cache=0&sync_timestamp=1632883284634&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-1.1.3.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk/2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1632883284634&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk/4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1632883284634&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /change-case/3.1.0:
    resolution: {integrity: sha1-DmEbftyZUt8uhROye0LecmR90X4=, tarball: change-case/download/change-case-3.1.0.tgz}
    dependencies:
      camel-case: 3.0.0
      constant-case: 2.0.0
      dot-case: 2.1.1
      header-case: 1.0.1
      is-lower-case: 1.1.3
      is-upper-case: 1.1.2
      lower-case: 1.1.4
      lower-case-first: 1.0.2
      no-case: 2.3.2
      param-case: 2.1.1
      pascal-case: 2.0.1
      path-case: 2.1.1
      sentence-case: 2.1.1
      snake-case: 2.1.0
      swap-case: 1.1.2
      title-case: 2.1.1
      upper-case: 1.1.3
      upper-case-first: 1.1.2
    dev: true

  /chardet/0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=, tarball: chardet/download/chardet-0.7.0.tgz}
    dev: true

  /chokidar/3.5.2:
    resolution: {integrity: sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=, tarball: chokidar/download/chokidar-3.5.2.tgz}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /class-utils/0.3.6:
    resolution: {integrity: sha1-+TNprouafOAv1B+q0MqDAzGQxGM=, tarball: class-utils/download/class-utils-0.3.6.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2
    dev: true

  /clean-css/4.2.3:
    resolution: {integrity: sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g=, tarball: clean-css/download/clean-css-4.2.3.tgz}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: true

  /clean-stack/2.2.0:
    resolution: {integrity: sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=, tarball: clean-stack/download/clean-stack-2.2.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /cli-cursor/2.1.0:
    resolution: {integrity: sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=, tarball: cli-cursor/download/cli-cursor-2.1.0.tgz?cache=0&sync_timestamp=1632920766030&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-2.1.0.tgz}
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: 2.0.0
    dev: true

  /cli-cursor/3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=, tarball: cli-cursor/download/cli-cursor-3.1.0.tgz?cache=0&sync_timestamp=1632920766030&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-3.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-spinners/2.6.1:
    resolution: {integrity: sha1-rclU6+KBw3pjGb+kAebdJIj/tw0=, tarball: cli-spinners/download/cli-spinners-2.6.1.tgz}
    engines: {node: '>=6'}
    dev: true

  /cli-truncate/2.1.0:
    resolution: {integrity: sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=, tarball: cli-truncate/download/cli-truncate-2.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3
    dev: true

  /cli-truncate/3.1.0:
    resolution: {integrity: sha1-PyOrElNePXPoObtD5zyd5IfbE4k=, tarball: cli-truncate/download/cli-truncate-3.1.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      slice-ansi: 5.0.0
      string-width: 5.0.1
    dev: true

  /cli-width/3.0.0:
    resolution: {integrity: sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=, tarball: cli-width/download/cli-width-3.0.0.tgz}
    engines: {node: '>= 10'}
    dev: true

  /clone-regexp/2.2.0:
    resolution: {integrity: sha1-fWXgCIXNh5ZAXDWnN+eoa3Qp428=, tarball: clone-regexp/download/clone-regexp-2.2.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      is-regexp: 2.1.0
    dev: true

  /clone-stats/0.0.1:
    resolution: {integrity: sha1-uI+UqCzzi4eR1YBG6kAprYjKmdE=, tarball: clone-stats/download/clone-stats-0.0.1.tgz}
    dev: true

  /clone/1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=, tarball: clone/download/clone-1.0.4.tgz}
    engines: {node: '>=0.8'}
    dev: true

  /clone/2.1.2:
    resolution: {integrity: sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=, tarball: clone/download/clone-2.1.2.tgz}
    engines: {node: '>=0.8'}
    dev: true

  /collection-visit/1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=, tarball: collection-visit/download/collection-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1
    dev: true

  /color-convert/1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: color-convert/download/color-convert-1.9.3.tgz}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert/2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: color-name/download/color-name-1.1.3.tgz}
    dev: true

  /color-name/1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: color-name/download/color-name-1.1.4.tgz}
    dev: true

  /colorette/2.0.16:
    resolution: {integrity: sha1-cTua+E/bAAE58EVGvUqT9ipQhdo=, tarball: colorette/download/colorette-2.0.16.tgz}
    dev: true

  /combined-stream/1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: true

  /commander/2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=, tarball: commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634889569010&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz}

  /commander/4.1.1:
    resolution: {integrity: sha1-n9YCvZNilOnp70aj9NaWQESxgGg=, tarball: commander/download/commander-4.1.1.tgz}
    engines: {node: '>= 6'}
    dev: true

  /commander/7.2.0:
    resolution: {integrity: sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=, tarball: commander/download/commander-7.2.0.tgz}
    engines: {node: '>= 10'}
    dev: true

  /commander/8.2.0:
    resolution: {integrity: sha1-N/4r3jAdh9R6U63v+LWRXbE4HKg=, tarball: commander/download/commander-8.2.0.tgz}
    engines: {node: '>= 12'}
    dev: false

  /commander/8.3.0:
    resolution: {integrity: sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=, tarball: commander/download/commander-8.3.0.tgz}
    engines: {node: '>= 12'}
    dev: true

  /component-emitter/1.3.0:
    resolution: {integrity: sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=, tarball: component-emitter/download/component-emitter-1.3.0.tgz}
    dev: true

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: concat-map/download/concat-map-0.0.1.tgz}
    dev: true

  /concat-stream/1.5.2:
    resolution: {integrity: sha1-cIl4Yk2FavQaWnQd790mHadSwmY=, tarball: concat-stream/download/concat-stream-1.5.2.tgz}
    engines: {'0': node >= 0.8}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.0.6
      typedarray: 0.0.6
    dev: true

  /connect/3.7.0:
    resolution: {integrity: sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg=, tarball: connect/download/connect-3.7.0.tgz}
    engines: {node: '>= 0.10.0'}
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    dev: true

  /constant-case/2.0.0:
    resolution: {integrity: sha1-QXV2TTidP6nI7NKRhu1gBSQ7akY=, tarball: constant-case/download/constant-case-2.0.0.tgz}
    dependencies:
      snake-case: 2.1.0
      upper-case: 1.1.3
    dev: true

  /contentstream/1.0.0:
    resolution: {integrity: sha1-C9z6RtowRkqGzo+n7OVlQQ3G+aU=, tarball: contentstream/download/contentstream-1.0.0.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      readable-stream: 1.0.34
    dev: true

  /copy-descriptor/0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=, tarball: copy-descriptor/download/copy-descriptor-0.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /core-js-pure/3.18.3:
    resolution: {integrity: sha1-fu133M4URato/WhxWFZjPi+zuQw=, tarball: core-js-pure/download/core-js-pure-3.18.3.tgz}
    requiresBuild: true
    dev: true

  /core-util-is/1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=, tarball: core-util-is/download/core-util-is-1.0.2.tgz}
    dev: true

  /core-util-is/1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=, tarball: core-util-is/download/core-util-is-1.0.3.tgz}
    dev: true

  /cors/2.8.5:
    resolution: {integrity: sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=, tarball: cors/download/cors-2.8.5.tgz}
    engines: {node: '>= 0.10'}
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2
    dev: true

  /cosmiconfig/7.0.1:
    resolution: {integrity: sha1-cU11ZSLKzoZ4Z8y0R0xdAbuuXW0=, tarball: cosmiconfig/download/cosmiconfig-7.0.1.tgz}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /cross-spawn/7.0.3:
    resolution: {integrity: sha1-9zqFudXUHQRVUcF34ogtSshXKKY=, tarball: cross-spawn/download/cross-spawn-7.0.3.tgz?cache=0&sync_timestamp=1632883303690&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcross-spawn%2Fdownload%2Fcross-spawn-7.0.3.tgz}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /css-select/4.1.3:
    resolution: {integrity: sha1-pwRA9wMX8maRGK10/xBeZYSccGc=, tarball: css-select/download/css-select-4.1.3.tgz}
    dependencies:
      boolbase: 1.0.0
      css-what: 5.1.0
      domhandler: 4.2.2
      domutils: 2.8.0
      nth-check: 2.0.1
    dev: true

  /css-tree/1.1.3:
    resolution: {integrity: sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=, tarball: css-tree/download/css-tree-1.1.3.tgz}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: true

  /css-what/5.1.0:
    resolution: {integrity: sha1-P3tweq32M7r2LCzrhXm1RbtA9/4=, tarball: css-what/download/css-what-5.1.0.tgz}
    engines: {node: '>= 6'}
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: cssesc/download/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /cssfilter/0.0.10:
    resolution: {integrity: sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=, tarball: cssfilter/download/cssfilter-0.0.10.tgz}
    dev: false

  /csso/4.2.0:
    resolution: {integrity: sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=, tarball: csso/download/csso-4.2.0.tgz}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: true

  /csstype/2.6.18:
    resolution: {integrity: sha1-mAqLUwhfNK8xNBCvBk8r0kF4Qhg=, tarball: csstype/download/csstype-2.6.18.tgz}
    dev: false

  /cwise-compiler/1.1.3:
    resolution: {integrity: sha1-9NZnQQ6FDToxOn0tt7HlBbsDTMU=, tarball: cwise-compiler/download/cwise-compiler-1.1.3.tgz}
    dependencies:
      uniq: 1.0.1
    dev: true

  /dashdash/1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=, tarball: dashdash/download/dashdash-1.14.1.tgz}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /data-uri-to-buffer/0.0.3:
    resolution: {integrity: sha1-GK6XmmoMqZSwYlhTkW0mYruuCxo=, tarball: data-uri-to-buffer/download/data-uri-to-buffer-0.0.3.tgz}
    dev: true

  /dayjs/1.10.7:
    resolution: {integrity: sha1-LPX5Gt0oEWdIRAhmoKHSbzps5Gg=, tarball: dayjs/download/dayjs-1.10.7.tgz}
    dev: false

  /debug/2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: debug/download/debug-2.6.9.tgz}
    dependencies:
      ms: 2.0.0
    dev: true

  /debug/3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=, tarball: debug/download/debug-3.2.7.tgz}
    dependencies:
      ms: 2.1.3
    dev: true

  /debug/4.3.2:
    resolution: {integrity: sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=, tarball: debug/download/debug-4.3.2.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /debug/4.3.2_supports-color@9.0.2:
    resolution: {integrity: sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=, tarball: debug/download/debug-4.3.2.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 9.0.2
    dev: true

  /decamelize-keys/1.1.0:
    resolution: {integrity: sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=, tarball: decamelize-keys/download/decamelize-keys-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1
    dev: true

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=, tarball: decamelize/download/decamelize-1.2.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /decode-uri-component/0.2.0:
    resolution: {integrity: sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=, tarball: decode-uri-component/download/decode-uri-component-0.2.0.tgz}
    engines: {node: '>=0.10'}
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: deep-is/download/deep-is-0.1.4.tgz}
    dev: true

  /deepmerge/4.2.2:
    resolution: {integrity: sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=, tarball: deepmerge/download/deepmerge-4.2.2.tgz?cache=0&sync_timestamp=1632825560302&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdeepmerge%2Fdownload%2Fdeepmerge-4.2.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /defaults/1.0.3:
    resolution: {integrity: sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=, tarball: defaults/download/defaults-1.0.3.tgz}
    dependencies:
      clone: 1.0.4
    dev: true

  /define-property/0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=, tarball: define-property/download/define-property-0.2.5.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.6
    dev: true

  /define-property/1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=, tarball: define-property/download/define-property-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
    dev: true

  /define-property/2.0.2:
    resolution: {integrity: sha1-1Flono1lS6d+AqgX+HENcCyxbp0=, tarball: define-property/download/define-property-2.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
      isobject: 3.0.1
    dev: true

  /del/5.1.0:
    resolution: {integrity: sha1-2Uh8lONnQQ5u/ykl7ljAyEp1s6c=, tarball: del/download/del-5.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      globby: 10.0.2
      graceful-fs: 4.2.8
      is-glob: 4.0.3
      is-path-cwd: 2.2.0
      is-path-inside: 3.0.3
      p-map: 3.0.0
      rimraf: 3.0.2
      slash: 3.0.0
    dev: true

  /delayed-stream/1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}
    dev: true

  /detect-file/1.0.0:
    resolution: {integrity: sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=, tarball: detect-file/download/detect-file-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /dir-glob/3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=, tarball: dir-glob/download/dir-glob-3.0.1.tgz}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /doctrine/3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=, tarball: doctrine/download/doctrine-3.0.0.tgz}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-serializer/0.2.2:
    resolution: {integrity: sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=, tarball: dom-serializer/download/dom-serializer-0.2.2.tgz}
    dependencies:
      domelementtype: 2.2.0
      entities: 2.2.0
    dev: true

  /dom-serializer/1.3.2:
    resolution: {integrity: sha1-YgZDfTLO767HFhgDIwx6ILwbTZE=, tarball: dom-serializer/download/dom-serializer-1.3.2.tgz}
    dependencies:
      domelementtype: 2.2.0
      domhandler: 4.2.2
      entities: 2.2.0
    dev: true

  /domelementtype/1.3.1:
    resolution: {integrity: sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=, tarball: domelementtype/download/domelementtype-1.3.1.tgz}
    dev: true

  /domelementtype/2.2.0:
    resolution: {integrity: sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc=, tarball: domelementtype/download/domelementtype-2.2.0.tgz}
    dev: true

  /domhandler/2.4.2:
    resolution: {integrity: sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=, tarball: domhandler/download/domhandler-2.4.2.tgz}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /domhandler/4.2.2:
    resolution: {integrity: sha1-6CXXIdGahrjCAaNSZOImxnjudV8=, tarball: domhandler/download/domhandler-4.2.2.tgz}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.2.0
    dev: true

  /domutils/1.7.0:
    resolution: {integrity: sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=, tarball: domutils/download/domutils-1.7.0.tgz}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: true

  /domutils/2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=, tarball: domutils/download/domutils-2.8.0.tgz}
    dependencies:
      dom-serializer: 1.3.2
      domelementtype: 2.2.0
      domhandler: 4.2.2
    dev: true

  /dot-case/2.1.1:
    resolution: {integrity: sha1-NNzzf1Co6TwrO8qLt/uRVcfaO+4=, tarball: dot-case/download/dot-case-2.1.1.tgz}
    dependencies:
      no-case: 2.3.2
    dev: true

  /dot-case/3.0.4:
    resolution: {integrity: sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=, tarball: dot-case/download/dot-case-3.0.4.tgz}
    dependencies:
      no-case: 3.0.4
      tslib: 2.3.1
    dev: true

  /dotenv-expand/5.1.0:
    resolution: {integrity: sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=, tarball: dotenv-expand/download/dotenv-expand-5.1.0.tgz}
    dev: true

  /dotenv/10.0.0:
    resolution: {integrity: sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE=, tarball: dotenv/download/dotenv-10.0.0.tgz}
    engines: {node: '>=10'}
    dev: true

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=, tarball: ecc-jsbn/download/ecc-jsbn-0.1.2.tgz}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: registry.nlark.com/safer-buffer/2.1.2
    dev: true

  /ee-first/1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=, tarball: ee-first/download/ee-first-1.1.1.tgz}
    dev: true

  /ejs/3.1.6:
    resolution: {integrity: sha1-W/0KBol0O7UmizVQzO7rvBcCgio=, tarball: ejs/download/ejs-3.1.6.tgz?cache=0&sync_timestamp=1634183876835&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fejs%2Fdownload%2Fejs-3.1.6.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      jake: 10.8.2
    dev: true

  /element-plus/1.2.0-beta.3_vue@3.2.22:
    resolution: {integrity: sha512-vvUxR3uL2k9K+WCWz/98hq4uPQ6jvfGf0hfzWvao7pySJkNQq9DtRPC6/4/zUceA/J6Y2Yo3xSbR19JRCwurRw==, tarball: element-plus/download/element-plus-1.2.0-beta.3.tgz}
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@element-plus/icons': 0.0.11
      '@popperjs/core': 2.10.2
      '@vueuse/core': 6.7.5_vue@3.2.22
      async-validator: 4.0.7
      dayjs: 1.10.7
      lodash: 4.17.21
      memoize-one: 6.0.0
      normalize-wheel-es: 1.1.1
      vue: 3.2.22
    transitivePeerDependencies:
      - '@vue/composition-api'
    dev: false

  /emoji-regex/8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: emoji-regex/download/emoji-regex-8.0.0.tgz}
    dev: true

  /emoji-regex/9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: emoji-regex/download/emoji-regex-9.2.2.tgz}
    dev: true

  /emojis-list/3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=, tarball: emojis-list/download/emojis-list-3.0.0.tgz}
    engines: {node: '>= 4'}
    dev: true

  /encodeurl/1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=, tarball: encodeurl/download/encodeurl-1.0.2.tgz}
    engines: {node: '>= 0.8'}
    dev: true

  /enquirer/2.3.6:
    resolution: {integrity: sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=, tarball: enquirer/download/enquirer-2.3.6.tgz}
    engines: {node: '>=8.6'}
    dependencies:
      ansi-colors: 4.1.1
    dev: true

  /entities/1.1.2:
    resolution: {integrity: sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=, tarball: entities/download/entities-1.1.2.tgz}
    dev: true

  /entities/2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=, tarball: entities/download/entities-2.2.0.tgz}
    dev: true

  /entities/3.0.1:
    resolution: {integrity: sha1-K4h8piWF6W2zkDSC0zbBAGwwAdQ=, tarball: entities/download/entities-3.0.1.tgz}
    engines: {node: '>=0.12'}
    dev: true

  /error-ex/1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: error-ex/download/error-ex-1.3.2.tgz}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /esbuild-android-arm64/0.13.5:
    resolution: {integrity: sha1-opmhj9igFq4Z/ZSPxlmz9l0bmS8=, tarball: esbuild-android-arm64/download/esbuild-android-arm64-0.13.5.tgz}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-64/0.13.5:
    resolution: {integrity: sha1-ATWfLGkhvScE0KiV9WA6sz8u6xs=, tarball: esbuild-darwin-64/download/esbuild-darwin-64-0.13.5.tgz}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-arm64/0.13.5:
    resolution: {integrity: sha1-Hb42Lrya/Nq0+a+bsyDazXPirtw=, tarball: esbuild-darwin-arm64/download/esbuild-darwin-arm64-0.13.5.tgz}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-64/0.13.5:
    resolution: {integrity: sha1-/s7ln6SRo/VExzGwwDGb1anafVA=, tarball: esbuild-freebsd-64/download/esbuild-freebsd-64-0.13.5.tgz}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-arm64/0.13.5:
    resolution: {integrity: sha1-TpjA4z7RmmP/1NuHMUmGudk4ULU=, tarball: esbuild-freebsd-arm64/download/esbuild-freebsd-arm64-0.13.5.tgz}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-32/0.13.5:
    resolution: {integrity: sha1-AAg3QK9/FBaVHGNKRh49Ae2BLNA=, tarball: esbuild-linux-32/download/esbuild-linux-32-0.13.5.tgz}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-64/0.13.5:
    resolution: {integrity: sha1-Sb0WSP0gcFlP46rTGSUQjuKRYhY=, tarball: esbuild-linux-64/download/esbuild-linux-64-0.13.5.tgz}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm/0.13.5:
    resolution: {integrity: sha1-J8TpKmWXN2qMP+jHkXfXK6d/hQA=, tarball: esbuild-linux-arm/download/esbuild-linux-arm-0.13.5.tgz}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm64/0.13.5:
    resolution: {integrity: sha1-eO8PINKxdUA1UgdcxtavgPVaItg=, tarball: esbuild-linux-arm64/download/esbuild-linux-arm64-0.13.5.tgz}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-mips64le/0.13.5:
    resolution: {integrity: sha1-QGHL70H5bkoXa+v357LW05fgXoY=, tarball: esbuild-linux-mips64le/download/esbuild-linux-mips64le-0.13.5.tgz}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-ppc64le/0.13.5:
    resolution: {integrity: sha1-KQpcrKZ1G4yAxdB1yv6FcQImMRg=, tarball: esbuild-linux-ppc64le/download/esbuild-linux-ppc64le-0.13.5.tgz}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-openbsd-64/0.13.5:
    resolution: {integrity: sha1-Ij6ycwpv7eeTCitEsLHVsGejzvU=, tarball: esbuild-openbsd-64/download/esbuild-openbsd-64-0.13.5.tgz}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-sunos-64/0.13.5:
    resolution: {integrity: sha1-bxIawoXCmPCUZ3SGB8wEluu/0j4=, tarball: esbuild-sunos-64/download/esbuild-sunos-64-0.13.5.tgz}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-32/0.13.5:
    resolution: {integrity: sha1-DabSQBUvdvPddkwLsDkdiUrNQD8=, tarball: esbuild-windows-32/download/esbuild-windows-32-0.13.5.tgz}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-64/0.13.5:
    resolution: {integrity: sha1-MwJmoslbJsL5Senemww2aST+xT8=, tarball: esbuild-windows-64/download/esbuild-windows-64-0.13.5.tgz}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-arm64/0.13.5:
    resolution: {integrity: sha1-4FAegriPQWXM580Bfbg0KPRZ93U=, tarball: esbuild-windows-arm64/download/esbuild-windows-arm64-0.13.5.tgz}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild/0.11.3:
    resolution: {integrity: sha1-tXFluQe+T/umUfZFBTjOjYwdXrA=, tarball: esbuild/download/esbuild-0.11.3.tgz}
    hasBin: true
    requiresBuild: true
    dev: true

  /esbuild/0.13.5:
    resolution: {integrity: sha1-+a3SwsiZqQI91/e2TEUjIPAIqnk=, tarball: esbuild/download/esbuild-0.13.5.tgz}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      esbuild-android-arm64: 0.13.5
      esbuild-darwin-64: 0.13.5
      esbuild-darwin-arm64: 0.13.5
      esbuild-freebsd-64: 0.13.5
      esbuild-freebsd-arm64: 0.13.5
      esbuild-linux-32: 0.13.5
      esbuild-linux-64: 0.13.5
      esbuild-linux-arm: 0.13.5
      esbuild-linux-arm64: 0.13.5
      esbuild-linux-mips64le: 0.13.5
      esbuild-linux-ppc64le: 0.13.5
      esbuild-openbsd-64: 0.13.5
      esbuild-sunos-64: 0.13.5
      esbuild-windows-32: 0.13.5
      esbuild-windows-64: 0.13.5
      esbuild-windows-arm64: 0.13.5
    dev: true

  /escape-html/1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: escape-html/download/escape-html-1.0.3.tgz}
    dev: true

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: escape-string-regexp/download/escape-string-regexp-1.0.5.tgz?cache=0&sync_timestamp=1632883286702&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}
    dev: true

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: escape-string-regexp/download/escape-string-regexp-4.0.0.tgz?cache=0&sync_timestamp=1632883286702&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fescape-string-regexp%2Fdownload%2Fescape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}
    dev: true

  /eslint-plugin-vue/8.1.1_eslint@8.3.0:
    resolution: {integrity: sha512-rx64IrlhdfPya6u2V5ukOGiLCTgaCBdMSpczLVqyo8A0l+Vbo+lzvIfEUfAQ2auj+MF6y0TwxLorzdCIzHunnw==, tarball: eslint-plugin-vue/download/eslint-plugin-vue-8.1.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0
    dependencies:
      eslint: 8.3.0
      eslint-utils: 3.0.0_eslint@8.3.0
      natural-compare: 1.4.0
      semver: 7.3.5
      vue-eslint-parser: 8.0.1_eslint@8.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-scope/6.0.0:
    resolution: {integrity: sha1-nPRbE8Wsjz1MUPRqUSH2Gz4xiXg=, tarball: eslint-scope/download/eslint-scope-6.0.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-scope/7.1.0:
    resolution: {integrity: sha512-aWwkhnS0qAXqNOgKOK0dJ2nvzEbhEvpy8OlJ9kZ0FeZnA6zpjv1/Vei+puGFFX7zkPCkHHXb7IDX3A+7yPrRWg==, tarball: eslint-scope/download/eslint-scope-7.1.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-utils/3.0.0_eslint@8.3.0:
    resolution: {integrity: sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=, tarball: eslint-utils/download/eslint-utils-3.0.0.tgz}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'
    dependencies:
      eslint: 8.3.0
      eslint-visitor-keys: 2.1.0
    dev: true

  /eslint-visitor-keys/2.1.0:
    resolution: {integrity: sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=, tarball: eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz}
    engines: {node: '>=10'}
    dev: true

  /eslint-visitor-keys/3.0.0:
    resolution: {integrity: sha1-4y6Zxs3C6wY/IE7aXbZ7/li7QYY=, tarball: eslint-visitor-keys/download/eslint-visitor-keys-3.0.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint-visitor-keys/3.1.0:
    resolution: {integrity: sha512-yWJFpu4DtjsWKkt5GeNBBuZMlNcYVs6vRCLoCVEJrTjaSB6LC98gFipNK/erM2Heg/E8mIK+hXG/pJMLK+eRZA==, tarball: eslint-visitor-keys/download/eslint-visitor-keys-3.1.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint/8.3.0:
    resolution: {integrity: sha512-aIay56Ph6RxOTC7xyr59Kt3ewX185SaGnAr8eWukoPLeriCrvGjvAubxuvaXOfsxhtwV5g0uBOsyhAom4qJdww==, tarball: eslint/download/eslint-8.3.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true
    dependencies:
      '@eslint/eslintrc': 1.0.4
      '@humanwhocodes/config-array': 0.6.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.2
      doctrine: 3.0.0
      enquirer: 2.3.6
      escape-string-regexp: 4.0.0
      eslint-scope: 7.1.0
      eslint-utils: 3.0.0_eslint@8.3.0
      eslint-visitor-keys: 3.1.0
      espree: 9.1.0
      esquery: 1.4.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 6.0.2
      globals: 13.11.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.0.4
      natural-compare: 1.4.0
      optionator: 0.9.1
      progress: 2.0.3
      regexpp: 3.2.0
      semver: 7.3.5
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      text-table: 0.2.0
      v8-compile-cache: 2.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/9.0.0:
    resolution: {integrity: sha1-6QopZWmCKFAudxx6WEibGp0QcJA=, tarball: espree/download/espree-9.0.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.6.0
      acorn-jsx: 5.3.2_acorn@8.6.0
      eslint-visitor-keys: 3.0.0
    dev: true

  /espree/9.1.0:
    resolution: {integrity: sha512-ZgYLvCS1wxOczBYGcQT9DDWgicXwJ4dbocr9uYN+/eresBAUuBu+O4WzB21ufQ/JqQT8gyp7hJ3z8SHii32mTQ==, tarball: espree/download/espree-9.1.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.6.0
      acorn-jsx: 5.3.2_acorn@8.6.0
      eslint-visitor-keys: 3.1.0
    dev: true

  /esquery/1.4.0:
    resolution: {integrity: sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=, tarball: esquery/download/esquery-1.4.0.tgz}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse/5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}
    dev: true

  /estree-walker/1.0.1:
    resolution: {integrity: sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=, tarball: estree-walker/download/estree-walker-1.0.1.tgz}
    dev: true

  /estree-walker/2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=, tarball: estree-walker/download/estree-walker-2.0.2.tgz}

  /esutils/2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /etag/1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=, tarball: etag/download/etag-1.8.1.tgz}
    engines: {node: '>= 0.6'}
    dev: true

  /execa/5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=, tarball: execa/download/execa-5.1.1.tgz}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.5
      strip-final-newline: 2.0.0
    dev: true

  /execall/2.0.0:
    resolution: {integrity: sha1-FqBrX+UJnffQC+XZwG7s3tFmO0U=, tarball: execall/download/execall-2.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      clone-regexp: 2.2.0
    dev: true

  /expand-brackets/2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=, tarball: expand-brackets/download/expand-brackets-2.1.4.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /expand-tilde/2.0.2:
    resolution: {integrity: sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=, tarball: expand-tilde/download/expand-tilde-2.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      homedir-polyfill: 1.0.3
    dev: true

  /extend-shallow/2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=, tarball: extend-shallow/download/extend-shallow-2.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: true

  /extend-shallow/3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=, tarball: extend-shallow/download/extend-shallow-3.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1
    dev: true

  /extend/3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=, tarball: extend/download/extend-3.0.2.tgz}
    dev: true

  /external-editor/3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=, tarball: external-editor/download/external-editor-3.1.0.tgz}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: registry.nlark.com/iconv-lite/0.4.24
      tmp: 0.0.33
    dev: true

  /extglob/2.0.4:
    resolution: {integrity: sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=, tarball: extglob/download/extglob-2.0.4.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /extsprintf/1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=, tarball: extsprintf/download/extsprintf-1.3.0.tgz}
    engines: {'0': node >=0.6.0}
    dev: true

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: fast-deep-equal/download/fast-deep-equal-3.1.3.tgz?cache=0&sync_timestamp=1632883289464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffast-deep-equal%2Fdownload%2Ffast-deep-equal-3.1.3.tgz}
    dev: true

  /fast-glob/3.2.7:
    resolution: {integrity: sha1-/Wy3otfpqnp4RhEehaGW1rL3ZqE=, tarball: fast-glob/download/fast-glob-3.2.7.tgz}
    engines: {node: '>=8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.4
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz?cache=0&sync_timestamp=1632883644068&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.1.0.tgz}
    dev: true

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}
    dev: true

  /fastest-levenshtein/1.0.12:
    resolution: {integrity: sha1-mZD306iMxan/0fF0V0UlFwDUl+I=, tarball: fastest-levenshtein/download/fastest-levenshtein-1.0.12.tgz}
    dev: true

  /fastq/1.13.0:
    resolution: {integrity: sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=, tarball: fastq/download/fastq-1.13.0.tgz}
    dependencies:
      reusify: 1.0.4
    dev: true

  /figures/3.2.0:
    resolution: {integrity: sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=, tarball: figures/download/figures-3.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-entry-cache/6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=, tarball: file-entry-cache/download/file-entry-cache-6.0.1.tgz?cache=0&sync_timestamp=1632883646270&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffile-entry-cache%2Fdownload%2Ffile-entry-cache-6.0.1.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.0.4
    dev: true

  /filelist/1.0.2:
    resolution: {integrity: sha1-gCAvIUYtTRwuIUEZsYB8G8A4Dls=, tarball: filelist/download/filelist-1.0.2.tgz}
    dependencies:
      minimatch: 3.0.4
    dev: true

  /fill-range/4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=, tarball: fill-range/download/fill-range-4.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1
    dev: true

  /fill-range/7.0.1:
    resolution: {integrity: sha1-GRmmp8df44ssfHflGYU12prN2kA=, tarball: fill-range/download/fill-range-7.0.1.tgz}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /finalhandler/1.1.2:
    resolution: {integrity: sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=, tarball: finalhandler/download/finalhandler-1.1.2.tgz}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    dev: true

  /find-up/4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=, tarball: find-up/download/find-up-4.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: true

  /findup-sync/2.0.0:
    resolution: {integrity: sha1-kyaxSIwi0aYIhlCoaQGy2akKLLw=, tarball: findup-sync/download/findup-sync-2.0.0.tgz}
    engines: {node: '>= 0.10'}
    dependencies:
      detect-file: 1.0.0
      is-glob: 3.1.0
      micromatch: 3.1.10
      resolve-dir: 1.0.1
    dev: true

  /fined/1.2.0:
    resolution: {integrity: sha1-0AvszxqitHXRbUI7Aji3E6LEo3s=, tarball: fined/download/fined-1.2.0.tgz}
    engines: {node: '>= 0.10'}
    dependencies:
      expand-tilde: 2.0.2
      is-plain-object: 2.0.4
      object.defaults: 1.1.0
      object.pick: 1.3.0
      parse-filepath: 1.0.2
    dev: true

  /first-chunk-stream/1.0.0:
    resolution: {integrity: sha1-Wb+1DNkF9g18OUzT2ayqtOatk04=, tarball: first-chunk-stream/download/first-chunk-stream-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /flagged-respawn/1.0.1:
    resolution: {integrity: sha1-595vEnnd2cqarIpZcdYYYGs6q0E=, tarball: flagged-respawn/download/flagged-respawn-1.0.1.tgz}
    engines: {node: '>= 0.10'}
    dev: true

  /flat-cache/3.0.4:
    resolution: {integrity: sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=, tarball: flat-cache/download/flat-cache-3.0.4.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.2.2
      rimraf: 3.0.2
    dev: true

  /flatted/3.2.2:
    resolution: {integrity: sha1-ZL/tXLaP48p4s+shStl7Y77c5WE=, tarball: flatted/download/flatted-3.2.2.tgz}
    dev: true

  /for-in/1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=, tarball: for-in/download/for-in-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /for-own/1.0.0:
    resolution: {integrity: sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=, tarball: for-own/download/for-own-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
    dev: true

  /forever-agent/0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=, tarball: forever-agent/download/forever-agent-0.6.1.tgz}
    dev: true

  /form-data/2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=, tarball: form-data/download/form-data-2.3.3.tgz}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.33
    dev: true

  /fragment-cache/0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=, tarball: fragment-cache/download/fragment-cache-0.2.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2
    dev: true

  /fs-extra/10.0.0:
    resolution: {integrity: sha1-n/YbZV3eU/s0qC34S7IUzoAuF8E=, tarball: fs-extra/download/fs-extra-10.0.0.tgz}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.8
      jsonfile: 6.1.0
      universalify: 2.0.0
    dev: true

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: fs.realpath/download/fs.realpath-1.0.0.tgz}
    dev: true

  /fsevents/2.3.2:
    resolution: {integrity: sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=, tarball: fsevents/download/fsevents-2.3.2.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    dev: true
    optional: true

  /function-bind/1.1.1:
    resolution: {integrity: sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=, tarball: function-bind/download/function-bind-1.1.1.tgz}

  /functional-red-black-tree/1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=, tarball: functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz}
    dev: true

  /gaze/1.1.3:
    resolution: {integrity: sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=, tarball: gaze/download/gaze-1.1.3.tgz}
    engines: {node: '>= 4.0.0'}
    dependencies:
      globule: 1.3.3
    dev: true

  /get-intrinsic/1.1.1:
    resolution: {integrity: sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=, tarball: get-intrinsic/download/get-intrinsic-1.1.1.tgz}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.2

  /get-pixels/3.3.3:
    resolution: {integrity: sha1-ceLf1L77gQtUeKYcY1SACXbOAcc=, tarball: get-pixels/download/get-pixels-3.3.3.tgz}
    dependencies:
      data-uri-to-buffer: 0.0.3
      jpeg-js: 0.4.3
      mime-types: 2.1.33
      ndarray: 1.0.19
      ndarray-pack: 1.2.1
      node-bitmap: 0.0.1
      omggif: 1.0.10
      parse-data-uri: 0.2.0
      pngjs: 3.4.0
      request: 2.88.2
      through: 2.3.8
    dev: true

  /get-stdin/8.0.0:
    resolution: {integrity: sha1-y61qc/63X27rIrqeAfiaooqpelM=, tarball: get-stdin/download/get-stdin-8.0.0.tgz}
    engines: {node: '>=10'}
    dev: true

  /get-stream/6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=, tarball: get-stream/download/get-stream-6.0.1.tgz}
    engines: {node: '>=10'}
    dev: true

  /get-value/2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=, tarball: get-value/download/get-value-2.0.6.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /getpass/0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=, tarball: getpass/download/getpass-0.1.7.tgz}
    dependencies:
      assert-plus: 1.0.0
    dev: true

  /gif-encoder/0.4.3:
    resolution: {integrity: sha1-iitP6MqJWkjjoLbLs0CgpqNXGJk=, tarball: gif-encoder/download/gif-encoder-0.4.3.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      readable-stream: 1.1.14
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-parent/6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: glob-parent/download/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob/7.1.7:
    resolution: {integrity: sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=, tarball: glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1632883301775&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.0.4
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /glob/7.2.0:
    resolution: {integrity: sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=, tarball: glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632883301775&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.0.4
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /global-modules/1.0.0:
    resolution: {integrity: sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=, tarball: global-modules/download/global-modules-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      global-prefix: 1.0.2
      is-windows: 1.0.2
      resolve-dir: 1.0.1
    dev: true

  /global-modules/2.0.0:
    resolution: {integrity: sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=, tarball: global-modules/download/global-modules-2.0.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      global-prefix: 3.0.0
    dev: true

  /global-prefix/1.0.2:
    resolution: {integrity: sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=, tarball: global-prefix/download/global-prefix-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      homedir-polyfill: 1.0.3
      ini: 1.3.8
      is-windows: 1.0.2
      which: 1.3.1
    dev: true

  /global-prefix/3.0.0:
    resolution: {integrity: sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=, tarball: global-prefix/download/global-prefix-3.0.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      ini: 1.3.8
      kind-of: 6.0.3
      which: 1.3.1
    dev: true

  /globals/13.11.0:
    resolution: {integrity: sha1-QO9njaEX/nvS4o8fqySVG9AlW+c=, tarball: globals/download/globals-13.11.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globby/10.0.2:
    resolution: {integrity: sha1-J3WT50WsqkZGw6tBEonsR6A5JUM=, tarball: globby/download/globby-10.0.2.tgz}
    engines: {node: '>=8'}
    dependencies:
      '@types/glob': 7.2.0
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.2.7
      glob: 7.2.0
      ignore: 5.1.9
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globby/11.0.4:
    resolution: {integrity: sha1-LLr/d8Lypi5x6bKBOme5ejowAaU=, tarball: globby/download/globby-11.0.4.tgz}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.2.7
      ignore: 5.1.9
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globjoin/0.1.4:
    resolution: {integrity: sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=, tarball: globjoin/download/globjoin-0.1.4.tgz}
    dev: true

  /globule/1.3.3:
    resolution: {integrity: sha1-gRkZ7qwatzROkF8uO+gKE0R5c8I=, tarball: globule/download/globule-1.3.3.tgz}
    engines: {node: '>= 0.10'}
    dependencies:
      glob: 7.1.7
      lodash: 4.17.21
      minimatch: 3.0.4
    dev: true

  /graceful-fs/4.2.8:
    resolution: {integrity: sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=, tarball: graceful-fs/download/graceful-fs-4.2.8.tgz}
    dev: true

  /handlebars-layouts/3.1.4:
    resolution: {integrity: sha1-JrO+uTG0uHffv35v6vQFjuYiiwI=, tarball: handlebars-layouts/download/handlebars-layouts-3.1.4.tgz}
    engines: {node: '>= 0.10'}
    dev: true

  /handlebars/4.7.7:
    resolution: {integrity: sha1-nOM0FqrQLb1sj6+oJA1dmABJRaE=, tarball: handlebars/download/handlebars-4.7.7.tgz}
    engines: {node: '>=0.4.7'}
    hasBin: true
    dependencies:
      minimist: registry.nlark.com/minimist/1.2.5
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.14.3
    dev: true

  /har-schema/2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=, tarball: har-schema/download/har-schema-2.0.0.tgz}
    engines: {node: '>=4'}
    dev: true

  /har-validator/5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=, tarball: har-validator/download/har-validator-5.1.5.tgz}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0
    dev: true

  /hard-rejection/2.1.0:
    resolution: {integrity: sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=, tarball: hard-rejection/download/hard-rejection-2.1.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=, tarball: has-ansi/download/has-ansi-2.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-flag/1.0.0:
    resolution: {integrity: sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=, tarball: has-flag/download/has-flag-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /has-flag/5.0.1:
    resolution: {integrity: sha1-VIPbKuAqRy0dBpFGL8WH0YQ82UA=, tarball: has-flag/download/has-flag-5.0.1.tgz}
    engines: {node: '>=12'}
    dev: true

  /has-symbols/1.0.2:
    resolution: {integrity: sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=, tarball: has-symbols/download/has-symbols-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  /has-value/0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=, tarball: has-value/download/has-value-0.3.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0
    dev: true

  /has-value/1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=, tarball: has-value/download/has-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1
    dev: true

  /has-values/0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=, tarball: has-values/download/has-values-0.1.4.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /has-values/1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=, tarball: has-values/download/has-values-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0
    dev: true

  /has/1.0.3:
    resolution: {integrity: sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=, tarball: has/download/has-1.0.3.tgz}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /he/1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: he/download/he-1.2.0.tgz}
    hasBin: true
    dev: true

  /header-case/1.0.1:
    resolution: {integrity: sha1-lTWXMZfBRLCWE81l0xfvGZY70C0=, tarball: header-case/download/header-case-1.0.1.tgz}
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3
    dev: true

  /highlight.js-async-webpack/1.0.4:
    resolution: {integrity: sha1-wGtnv5nwSQRdYrdW5YVbCRLsYWw=, tarball: highlight.js-async-webpack/download/highlight.js-async-webpack-1.0.4.tgz}
    dev: false

  /highlight.js/9.18.5:
    resolution: {integrity: sha1-0Yo1mGfzeME41oGe38KorNXymCU=, tarball: highlight.js/download/highlight.js-9.18.5.tgz}
    deprecated: Support has ended for 9.x series. Upgrade to @latest
    requiresBuild: true
    dev: false

  /homedir-polyfill/1.0.3:
    resolution: {integrity: sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=, tarball: homedir-polyfill/download/homedir-polyfill-1.0.3.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      parse-passwd: 1.0.0
    dev: true

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=, tarball: hosted-git-info/download/hosted-git-info-2.8.9.tgz}
    dev: true

  /hosted-git-info/4.0.2:
    resolution: {integrity: sha1-XkJVB+7eT+qEa3Ji8IOEVsQgmWE=, tarball: hosted-git-info/download/hosted-git-info-4.0.2.tgz}
    engines: {node: '>=10'}
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /hotkeys-js/3.8.7:
    resolution: {integrity: sha1-wWyrl4tT1yQvhgyjky6Xa5I5mYE=, tarball: hotkeys-js/download/hotkeys-js-3.8.7.tgz}
    dev: false

  /html-minifier-terser/5.1.1:
    resolution: {integrity: sha1-ki6W8fO7YIMsJjS3mIQJY4mx8FQ=, tarball: html-minifier-terser/download/html-minifier-terser-5.1.1.tgz}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 4.2.3
      commander: 4.1.1
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 4.8.0
    dev: true

  /html-tags/3.1.0:
    resolution: {integrity: sha1-e15vfmZen7QfMAB+2eDUHpf7IUA=, tarball: html-tags/download/html-tags-3.1.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /htmlparser2/3.10.1:
    resolution: {integrity: sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=, tarball: htmlparser2/download/htmlparser2-3.10.1.tgz}
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.4.2
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: true

  /htmlparser2/7.1.2:
    resolution: {integrity: sha1-WHkj048DvIngMHbgDLosdHPzf3w=, tarball: htmlparser2/download/htmlparser2-7.1.2.tgz}
    dependencies:
      domelementtype: 2.2.0
      domhandler: 4.2.2
      domutils: 2.8.0
      entities: 3.0.1
    dev: true

  /http-server/14.0.0:
    resolution: {integrity: sha1-vSFJUqYLk86Mqbvouhgfr3+YIbA=, tarball: http-server/download/http-server-14.0.0.tgz}
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      basic-auth: registry.nlark.com/basic-auth/2.0.1
      colors: registry.nlark.com/colors/1.4.0
      corser: registry.nlark.com/corser/2.0.1
      he: registry.nlark.com/he/1.2.0
      html-encoding-sniffer: registry.nlark.com/html-encoding-sniffer/3.0.0
      http-proxy: registry.nlark.com/http-proxy/1.18.1
      mime: registry.nlark.com/mime/1.6.0
      minimist: registry.nlark.com/minimist/1.2.5
      opener: registry.nlark.com/opener/1.5.2
      portfinder: registry.nlark.com/portfinder/1.0.28
      secure-compare: 3.0.1
      union: registry.nlark.com/union/0.5.0
      url-join: registry.nlark.com/url-join/4.0.1
    transitivePeerDependencies:
      - debug
    dev: true

  /http-signature/1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=, tarball: http-signature/download/http-signature-1.2.0.tgz}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.1
      sshpk: 1.16.1
    dev: true

  /human-signals/2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=, tarball: human-signals/download/human-signals-2.1.0.tgz}
    engines: {node: '>=10.17.0'}
    dev: true

  /husky/7.0.4:
    resolution: {integrity: sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=, tarball: husky/download/husky-7.0.4.tgz}
    engines: {node: '>=12'}
    hasBin: true
    dev: true

  /ignore/4.0.6:
    resolution: {integrity: sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=, tarball: ignore/download/ignore-4.0.6.tgz}
    engines: {node: '>= 4'}
    dev: true

  /ignore/5.1.9:
    resolution: {integrity: sha1-nsGly+jhRG7GDUQgBg1Dqm5zgvs=, tarball: ignore/download/ignore-5.1.9.tgz}
    engines: {node: '>= 4'}
    dev: true

  /image-size/0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=, tarball: image-size/download/image-size-0.5.5.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dev: true

  /import-fresh/3.3.0:
    resolution: {integrity: sha1-NxYsJfy566oublPVtNiM4X2eDCs=, tarball: import-fresh/download/import-fresh-3.3.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-lazy/4.0.0:
    resolution: {integrity: sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=, tarball: import-lazy/download/import-lazy-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /import-meta-resolve/1.1.1:
    resolution: {integrity: sha1-JE/VQv0frnNVDU+LPN47uh17Kxg=, tarball: import-meta-resolve/download/import-meta-resolve-1.1.1.tgz}
    dependencies:
      builtins: 4.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: imurmurhash/download/imurmurhash-0.1.4.tgz?cache=0&sync_timestamp=1632883651457&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fimurmurhash%2Fdownload%2Fimurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string/4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=, tarball: indent-string/download/indent-string-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: inflight/download/inflight-1.0.6.tgz}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: true

  /inherits/2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: inherits/download/inherits-2.0.4.tgz}
    dev: true

  /ini/1.3.8:
    resolution: {integrity: sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=, tarball: ini/download/ini-1.3.8.tgz}
    dev: true

  /inquirer/7.3.3:
    resolution: {integrity: sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=, tarball: inquirer/download/inquirer-7.3.3.tgz}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: true

  /interpret/1.4.0:
    resolution: {integrity: sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=, tarball: interpret/download/interpret-1.4.0.tgz}
    engines: {node: '>= 0.10'}
    dev: true

  /iota-array/1.0.0:
    resolution: {integrity: sha1-ge9X/l0FgUzVjCSDYyqZwwoOgIc=, tarball: iota-array/download/iota-array-1.0.0.tgz}
    dev: true

  /is-absolute/1.0.0:
    resolution: {integrity: sha1-OV4a6EsR8mrReV5zwXN45IowFXY=, tarball: is-absolute/download/is-absolute-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-relative: 1.0.0
      is-windows: 1.0.2
    dev: true

  /is-accessor-descriptor/0.1.6:
    resolution: {integrity: sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=, tarball: is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-accessor-descriptor/1.0.0:
    resolution: {integrity: sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=, tarball: is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: is-arrayish/download/is-arrayish-0.2.1.tgz}
    dev: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=, tarball: is-binary-path/download/is-binary-path-2.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: true

  /is-buffer/1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=, tarball: is-buffer/download/is-buffer-1.1.6.tgz}
    dev: true

  /is-core-module/2.8.0:
    resolution: {integrity: sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=, tarball: is-core-module/download/is-core-module-2.8.0.tgz}
    dependencies:
      has: 1.0.3
    dev: true

  /is-data-descriptor/0.1.4:
    resolution: {integrity: sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=, tarball: is-data-descriptor/download/is-data-descriptor-0.1.4.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-data-descriptor/1.0.0:
    resolution: {integrity: sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=, tarball: is-data-descriptor/download/is-data-descriptor-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /is-descriptor/0.1.6:
    resolution: {integrity: sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=, tarball: is-descriptor/download/is-descriptor-0.1.6.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 0.1.6
      is-data-descriptor: 0.1.4
      kind-of: 5.1.0
    dev: true

  /is-descriptor/1.0.2:
    resolution: {integrity: sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=, tarball: is-descriptor/download/is-descriptor-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 1.0.0
      is-data-descriptor: 1.0.0
      kind-of: 6.0.3
    dev: true

  /is-extendable/0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=, tarball: is-extendable/download/is-extendable-0.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-extendable/1.0.1:
    resolution: {integrity: sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=, tarball: is-extendable/download/is-extendable-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /is-fullwidth-code-point/4.0.0:
    resolution: {integrity: sha1-+uMWfHKedGP4RhzlErCApJJoqog=, tarball: is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz}
    engines: {node: '>=12'}
    dev: true

  /is-glob/3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=, tarball: is-glob/download/is-glob-3.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-lower-case/1.1.3:
    resolution: {integrity: sha1-fhR75HaNxGbbO/shzGCzHmrWk5M=, tarball: is-lower-case/download/is-lower-case-1.1.3.tgz}
    dependencies:
      lower-case: 1.1.4
    dev: true

  /is-module/1.0.0:
    resolution: {integrity: sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=, tarball: is-module/download/is-module-1.0.0.tgz}
    dev: true

  /is-number/3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=, tarball: is-number/download/is-number-3.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-path-cwd/2.2.0:
    resolution: {integrity: sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=, tarball: is-path-cwd/download/is-path-cwd-2.2.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /is-path-inside/3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=, tarball: is-path-inside/download/is-path-inside-3.0.3.tgz}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj/1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=, tarball: is-plain-obj/download/is-plain-obj-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-object/2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=, tarball: is-plain-object/download/is-plain-object-2.0.4.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /is-plain-object/5.0.0:
    resolution: {integrity: sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=, tarball: is-plain-object/download/is-plain-object-5.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-regexp/2.1.0:
    resolution: {integrity: sha1-zXNKVoZOI7lWv058ZsOWpMCyLC0=, tarball: is-regexp/download/is-regexp-2.1.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /is-relative/1.0.0:
    resolution: {integrity: sha1-obtpNc6MXboei5dUubLcwCDiJg0=, tarball: is-relative/download/is-relative-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-unc-path: 1.0.0
    dev: true

  /is-stream/2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=, tarball: is-stream/download/is-stream-2.0.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /is-typedarray/1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: is-typedarray/download/is-typedarray-1.0.0.tgz}
    dev: true

  /is-unc-path/1.0.0:
    resolution: {integrity: sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0=, tarball: is-unc-path/download/is-unc-path-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      unc-path-regex: 0.1.2
    dev: true

  /is-upper-case/1.1.2:
    resolution: {integrity: sha1-jQsfp+eTOh5YSDYA7H2WYcuvdW8=, tarball: is-upper-case/download/is-upper-case-1.1.2.tgz}
    dependencies:
      upper-case: 1.1.3
    dev: true

  /is-utf8/0.2.1:
    resolution: {integrity: sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=, tarball: is-utf8/download/is-utf8-0.2.1.tgz}
    dev: true

  /is-windows/1.0.2:
    resolution: {integrity: sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=, tarball: is-windows/download/is-windows-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /isarray/0.0.1:
    resolution: {integrity: sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=, tarball: isarray/download/isarray-0.0.1.tgz}
    dev: true

  /isarray/1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=, tarball: isarray/download/isarray-1.0.0.tgz}
    dev: true

  /isbinaryfile/4.0.8:
    resolution: {integrity: sha1-XTS5SGW9SUZjPsx4oCb8dsWxH88=, tarball: isbinaryfile/download/isbinaryfile-4.0.8.tgz?cache=0&sync_timestamp=1634183877546&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fisbinaryfile%2Fdownload%2Fisbinaryfile-4.0.8.tgz}
    engines: {node: '>= 8.0.0'}
    dev: true

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: isexe/download/isexe-2.0.0.tgz}
    dev: true

  /isobject/2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=, tarball: isobject/download/isobject-2.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0
    dev: true

  /isobject/3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=, tarball: isobject/download/isobject-3.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /isstream/0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=, tarball: isstream/download/isstream-0.1.2.tgz}
    dev: true

  /jake/10.8.2:
    resolution: {integrity: sha1-68nehVgWCmbYLQ6txqLlj7xQCns=, tarball: jake/download/jake-10.8.2.tgz}
    hasBin: true
    dependencies:
      async: registry.nlark.com/async/0.9.2
      chalk: 2.4.2
      filelist: 1.0.2
      minimatch: 3.0.4
    dev: true

  /jpeg-js/0.4.3:
    resolution: {integrity: sha1-YVjgnxmDrXc4E3BL6AaAVQ7/l3s=, tarball: jpeg-js/download/jpeg-js-0.4.3.tgz}
    dev: true

  /js-base64/2.6.4:
    resolution: {integrity: sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=, tarball: js-base64/download/js-base64-2.6.4.tgz}
    dev: true

  /js-cookie/3.0.1:
    resolution: {integrity: sha1-njm0xsL1ZWNwjX0x9vXyGHOpJBQ=, tarball: js-cookie/download/js-cookie-3.0.1.tgz}
    engines: {node: '>=12'}
    dev: false

  /js-tokens/4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: js-tokens/download/js-tokens-4.0.0.tgz}
    dev: true

  /js-yaml/4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: js-yaml/download/js-yaml-4.1.0.tgz}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsbn/0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=, tarball: jsbn/download/jsbn-0.1.1.tgz}
    dev: true

  /json-content-demux/0.1.4:
    resolution: {integrity: sha1-bVc/u1orIkriJXNuKH8y6pGkJz0=, tarball: json-content-demux/download/json-content-demux-0.1.4.tgz}
    engines: {node: '>= 0.6.0'}
    dev: true

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz}
    dev: true

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}
    dev: true

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=, tarball: json-schema-traverse/download/json-schema-traverse-1.0.0.tgz}
    dev: true

  /json-schema/0.2.3:
    resolution: {integrity: sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=, tarball: json-schema/download/json-schema-0.2.3.tgz}
    dev: true

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}
    dev: true

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=, tarball: json-stringify-safe/download/json-stringify-safe-5.0.1.tgz}
    dev: true

  /json5/1.0.1:
    resolution: {integrity: sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=, tarball: json5/download/json5-1.0.1.tgz}
    hasBin: true
    dependencies:
      minimist: registry.nlark.com/minimist/1.2.5
    dev: true

  /jsonfile/6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: jsonfile/download/jsonfile-6.1.0.tgz}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: 4.2.8
    dev: true

  /jsprim/1.4.1:
    resolution: {integrity: sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=, tarball: jsprim/download/jsprim-1.4.1.tgz}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.2.3
      verror: 1.10.0
    dev: true

  /kind-of/3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=, tarball: kind-of/download/kind-of-3.2.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: true

  /kind-of/4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=, tarball: kind-of/download/kind-of-4.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: true

  /kind-of/5.1.0:
    resolution: {integrity: sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=, tarball: kind-of/download/kind-of-5.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /kind-of/6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=, tarball: kind-of/download/kind-of-6.0.3.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /known-css-properties/0.23.0:
    resolution: {integrity: sha1-5kPhurKx+LopLuqVVxIcwC6YRqA=, tarball: known-css-properties/download/known-css-properties-0.23.0.tgz}
    dev: true

  /layout/2.2.0:
    resolution: {integrity: sha1-MeRL/BjdEBmz/7II5AKku/4uavQ=, tarball: layout/download/layout-2.2.0.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      bin-pack: 1.0.2
    dev: true

  /levn/0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: levn/download/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /liftoff/2.5.0:
    resolution: {integrity: sha1-IAkpG7Mc6oYbvxCnwVooyvdcMew=, tarball: liftoff/download/liftoff-2.5.0.tgz}
    engines: {node: '>= 0.8'}
    dependencies:
      extend: 3.0.2
      findup-sync: 2.0.0
      fined: 1.2.0
      flagged-respawn: 1.0.1
      is-plain-object: 2.0.4
      object.map: 1.0.1
      rechoir: 0.6.2
      resolve: 1.20.0
    dev: true

  /lilconfig/2.0.4:
    resolution: {integrity: sha1-9FB9BD1wWLOAtqj1y3vNSzTO4II=, tarball: lilconfig/download/lilconfig-2.0.4.tgz}
    engines: {node: '>=10'}
    dev: true

  /lines-and-columns/1.1.6:
    resolution: {integrity: sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=, tarball: lines-and-columns/download/lines-and-columns-1.1.6.tgz}
    dev: true

  /lint-staged/12.1.2:
    resolution: {integrity: sha512-bSMcQVqMW98HLLLR2c2tZ+vnDCnx4fd+0QJBQgN/4XkdspGRPc8DGp7UuOEBe1ApCfJ+wXXumYnJmU+wDo7j9A==, tarball: lint-staged/download/lint-staged-12.1.2.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dependencies:
      cli-truncate: 3.1.0
      colorette: 2.0.16
      commander: 8.3.0
      debug: 4.3.2_supports-color@9.0.2
      enquirer: 2.3.6
      execa: 5.1.1
      lilconfig: 2.0.4
      listr2: 3.13.3_enquirer@2.3.6
      micromatch: 4.0.4
      normalize-path: 3.0.0
      object-inspect: 1.11.0
      string-argv: 0.3.1
      supports-color: 9.0.2
      yaml: 1.10.2
    dev: true

  /listr2/3.13.3_enquirer@2.3.6:
    resolution: {integrity: sha1-2PYJXJNxs4LJscK8M8WUHY4XfxE=, tarball: listr2/download/listr2-3.13.3.tgz}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    dependencies:
      cli-truncate: 2.1.0
      clone: 2.1.2
      colorette: 2.0.16
      enquirer: 2.3.6
      log-update: 4.0.0
      p-map: 4.0.0
      rxjs: 7.4.0
      through: 2.3.8
      wrap-ansi: 7.0.0
    dev: true

  /loader-utils/1.4.0:
    resolution: {integrity: sha1-xXm140yzSxp07cbB+za/o3HVphM=, tarball: loader-utils/download/loader-utils-1.4.0.tgz}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.1
    dev: true

  /local-pkg/0.1.0:
    resolution: {integrity: sha1-dCKyro/B47nvLxMrCg6S2HnfUu8=, tarball: local-pkg/download/local-pkg-0.1.0.tgz}
    dependencies:
      mlly: 0.2.10
    dev: true

  /local-pkg/0.4.0:
    resolution: {integrity: sha1-5iIRcYE9WggS3YT6gP+xyPzABTE=, tarball: local-pkg/download/local-pkg-0.4.0.tgz}
    engines: {node: '>=14'}
    dependencies:
      mlly: 0.2.10
    dev: true

  /locate-path/5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=, tarball: locate-path/download/locate-path-5.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: true

  /lodash.get/4.4.2:
    resolution: {integrity: sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=, tarball: lodash.get/download/lodash.get-4.4.2.tgz}
    dev: true

  /lodash.merge/4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: lodash.merge/download/lodash.merge-4.6.2.tgz}
    dev: true

  /lodash.truncate/4.4.2:
    resolution: {integrity: sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=, tarball: lodash.truncate/download/lodash.truncate-4.4.2.tgz}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: lodash/download/lodash-4.17.21.tgz}

  /log-symbols/2.2.0:
    resolution: {integrity: sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=, tarball: log-symbols/download/log-symbols-2.2.0.tgz}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
    dev: true

  /log-update/4.0.0:
    resolution: {integrity: sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=, tarball: log-update/download/log-update-4.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0
    dev: true

  /lower-case-first/1.0.2:
    resolution: {integrity: sha1-5dp8JvKacHO+AtUrrJmA5ZIq36E=, tarball: lower-case-first/download/lower-case-first-1.0.2.tgz}
    dependencies:
      lower-case: 1.1.4
    dev: true

  /lower-case/1.1.4:
    resolution: {integrity: sha1-miyr0bno4K6ZOkv31YdcOcQujqw=, tarball: lower-case/download/lower-case-1.1.4.tgz}
    dev: true

  /lower-case/2.0.2:
    resolution: {integrity: sha1-b6I3xj29xKgsoP2ILkci3F5jTig=, tarball: lower-case/download/lower-case-2.0.2.tgz}
    dependencies:
      tslib: 2.3.1
    dev: true

  /lru-cache/6.0.0:
    resolution: {integrity: sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=, tarball: lru-cache/download/lru-cache-6.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /magic-string/0.25.7:
    resolution: {integrity: sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE=, tarball: magic-string/download/magic-string-0.25.7.tgz}
    dependencies:
      sourcemap-codec: 1.4.8

  /make-iterator/1.0.1:
    resolution: {integrity: sha1-KbM/MSqo9UfEpeSQ9Wr87JkTOtY=, tarball: make-iterator/download/make-iterator-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /map-cache/0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=, tarball: map-cache/download/map-cache-0.2.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj/1.0.1:
    resolution: {integrity: sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=, tarball: map-obj/download/map-obj-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-obj/4.3.0:
    resolution: {integrity: sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=, tarball: map-obj/download/map-obj-4.3.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /map-visit/1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=, tarball: map-visit/download/map-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1
    dev: true

  /mathml-tag-names/2.1.3:
    resolution: {integrity: sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=, tarball: mathml-tag-names/download/mathml-tag-names-2.1.3.tgz}
    dev: true

  /mavon-editor/3.0.0-beta:
    resolution: {integrity: sha512-cmGKbkgrlvFtrhEVyEUIO2X30R+0p0cWpWcYZ97p2E7zn5flKakgv3gq/4mrzjb6UTmhUiHis38k+a6fP6XUEQ==, tarball: mavon-editor/download/mavon-editor-3.0.0-beta.tgz}
    dependencies:
      highlight.js: 9.18.5
      highlight.js-async-webpack: 1.0.4
      xss: 1.0.10
    dev: false

  /mdn-data/2.0.14:
    resolution: {integrity: sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=, tarball: mdn-data/download/mdn-data-2.0.14.tgz}
    dev: true

  /memoize-one/6.0.0:
    resolution: {integrity: sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=, tarball: memoize-one/download/memoize-one-6.0.0.tgz}
    dev: false

  /meow/9.0.0:
    resolution: {integrity: sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=, tarball: meow/download/meow-9.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimist': 1.2.2
      camelcase-keys: 6.2.2
      decamelize: 1.2.0
      decamelize-keys: 1.1.0
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9
    dev: true

  /merge-options/1.0.1:
    resolution: {integrity: sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=, tarball: merge-options/download/merge-options-1.0.1.tgz}
    engines: {node: '>=4'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /merge-stream/2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: merge-stream/download/merge-stream-2.0.0.tgz}
    dev: true

  /merge2/1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}
    dev: true

  /micromatch/3.1.0:
    resolution: {integrity: sha1-UQLU6vILaZfWAI46z+HESj+oFeI=, tarball: micromatch/download/micromatch-3.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 1.0.0
      extend-shallow: 2.0.1
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 5.1.0
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /micromatch/3.1.10:
    resolution: {integrity: sha1-cIWbyVyYQJUvNZoGij/En57PrCM=, tarball: micromatch/download/micromatch-3.1.10.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /micromatch/4.0.4:
    resolution: {integrity: sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k=, tarball: micromatch/download/micromatch-4.0.4.tgz}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.0
    dev: true

  /mime-db/1.50.0:
    resolution: {integrity: sha1-q9SslOmNPA4YUBbGerRdX95AwR8=, tarball: mime-db/download/mime-db-1.50.0.tgz}
    engines: {node: '>= 0.6'}
    dev: true

  /mime-types/2.1.33:
    resolution: {integrity: sha1-H6EqkERy+v0GjkjZ6EAfdNP3Dts=, tarball: mime-types/download/mime-types-2.1.33.tgz}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.50.0
    dev: true

  /mimic-fn/1.2.0:
    resolution: {integrity: sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=, tarball: mimic-fn/download/mimic-fn-1.2.0.tgz}
    engines: {node: '>=4'}
    dev: true

  /mimic-fn/2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /min-indent/1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=, tarball: min-indent/download/min-indent-1.0.1.tgz}
    engines: {node: '>=4'}
    dev: true

  /minimatch/3.0.4:
    resolution: {integrity: sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=, tarball: minimatch/download/minimatch-3.0.4.tgz}
    dependencies:
      brace-expansion: 1.1.11
    dev: true

  /minimist-options/4.1.0:
    resolution: {integrity: sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=, tarball: minimist-options/download/minimist-options-4.1.0.tgz}
    engines: {node: '>= 6'}
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3
    dev: true

  /minimist/1.2.5:
    resolution: {integrity: sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=, tarball: minimist/download/minimist-1.2.5.tgz}
    dev: true

  /mitt/3.0.0:
    resolution: {integrity: sha1-ae+b1cgP9vV0c+jYkybQHEFL4L0=, tarball: mitt/download/mitt-3.0.0.tgz}
    dev: false

  /mixin-deep/1.3.2:
    resolution: {integrity: sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=, tarball: mixin-deep/download/mixin-deep-1.3.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1
    dev: true

  /mkdirp/0.5.5:
    resolution: {integrity: sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=, tarball: mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1634135125547&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz}
    hasBin: true
    dependencies:
      minimist: registry.nlark.com/minimist/1.2.5
    dev: true

  /mkdirp/1.0.4:
    resolution: {integrity: sha1-PrXtYmInVteaXw4qIh3+utdcL34=, tarball: mkdirp/download/mkdirp-1.0.4.tgz?cache=0&sync_timestamp=1634135125547&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmkdirp%2Fdownload%2Fmkdirp-1.0.4.tgz}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /mlly/0.2.10:
    resolution: {integrity: sha1-ZFkCyXYdxrXe0XS45xcUf+UuSJM=, tarball: mlly/download/mlly-0.2.10.tgz}
    dependencies:
      import-meta-resolve: 1.1.1
    dev: true

  /mockjs/1.1.0:
    resolution: {integrity: sha1-5qDDeOkZBtuv8gkRzAJzs8fXWwY=, tarball: mockjs/download/mockjs-1.1.0.tgz}
    hasBin: true
    dependencies:
      commander: 8.2.0
    dev: false

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1632883299868&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fms%2Fdownload%2Fms-2.0.0.tgz}
    dev: true

  /ms/2.1.2:
    resolution: {integrity: sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=, tarball: ms/download/ms-2.1.2.tgz}
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: ms/download/ms-2.1.3.tgz?cache=0&sync_timestamp=1632883299868&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fms%2Fdownload%2Fms-2.1.3.tgz}
    dev: true

  /mute-stream/0.0.8:
    resolution: {integrity: sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=, tarball: mute-stream/download/mute-stream-0.0.8.tgz}
    dev: true

  /naming-style/1.0.1:
    resolution: {integrity: sha1-LIHzeKnk0nhhjCyKpVBrCyozA00=, registry: https://registry.npm.taobao.org/, tarball: https://registry.npm.taobao.org/naming-style/download/naming-style-1.0.1.tgz}
    dev: false

  /nanoid/3.1.30:
    resolution: {integrity: sha1-Y/k8xUjSoRPcXfvGO/oJ4rm2Q2I=, tarball: nanoid/download/nanoid-3.1.30.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /nanomatch/1.2.13:
    resolution: {integrity: sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=, tarball: nanomatch/download/nanomatch-1.2.13.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: natural-compare/download/natural-compare-1.4.0.tgz}
    dev: true

  /ndarray-ops/1.2.2:
    resolution: {integrity: sha1-WeiNLDKn7ryxvGkPrhQVeVV6YU4=, tarball: ndarray-ops/download/ndarray-ops-1.2.2.tgz}
    dependencies:
      cwise-compiler: 1.1.3
    dev: true

  /ndarray-pack/1.2.1:
    resolution: {integrity: sha1-jK6+qqJNXs9w/4YCBjeXfajuWFo=, tarball: ndarray-pack/download/ndarray-pack-1.2.1.tgz}
    dependencies:
      cwise-compiler: 1.1.3
      ndarray: 1.0.19
    dev: true

  /ndarray/1.0.19:
    resolution: {integrity: sha1-Z4W19d+li4PjGuWyoFjP0as/aU4=, tarball: ndarray/download/ndarray-1.0.19.tgz}
    dependencies:
      iota-array: 1.0.0
      is-buffer: 1.1.6
    dev: true

  /neo-async/2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=, tarball: neo-async/download/neo-async-2.6.2.tgz}
    dev: true

  /no-case/2.3.2:
    resolution: {integrity: sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=, tarball: no-case/download/no-case-2.3.2.tgz}
    dependencies:
      lower-case: 1.1.4
    dev: true

  /no-case/3.0.4:
    resolution: {integrity: sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=, tarball: no-case/download/no-case-3.0.4.tgz}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.3.1
    dev: true

  /node-bitmap/0.0.1:
    resolution: {integrity: sha1-GA6scAPgxwdhjvMTaPYvhLKmkJE=, tarball: node-bitmap/download/node-bitmap-0.0.1.tgz}
    engines: {node: '>=v0.6.5'}
    dev: true

  /node-plop/0.26.3:
    resolution: {integrity: sha1-1vp+cTk8i5QFE7qMSGj4qqbeqd8=, tarball: node-plop/download/node-plop-0.26.3.tgz}
    engines: {node: '>=8.9.4'}
    dependencies:
      '@babel/runtime-corejs3': 7.15.4
      '@types/inquirer': 6.5.0
      change-case: 3.1.0
      del: 5.1.0
      globby: 10.0.2
      handlebars: 4.7.7
      inquirer: 7.3.3
      isbinaryfile: 4.0.8
      lodash.get: 4.4.2
      mkdirp: 0.5.5
      resolve: 1.20.0
    dev: true

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=, tarball: normalize-package-data/download/normalize-package-data-2.5.0.tgz}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.20.0
      semver: 5.7.1
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-package-data/3.0.3:
    resolution: {integrity: sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=, tarball: normalize-package-data/download/normalize-package-data-3.0.3.tgz}
    engines: {node: '>=10'}
    dependencies:
      hosted-git-info: 4.0.2
      is-core-module: 2.8.0
      semver: 7.3.5
      validate-npm-package-license: 3.0.4
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: normalize-path/download/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-selector/0.2.0:
    resolution: {integrity: sha1-0LFF62kRicY6eNIB3E/bEpPvDAM=, tarball: normalize-selector/download/normalize-selector-0.2.0.tgz}
    dev: true

  /normalize-wheel-es/1.1.1:
    resolution: {integrity: sha1-qAlttqVvlDMtiE/Y6+2ojy/HlWk=, tarball: normalize-wheel-es/download/normalize-wheel-es-1.1.1.tgz}
    dev: false

  /npm-run-path/4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=, tarball: npm-run-path/download/npm-run-path-4.0.1.tgz}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /nprogress/0.2.0:
    resolution: {integrity: sha1-y480xTIT2JVyP8urkH6UIq28r7E=, tarball: nprogress/download/nprogress-0.2.0.tgz}
    dev: false

  /nth-check/2.0.1:
    resolution: {integrity: sha1-Lv4WL1w9oGoolZ+9PbddvuqfD8I=, tarball: nth-check/download/nth-check-2.0.1.tgz}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /oauth-sign/0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=, tarball: oauth-sign/download/oauth-sign-0.9.0.tgz}
    dev: true

  /obj-extend/0.1.0:
    resolution: {integrity: sha1-u0SKR3X7les0p4H5CLusLfI9u1s=, tarball: obj-extend/download/obj-extend-0.1.0.tgz}
    dev: true

  /object-assign/4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /object-copy/0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=, tarball: object-copy/download/object-copy-0.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2
    dev: true

  /object-inspect/1.11.0:
    resolution: {integrity: sha1-nc6xRs7dQUig2eUauI00z1CZIrE=, tarball: object-inspect/download/object-inspect-1.11.0.tgz}

  /object-visit/1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=, tarball: object-visit/download/object-visit-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /object.defaults/1.1.0:
    resolution: {integrity: sha1-On+GgzS0B96gbaFtiNXNKeQ1/s8=, tarball: object.defaults/download/object.defaults-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-each: 1.0.1
      array-slice: 1.1.0
      for-own: 1.0.0
      isobject: 3.0.1
    dev: true

  /object.map/1.0.1:
    resolution: {integrity: sha1-z4Plncj8wK1fQlDh94s7gb2AHTc=, tarball: object.map/download/object.map-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-own: 1.0.0
      make-iterator: 1.0.1
    dev: true

  /object.pick/1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=, tarball: object.pick/download/object.pick-1.3.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /omggif/1.0.10:
    resolution: {integrity: sha1-3ar5DUpC9TLp58s6lezdR/F8exk=, tarball: omggif/download/omggif-1.0.10.tgz}
    dev: true

  /on-finished/2.3.0:
    resolution: {integrity: sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=, tarball: on-finished/download/on-finished-2.3.0.tgz}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: true

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: once/download/once-1.4.0.tgz}
    dependencies:
      wrappy: 1.0.2
    dev: true

  /onetime/2.0.1:
    resolution: {integrity: sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=, tarball: onetime/download/onetime-2.0.1.tgz}
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: 1.2.0
    dev: true

  /onetime/5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /optionator/0.9.1:
    resolution: {integrity: sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=, tarball: optionator/download/optionator-0.9.1.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.3
    dev: true

  /ora/3.4.0:
    resolution: {integrity: sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=, tarball: ora/download/ora-3.4.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.6.1
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1
    dev: true

  /os-tmpdir/1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=, tarball: os-tmpdir/download/os-tmpdir-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /p-limit/2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=, tarball: p-limit/download/p-limit-2.3.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: true

  /p-locate/4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=, tarball: p-locate/download/p-locate-4.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-map/3.0.0:
    resolution: {integrity: sha1-1wTZr4orpoTiYA2aIVmD1BQal50=, tarball: p-map/download/p-map-3.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-map/4.0.0:
    resolution: {integrity: sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=, tarball: p-map/download/p-map-4.0.0.tgz?cache=0&sync_timestamp=1635935258330&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-map%2Fdownload%2Fp-map-4.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-try/2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=, tarball: p-try/download/p-try-2.2.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /param-case/2.1.1:
    resolution: {integrity: sha1-35T9jPZTHs915r75oIWPvHK+Ikc=, tarball: param-case/download/param-case-2.1.1.tgz}
    dependencies:
      no-case: 2.3.2
    dev: true

  /param-case/3.0.4:
    resolution: {integrity: sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=, tarball: param-case/download/param-case-3.0.4.tgz}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.3.1
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-data-uri/0.2.0:
    resolution: {integrity: sha1-vwTYUd1ch7CrI45dAazklLYEtMk=, tarball: parse-data-uri/download/parse-data-uri-0.2.0.tgz}
    dependencies:
      data-uri-to-buffer: 0.0.3
    dev: true

  /parse-filepath/1.0.2:
    resolution: {integrity: sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE=, tarball: parse-filepath/download/parse-filepath-1.0.2.tgz}
    engines: {node: '>=0.8'}
    dependencies:
      is-absolute: 1.0.0
      map-cache: 0.2.2
      path-root: 0.1.1
    dev: true

  /parse-json/5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: parse-json/download/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.15.8
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.1.6
    dev: true

  /parse-passwd/1.0.0:
    resolution: {integrity: sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=, tarball: parse-passwd/download/parse-passwd-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /parseurl/1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=, tarball: parseurl/download/parseurl-1.3.3.tgz}
    engines: {node: '>= 0.8'}
    dev: true

  /pascal-case/2.0.1:
    resolution: {integrity: sha1-LVeNNFX2YNpl7KGO+VtODekSdh4=, tarball: pascal-case/download/pascal-case-2.0.1.tgz}
    dependencies:
      camel-case: 3.0.0
      upper-case-first: 1.1.2
    dev: true

  /pascal-case/3.1.2:
    resolution: {integrity: sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=, tarball: pascal-case/download/pascal-case-3.1.2.tgz}
    dependencies:
      no-case: 3.0.4
      tslib: 2.3.1
    dev: true

  /pascalcase/0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=, tarball: pascalcase/download/pascalcase-0.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-browserify/1.0.1:
    resolution: {integrity: sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=, tarball: path-browserify/download/path-browserify-1.0.1.tgz}
    dev: false

  /path-case/2.1.1:
    resolution: {integrity: sha1-lLgDfDctP+KQbkZbtF4l0ibo7qU=, tarball: path-case/download/path-case-2.1.1.tgz}
    dependencies:
      no-case: 2.3.2
    dev: true

  /path-exists/4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: path-parse/download/path-parse-1.0.7.tgz}
    dev: true

  /path-root-regex/0.1.2:
    resolution: {integrity: sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0=, tarball: path-root-regex/download/path-root-regex-0.1.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-root/0.1.1:
    resolution: {integrity: sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=, tarball: path-root/download/path-root-0.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-root-regex: 0.1.2
    dev: true

  /path-to-regexp/6.2.0:
    resolution: {integrity: sha1-97OAMzYQTDRoia3s5hRmkjBkXzg=, tarball: path-to-regexp/download/path-to-regexp-6.2.0.tgz}

  /path-type/4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=, tarball: path-type/download/path-type-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /performance-now/2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: performance-now/download/performance-now-2.1.0.tgz}
    dev: true

  /picocolors/0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=, tarball: picocolors/download/picocolors-0.2.1.tgz}

  /picocolors/1.0.0:
    resolution: {integrity: sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=, tarball: picocolors/download/picocolors-1.0.0.tgz}
    dev: true

  /picomatch/2.3.0:
    resolution: {integrity: sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=, tarball: picomatch/download/picomatch-2.3.0.tgz}
    engines: {node: '>=8.6'}
    dev: true

  /pixelsmith/2.5.0:
    resolution: {integrity: sha1-ez5SLgjqh3vXC63xWb3D7Ffh6EE=, tarball: pixelsmith/download/pixelsmith-2.5.0.tgz}
    engines: {node: '>= 8.0.0'}
    dependencies:
      async: registry.nlark.com/async/0.9.2
      concat-stream: 1.5.2
      get-pixels: 3.3.3
      mime-types: 2.1.33
      ndarray: 1.0.19
      obj-extend: 0.1.0
      save-pixels: 2.3.6
      vinyl-file: 1.3.0
    dev: true

  /plop/2.7.6:
    resolution: {integrity: sha1-H6U2DNWwTpkyzmd7tr1EdQ2Xrmc=, tarball: plop/download/plop-2.7.6.tgz}
    engines: {node: '>=8.9.4'}
    hasBin: true
    dependencies:
      '@types/liftoff': 2.5.1
      chalk: 1.1.3
      interpret: 1.4.0
      liftoff: 2.5.0
      minimist: 1.2.5
      node-plop: 0.26.3
      ora: 3.4.0
      v8flags: 2.1.1
    dev: true

  /pngjs-nozlib/1.0.0:
    resolution: {integrity: sha1-nmTWAs/pzOTZ1Zl9BodCmnPwt9c=, tarball: pngjs-nozlib/download/pngjs-nozlib-1.0.0.tgz}
    engines: {iojs: '>= 1.0.0', node: '>=0.10.0'}
    dev: true

  /pngjs/3.4.0:
    resolution: {integrity: sha1-mcp9clll+2VYFOr2XzjxK72/VV8=, tarball: pngjs/download/pngjs-3.4.0.tgz}
    engines: {node: '>=4.0.0'}
    dev: true

  /posix-character-classes/0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=, tarball: posix-character-classes/download/posix-character-classes-0.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /postcss-html/1.3.0:
    resolution: {integrity: sha512-ewbwd7OGW4dLsErtvZH9HpVMEcXnlhYSzKsr7MepGlOT8imHTIZ/+pdfEruLS+hTYapLTQAWDnoQcJpsYU4uRw==, tarball: postcss-html/download/postcss-html-1.3.0.tgz}
    engines: {node: ^12 || >=14}
    dependencies:
      htmlparser2: 7.1.2
      postcss: 8.4.1
      postcss-safe-parser: 6.0.0_postcss@8.4.1
    dev: true

  /postcss-media-query-parser/0.2.3:
    resolution: {integrity: sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=, tarball: postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz}
    dev: true

  /postcss-prefix-selector/1.13.0:
    resolution: {integrity: sha1-E2w+uEjtrO2QQXuxA4PeVsB1V/Y=, tarball: postcss-prefix-selector/download/postcss-prefix-selector-1.13.0.tgz}
    dependencies:
      postcss: 8.4.1
    dev: true

  /postcss-resolve-nested-selector/0.1.1:
    resolution: {integrity: sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=, tarball: postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz}
    dev: true

  /postcss-safe-parser/6.0.0_postcss@8.3.11:
    resolution: {integrity: sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=, tarball: postcss-safe-parser/download/postcss-safe-parser-6.0.0.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.3.3
    dependencies:
      postcss: 8.3.11
    dev: true

  /postcss-safe-parser/6.0.0_postcss@8.4.1:
    resolution: {integrity: sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=, tarball: postcss-safe-parser/download/postcss-safe-parser-6.0.0.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.3.3
    dependencies:
      postcss: 8.4.1
    dev: true

  /postcss-scss/4.0.2:
    resolution: {integrity: sha1-Od3MCrMvFV1asyjukTU9Z6UtU3s=, tarball: postcss-scss/download/postcss-scss-4.0.2.tgz?cache=0&sync_timestamp=1635496549918&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-scss%2Fdownload%2Fpostcss-scss-4.0.2.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.3.3
    dev: true

  /postcss-selector-parser/6.0.6:
    resolution: {integrity: sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo=, tarball: postcss-selector-parser/download/postcss-selector-parser-6.0.6.tgz}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-value-parser/4.1.0:
    resolution: {integrity: sha1-RD9qIM7WSBor2k+oUypuVdeJoss=, tarball: postcss-value-parser/download/postcss-value-parser-4.1.0.tgz}
    dev: true

  /postcss/5.2.18:
    resolution: {integrity: sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=, tarball: postcss/download/postcss-5.2.18.tgz}
    engines: {node: '>=0.12'}
    dependencies:
      chalk: 1.1.3
      js-base64: 2.6.4
      source-map: 0.5.7
      supports-color: 3.2.3
    dev: true

  /postcss/8.3.11:
    resolution: {integrity: sha1-w77KfqgRzV4cSj7G0udZnvH4+Fg=, tarball: postcss/download/postcss-8.3.11.tgz}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.1.30
      picocolors: 1.0.0
      source-map-js: 0.6.2
    dev: true

  /postcss/8.3.9:
    resolution: {integrity: sha1-mHVMqgbE7p61nMSL0HO7a9NDfDE=, tarball: postcss/download/postcss-8.3.9.tgz}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.1.30
      picocolors: 0.2.1
      source-map-js: 0.6.2

  /postcss/8.4.1:
    resolution: {integrity: sha512-WqLs/TTzXdG+/A4ZOOK9WDZiikrRaiA+eoEb/jz2DT9KUhMNHgP7yKPO8vwi62ZCsb703Gwb7BMZwDzI54Y2Ag==, tarball: postcss/download/postcss-8.4.1.tgz}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.1.30
      picocolors: 1.0.0
      source-map-js: 1.0.1
    dev: true

  /posthtml-parser/0.2.1:
    resolution: {integrity: sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=, tarball: posthtml-parser/download/posthtml-parser-0.2.1.tgz}
    dependencies:
      htmlparser2: 3.10.1
      isobject: 2.1.0
    dev: true

  /posthtml-rename-id/1.0.12:
    resolution: {integrity: sha1-z39us3FGvxr6wx5o8YxswZrmFDM=, tarball: posthtml-rename-id/download/posthtml-rename-id-1.0.12.tgz}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /posthtml-render/1.4.0:
    resolution: {integrity: sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=, tarball: posthtml-render/download/posthtml-render-1.4.0.tgz}
    engines: {node: '>=10'}
    dev: true

  /posthtml-svg-mode/1.0.3:
    resolution: {integrity: sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=, tarball: posthtml-svg-mode/download/posthtml-svg-mode-1.0.3.tgz}
    dependencies:
      merge-options: 1.0.1
      posthtml: 0.9.2
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /posthtml/0.9.2:
    resolution: {integrity: sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=, tarball: posthtml/download/posthtml-0.9.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /prelude-ls/1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: prelude-ls/download/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}
    dev: true

  /process-nextick-args/1.0.7:
    resolution: {integrity: sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M=, tarball: process-nextick-args/download/process-nextick-args-1.0.7.tgz}
    dev: true

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=, tarball: process-nextick-args/download/process-nextick-args-2.0.1.tgz}
    dev: true

  /progress/2.0.3:
    resolution: {integrity: sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=, tarball: progress/download/progress-2.0.3.tgz}
    engines: {node: '>=0.4.0'}
    dev: true

  /psl/1.8.0:
    resolution: {integrity: sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=, tarball: psl/download/psl-1.8.0.tgz}
    dev: true

  /punycode/2.1.1:
    resolution: {integrity: sha1-tYsBCsQMIsVldhbI0sLALHv0eew=, tarball: punycode/download/punycode-2.1.1.tgz}
    engines: {node: '>=6'}
    dev: true

  /qs/6.10.1:
    resolution: {integrity: sha1-STFIL6jWR6Wqt5nFJx0hM7mB+2o=, tarball: qs/download/qs-6.10.1.tgz}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4

  /qs/6.5.2:
    resolution: {integrity: sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=, tarball: qs/download/qs-6.5.2.tgz}
    engines: {node: '>=0.6'}
    dev: true

  /query-string/4.3.4:
    resolution: {integrity: sha1-u7aTucqRXCMlFbIosaArYJBD2+s=, tarball: query-string/download/query-string-4.3.4.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: true

  /queue-microtask/1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: queue-microtask/download/queue-microtask-1.2.3.tgz}
    dev: true

  /quick-lru/4.0.1:
    resolution: {integrity: sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=, tarball: quick-lru/download/quick-lru-4.0.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /read-pkg-up/7.0.1:
    resolution: {integrity: sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=, tarball: read-pkg-up/download/read-pkg-up-7.0.1.tgz}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1
    dev: true

  /read-pkg/5.2.0:
    resolution: {integrity: sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=, tarball: read-pkg/download/read-pkg-5.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.1
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /readable-stream/1.0.34:
    resolution: {integrity: sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=, tarball: readable-stream/download/readable-stream-1.0.34.tgz}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31
    dev: true

  /readable-stream/1.1.14:
    resolution: {integrity: sha1-fPTFTvZI44EwhMY23SB54WbAgdk=, tarball: readable-stream/download/readable-stream-1.1.14.tgz}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31
    dev: true

  /readable-stream/2.0.6:
    resolution: {integrity: sha1-j5A0HmilPMySh4jaz80Rs265t44=, tarball: readable-stream/download/readable-stream-2.0.6.tgz}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 1.0.7
      string_decoder: 0.10.31
      util-deprecate: registry.nlark.com/util-deprecate/1.0.2
    dev: true

  /readable-stream/2.3.7:
    resolution: {integrity: sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=, tarball: readable-stream/download/readable-stream-2.3.7.tgz}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: registry.nlark.com/safe-buffer/5.1.2
      string_decoder: 1.1.1
      util-deprecate: registry.nlark.com/util-deprecate/1.0.2
    dev: true

  /readable-stream/3.6.0:
    resolution: {integrity: sha1-M3u9o63AcGvT4CRCaihtS0sskZg=, tarball: readable-stream/download/readable-stream-3.6.0.tgz}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: registry.nlark.com/util-deprecate/1.0.2
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=, tarball: readdirp/download/readdirp-3.6.0.tgz}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.0
    dev: true

  /rechoir/0.6.2:
    resolution: {integrity: sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=, tarball: rechoir/download/rechoir-0.6.2.tgz}
    engines: {node: '>= 0.10'}
    dependencies:
      resolve: 1.20.0
    dev: true

  /redent/3.0.0:
    resolution: {integrity: sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=, tarball: redent/download/redent-3.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0
    dev: true

  /regenerator-runtime/0.13.9:
    resolution: {integrity: sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I=, tarball: regenerator-runtime/download/regenerator-runtime-0.13.9.tgz}
    dev: true

  /regex-not/1.0.2:
    resolution: {integrity: sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=, tarball: regex-not/download/regex-not-1.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0
    dev: true

  /regexpp/3.2.0:
    resolution: {integrity: sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=, tarball: regexpp/download/regexpp-3.2.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /relateurl/0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=, tarball: relateurl/download/relateurl-0.2.7.tgz}
    engines: {node: '>= 0.10'}
    dev: true

  /repeat-element/1.1.4:
    resolution: {integrity: sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=, tarball: repeat-element/download/repeat-element-1.1.4.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /repeat-string/1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=, tarball: repeat-string/download/repeat-string-1.6.1.tgz}
    engines: {node: '>=0.10'}
    dev: true

  /replace-ext/0.0.1:
    resolution: {integrity: sha1-KbvZIHinOfC8zitO5B6DeVNSKSQ=, tarball: replace-ext/download/replace-ext-0.0.1.tgz}
    engines: {node: '>= 0.4'}
    dev: true

  /request/2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=, tarball: request/download/request-2.88.2.tgz}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.11.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.33
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.2
      safe-buffer: registry.nlark.com/safe-buffer/5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0
    dev: true

  /require-from-string/2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=, tarball: require-from-string/download/require-from-string-2.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /resolve-dir/1.0.1:
    resolution: {integrity: sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=, tarball: resolve-dir/download/resolve-dir-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: 2.0.2
      global-modules: 1.0.0
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}
    dev: true

  /resolve-from/5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=, tarball: resolve-from/download/resolve-from-5.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /resolve-url/0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=, tarball: resolve-url/download/resolve-url-0.2.1.tgz}
    deprecated: https://github.com/lydell/resolve-url#deprecated
    dev: true

  /resolve/1.20.0:
    resolution: {integrity: sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=, tarball: resolve/download/resolve-1.20.0.tgz}
    dependencies:
      is-core-module: 2.8.0
      path-parse: 1.0.7
    dev: true

  /restore-cursor/2.0.0:
    resolution: {integrity: sha1-n37ih/gv0ybU/RYpI9YhKe7g368=, tarball: restore-cursor/download/restore-cursor-2.0.0.tgz}
    engines: {node: '>=4'}
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.5
    dev: true

  /restore-cursor/3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=, tarball: restore-cursor/download/restore-cursor-3.1.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.5
    dev: true

  /ret/0.1.15:
    resolution: {integrity: sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=, tarball: ret/download/ret-0.1.15.tgz}
    engines: {node: '>=0.12'}
    dev: true

  /reusify/1.0.4:
    resolution: {integrity: sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=, tarball: reusify/download/reusify-1.0.4.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rimraf/3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=, tarball: rimraf/download/rimraf-3.0.2.tgz?cache=0&sync_timestamp=1632883295571&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Frimraf%2Fdownload%2Frimraf-3.0.2.tgz}
    hasBin: true
    dependencies:
      glob: 7.2.0
    dev: true

  /rollup/2.58.0:
    resolution: {integrity: sha1-pkOYM2Xnv39bfGKoMxuYO3xMZ/s=, tarball: rollup/download/rollup-2.58.0.tgz}
    engines: {node: '>=10.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /run-async/2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=, tarball: run-async/download/run-async-2.4.1.tgz}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: run-parallel/download/run-parallel-1.2.0.tgz}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /rxjs/6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=, tarball: rxjs/download/rxjs-6.6.7.tgz}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: true

  /rxjs/7.4.0:
    resolution: {integrity: sha1-oSpE1+6/AW9f8kQbh/KMmlHOvGg=, tarball: rxjs/download/rxjs-7.4.0.tgz}
    dependencies:
      tslib: 2.1.0
    dev: true

  /safe-regex/1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=, tarball: safe-regex/download/safe-regex-1.1.0.tgz}
    dependencies:
      ret: 0.1.15
    dev: true

  /sass/1.43.5:
    resolution: {integrity: sha512-WuNm+eAryMgQluL7Mbq9M4EruyGGMyal7Lu58FfnRMVWxgUzIvI7aSn60iNt3kn5yZBMR7G84fAGDcwqOF5JOg==, tarball: sass/download/sass-1.43.5.tgz}
    engines: {node: '>=8.9.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.2
    dev: true

  /save-pixels/2.3.6:
    resolution: {integrity: sha1-2fQKL2s3T5xh0U8bfe8lkdt/t4A=, tarball: save-pixels/download/save-pixels-2.3.6.tgz}
    dependencies:
      contentstream: 1.0.0
      gif-encoder: 0.4.3
      jpeg-js: 0.4.3
      ndarray: 1.0.19
      ndarray-ops: 1.2.2
      pngjs-nozlib: 1.0.0
      through: 2.3.8
    dev: true

  /screenfull/6.0.0:
    resolution: {integrity: sha1-b383v+GcXuUJMaxDjB/jgNW1/0Q=, tarball: screenfull/download/screenfull-6.0.0.tgz}
    engines: {node: ^14.13.1 || >=16.0.0}
    dev: false

  /secure-compare/3.0.1:
    resolution: {integrity: sha1-8aAymzCLIh+uN7mXTz1XjQypmeM=, tarball: secure-compare/download/secure-compare-3.0.1.tgz}
    dev: true

  /semver/5.0.3:
    resolution: {integrity: sha1-d0Zt5YnNXTyV8TiqeLxWmjy10no=, tarball: semver/download/semver-5.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.0.3.tgz}
    hasBin: true
    dev: true

  /semver/5.7.1:
    resolution: {integrity: sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=, tarball: semver/download/semver-5.7.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz}
    hasBin: true
    dev: true

  /semver/7.3.5:
    resolution: {integrity: sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=, tarball: semver/download/semver-7.3.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /sentence-case/2.1.1:
    resolution: {integrity: sha1-H24t2jnBaL+S0T+G1KkYkz9mftQ=, tarball: sentence-case/download/sentence-case-2.1.1.tgz}
    dependencies:
      no-case: 2.3.2
      upper-case-first: 1.1.2
    dev: true

  /set-value/2.0.1:
    resolution: {integrity: sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=, tarball: set-value/download/set-value-2.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0
    dev: true

  /shebang-command/2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /side-channel/1.0.4:
    resolution: {integrity: sha1-785cj9wQTudRslxY1CkAEfpeos8=, tarball: side-channel/download/side-channel-1.0.4.tgz}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.1
      object-inspect: 1.11.0

  /signal-exit/3.0.5:
    resolution: {integrity: sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8=, tarball: signal-exit/download/signal-exit-3.0.5.tgz}
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=, tarball: slash/download/slash-3.0.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi/3.0.0:
    resolution: {integrity: sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=, tarball: slice-ansi/download/slice-ansi-3.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi/4.0.0:
    resolution: {integrity: sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=, tarball: slice-ansi/download/slice-ansi-4.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi/5.0.0:
    resolution: {integrity: sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=, tarball: slice-ansi/download/slice-ansi-5.0.0.tgz}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.1.0
      is-fullwidth-code-point: 4.0.0
    dev: true

  /snake-case/2.1.0:
    resolution: {integrity: sha1-Qb2xtz8w7GagTU4srRt2OH1NbZ8=, tarball: snake-case/download/snake-case-2.1.0.tgz}
    dependencies:
      no-case: 2.3.2
    dev: true

  /snapdragon-node/2.1.1:
    resolution: {integrity: sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=, tarball: snapdragon-node/download/snapdragon-node-2.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1
    dev: true

  /snapdragon-util/3.0.1:
    resolution: {integrity: sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=, tarball: snapdragon-util/download/snapdragon-util-3.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /snapdragon/0.8.2:
    resolution: {integrity: sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=, tarball: snapdragon/download/snapdragon-0.8.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    dev: true

  /source-map-js/0.6.2:
    resolution: {integrity: sha1-C7XeYxtBz72mz7qL0FqA79/SOF4=, tarball: source-map-js/download/source-map-js-0.6.2.tgz}
    engines: {node: '>=0.10.0'}

  /source-map-js/1.0.1:
    resolution: {integrity: sha512-4+TN2b3tqOCd/kaGRJ/sTYA0tR0mdXx26ipdolxcwtJVqEnqNYvlCAt1q3ypy4QMlYus+Zh34RNtYLoq2oQ4IA==, tarball: source-map-js/download/source-map-js-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map-resolve/0.5.3:
    resolution: {integrity: sha1-GQhmvs51U+H48mei7oLGBrVQmho=, tarball: source-map-resolve/download/source-map-resolve-0.5.3.tgz}
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.0
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0
    dev: true

  /source-map-support/0.5.20:
    resolution: {integrity: sha1-EhZgifj15ejFaSazd2Mzkt0stsk=, tarball: source-map-support/download/source-map-support-0.5.20.tgz}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map-url/0.4.1:
    resolution: {integrity: sha1-CvZmBadFpaL5HPG7+KevvCg97FY=, tarball: source-map-url/download/source-map-url-0.4.1.tgz}
    dev: true

  /source-map/0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=, tarball: source-map/download/source-map-0.5.7.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map/0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=, tarball: source-map/download/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}

  /sourcemap-codec/1.4.8:
    resolution: {integrity: sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=, tarball: sourcemap-codec/download/sourcemap-codec-1.4.8.tgz}

  /spdx-correct/3.1.1:
    resolution: {integrity: sha1-3s6BrJweZxPl99G28X1Gj6U9iak=, tarball: spdx-correct/download/spdx-correct-3.1.1.tgz}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.10
    dev: true

  /spdx-exceptions/2.3.0:
    resolution: {integrity: sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=, tarball: spdx-exceptions/download/spdx-exceptions-2.3.0.tgz}
    dev: true

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=, tarball: spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.10
    dev: true

  /spdx-license-ids/3.0.10:
    resolution: {integrity: sha1-DZvszN5wA9bGWNSH3UijLwvzAUs=, tarball: spdx-license-ids/download/spdx-license-ids-3.0.10.tgz}
    dev: true

  /specificity/0.4.1:
    resolution: {integrity: sha1-qrXmRQEtsIuhguFRFlc40AiHsBk=, tarball: specificity/download/specificity-0.4.1.tgz}
    hasBin: true
    dev: true

  /split-string/3.1.0:
    resolution: {integrity: sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=, tarball: split-string/download/split-string-3.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
    dev: true

  /sprintf-js/1.1.2:
    resolution: {integrity: sha1-2hdlJiv4wPVxdJ8q1sJjACB65nM=, tarball: sprintf-js/download/sprintf-js-1.1.2.tgz}
    dev: true

  /spritesheet-templates/10.5.2:
    resolution: {integrity: sha1-t1JtHNzt48LL/MspY76b7GBm7Ek=, tarball: spritesheet-templates/download/spritesheet-templates-10.5.2.tgz}
    engines: {node: '>= 8.0.0'}
    dependencies:
      handlebars: 4.7.7
      handlebars-layouts: 3.1.4
      json-content-demux: 0.1.4
      underscore: 1.13.1
      underscore.string: 3.3.5
    dev: true

  /spritesmith/3.4.0:
    resolution: {integrity: sha1-XPoAQnjJByq0kAoM2AAUHHzyluI=, tarball: spritesmith/download/spritesmith-3.4.0.tgz}
    engines: {node: '>= 4.0.0'}
    dependencies:
      concat-stream: 1.5.2
      layout: 2.2.0
      pixelsmith: 2.5.0
      semver: 5.0.3
      through2: 2.0.5
    dev: true

  /sshpk/1.16.1:
    resolution: {integrity: sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=, tarball: sshpk/download/sshpk-1.16.1.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.4
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: registry.nlark.com/safer-buffer/2.1.2
      tweetnacl: 0.14.5
    dev: true

  /stable/0.1.8:
    resolution: {integrity: sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=, tarball: stable/download/stable-0.1.8.tgz}
    dev: true

  /static-extend/0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=, tarball: static-extend/download/static-extend-0.1.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0
    dev: true

  /statuses/1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=, tarball: statuses/download/statuses-1.5.0.tgz}
    engines: {node: '>= 0.6'}
    dev: true

  /strict-uri-encode/1.1.0:
    resolution: {integrity: sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=, tarball: strict-uri-encode/download/strict-uri-encode-1.1.0.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /string-argv/0.3.1:
    resolution: {integrity: sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=, tarball: string-argv/download/string-argv-0.3.1.tgz}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-width/4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width/5.0.1:
    resolution: {integrity: sha1-DYFYM1ps/Y65Xam2smLOMUoDb/0=, tarball: string-width/download/string-width-5.0.1.tgz}
    engines: {node: '>=12'}
    dependencies:
      emoji-regex: 9.2.2
      is-fullwidth-code-point: 4.0.0
      strip-ansi: 7.0.1
    dev: true

  /string_decoder/0.10.31:
    resolution: {integrity: sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=, tarball: string_decoder/download/string_decoder-0.10.31.tgz}
    dev: true

  /string_decoder/1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=, tarball: string_decoder/download/string_decoder-1.1.1.tgz}
    dependencies:
      safe-buffer: registry.nlark.com/safe-buffer/5.1.2
    dev: true

  /string_decoder/1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=, tarball: string_decoder/download/string_decoder-1.3.0.tgz}
    dependencies:
      safe-buffer: registry.nlark.com/safe-buffer/5.2.1
    dev: true

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=, tarball: strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1632883295900&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi/5.2.0:
    resolution: {integrity: sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=, tarball: strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1632883295900&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.0
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: strip-ansi/download/strip-ansi-6.0.1.tgz?cache=0&sync_timestamp=1632883295900&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi/7.0.1:
    resolution: {integrity: sha1-YXQKCM42th5Q5lZT8HBg0ACXX7I=, tarball: strip-ansi/download/strip-ansi-7.0.1.tgz?cache=0&sync_timestamp=1632883295900&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-7.0.1.tgz}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.0.1
    dev: true

  /strip-bom-stream/1.0.0:
    resolution: {integrity: sha1-5xRDmFd9Uaa+0PoZlPoF9D/ZiO4=, tarball: strip-bom-stream/download/strip-bom-stream-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      first-chunk-stream: 1.0.0
      strip-bom: 2.0.0
    dev: true

  /strip-bom/2.0.0:
    resolution: {integrity: sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=, tarball: strip-bom/download/strip-bom-2.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-utf8: 0.2.1
    dev: true

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=, tarball: strip-final-newline/download/strip-final-newline-2.0.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /strip-indent/3.0.0:
    resolution: {integrity: sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=, tarball: strip-indent/download/strip-indent-3.0.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      min-indent: 1.0.1
    dev: true

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: strip-json-comments/download/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /style-search/0.1.0:
    resolution: {integrity: sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=, tarball: style-search/download/style-search-0.1.0.tgz}
    dev: true

  /stylelint-config-html/1.0.0_c1fe332c2f0bd1acbb28be685cc2d480:
    resolution: {integrity: sha1-FyrLmW7kyFToxnD8/3f/DZjTvsI=, tarball: stylelint-config-html/download/stylelint-config-html-1.0.0.tgz}
    engines: {node: ^12 || >=14}
    peerDependencies:
      postcss-html: ^1.0.0
      stylelint: '>=14.0.0'
    dependencies:
      postcss-html: 1.3.0
      stylelint: 14.1.0
    dev: true

  /stylelint-config-recommended-scss/5.0.2_stylelint@14.1.0:
    resolution: {integrity: sha512-b14BSZjcwW0hqbzm9b0S/ScN2+3CO3O4vcMNOw2KGf8lfVSwJ4p5TbNEXKwKl1+0FMtgRXZj6DqVUe/7nGnuBg==, tarball: stylelint-config-recommended-scss/download/stylelint-config-recommended-scss-5.0.2.tgz}
    peerDependencies:
      stylelint: ^14.0.0
    dependencies:
      postcss-scss: 4.0.2
      stylelint: 14.1.0
      stylelint-config-recommended: 6.0.0_stylelint@14.1.0
      stylelint-scss: 4.0.0_stylelint@14.1.0
    transitivePeerDependencies:
      - postcss
    dev: true

  /stylelint-config-recommended-vue/1.0.0_c1fe332c2f0bd1acbb28be685cc2d480:
    resolution: {integrity: sha1-XNL3GQzc23wAYgTTTn1elrtFFb4=, tarball: stylelint-config-recommended-vue/download/stylelint-config-recommended-vue-1.0.0.tgz}
    engines: {node: ^12 || >=14}
    peerDependencies:
      postcss-html: ^1.0.0
      stylelint: '>=14.0.0'
    dependencies:
      postcss-html: 1.3.0
      stylelint: 14.1.0
      stylelint-config-html: 1.0.0_c1fe332c2f0bd1acbb28be685cc2d480
      stylelint-config-recommended: 6.0.0_stylelint@14.1.0
    dev: true

  /stylelint-config-recommended/6.0.0_stylelint@14.1.0:
    resolution: {integrity: sha1-/SUjoyKDYAWtm/Rz0+VTRxnAn50=, tarball: stylelint-config-recommended/download/stylelint-config-recommended-6.0.0.tgz}
    peerDependencies:
      stylelint: ^14.0.0
    dependencies:
      stylelint: 14.1.0
    dev: true

  /stylelint-config-standard/24.0.0_stylelint@14.1.0:
    resolution: {integrity: sha512-+RtU7fbNT+VlNbdXJvnjc3USNPZRiRVp/d2DxOF/vBDDTi0kH5RX2Ny6errdtZJH3boO+bmqIYEllEmok4jiuw==, tarball: stylelint-config-standard/download/stylelint-config-standard-24.0.0.tgz}
    peerDependencies:
      stylelint: ^14.0.0
    dependencies:
      stylelint: 14.1.0
      stylelint-config-recommended: 6.0.0_stylelint@14.1.0
    dev: true

  /stylelint-scss/4.0.0_stylelint@14.1.0:
    resolution: {integrity: sha1-SQHO2SucaON2SXmaOd771fKsW80=, tarball: stylelint-scss/download/stylelint-scss-4.0.0.tgz}
    peerDependencies:
      stylelint: ^14.0.0
    dependencies:
      lodash: registry.nlark.com/lodash/4.17.21
      postcss-media-query-parser: registry.nlark.com/postcss-media-query-parser/0.2.3
      postcss-resolve-nested-selector: registry.nlark.com/postcss-resolve-nested-selector/0.1.1
      postcss-selector-parser: registry.nlark.com/postcss-selector-parser/6.0.6
      postcss-value-parser: registry.nlark.com/postcss-value-parser/4.1.0
      stylelint: 14.1.0
    dev: true

  /stylelint/14.1.0:
    resolution: {integrity: sha512-IedkssuNVA11+v++2PIV2OHOU5A3SfRcXVi56vZVSsMhGrgtwmmit69jeM+08/Tun5DTBe7BuH1Zp1mMLmtKLA==, tarball: stylelint/download/stylelint-14.1.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dependencies:
      balanced-match: 2.0.0
      cosmiconfig: 7.0.1
      debug: 4.3.2
      execall: 2.0.0
      fast-glob: 3.2.7
      fastest-levenshtein: 1.0.12
      file-entry-cache: 6.0.1
      get-stdin: 8.0.0
      global-modules: 2.0.0
      globby: 11.0.4
      globjoin: 0.1.4
      html-tags: 3.1.0
      ignore: 5.1.9
      import-lazy: 4.0.0
      imurmurhash: 0.1.4
      is-plain-object: 5.0.0
      known-css-properties: 0.23.0
      mathml-tag-names: 2.1.3
      meow: 9.0.0
      micromatch: 4.0.4
      normalize-path: 3.0.0
      normalize-selector: 0.2.0
      picocolors: 1.0.0
      postcss: 8.3.11
      postcss-media-query-parser: 0.2.3
      postcss-resolve-nested-selector: 0.1.1
      postcss-safe-parser: 6.0.0_postcss@8.3.11
      postcss-selector-parser: 6.0.6
      postcss-value-parser: 4.1.0
      resolve-from: 5.0.0
      specificity: 0.4.1
      string-width: 4.2.3
      strip-ansi: 6.0.1
      style-search: 0.1.0
      svg-tags: 1.0.0
      table: 6.7.3
      v8-compile-cache: 2.3.0
      write-file-atomic: 3.0.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=, tarball: supports-color/download/supports-color-2.0.0.tgz}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color/3.2.3:
    resolution: {integrity: sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=, tarball: supports-color/download/supports-color-3.2.3.tgz}
    engines: {node: '>=0.8.0'}
    dependencies:
      has-flag: 1.0.0
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color/7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-color/9.0.2:
    resolution: {integrity: sha1-UPCCiI5LCk4szS0LT570780zJIU=, tarball: supports-color/download/supports-color-9.0.2.tgz}
    engines: {node: '>=12'}
    dependencies:
      has-flag: 5.0.1
    dev: true

  /svg-baker/1.7.0:
    resolution: {integrity: sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=, tarball: svg-baker/download/svg-baker-1.7.0.tgz}
    dependencies:
      bluebird: 3.7.2
      clone: 2.1.2
      he: registry.nlark.com/he/1.2.0
      image-size: 0.5.5
      loader-utils: 1.4.0
      merge-options: 1.0.1
      micromatch: 3.1.0
      postcss: 5.2.18
      postcss-prefix-selector: 1.13.0
      posthtml-rename-id: 1.0.12
      posthtml-svg-mode: 1.0.3
      query-string: 4.3.4
      traverse: 0.6.6
    dev: true

  /svg-tags/1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=, tarball: svg-tags/download/svg-tags-1.0.0.tgz}
    dev: true

  /svgo/2.8.0:
    resolution: {integrity: sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=, tarball: svgo/download/svgo-2.8.0.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.1.3
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8
    dev: true

  /swap-case/1.1.2:
    resolution: {integrity: sha1-w5IDpFhzhfrTyFCgvRvK+ggZdOM=, tarball: swap-case/download/swap-case-1.1.2.tgz}
    dependencies:
      lower-case: 1.1.4
      upper-case: 1.1.3
    dev: true

  /table/6.7.3:
    resolution: {integrity: sha1-JVOIQ5cVpzg5G9LuTLyomk0Fqbc=, tarball: table/download/table-6.7.3.tgz}
    engines: {node: '>=10.0.0'}
    dependencies:
      ajv: 8.8.0
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /terser/4.8.0:
    resolution: {integrity: sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=, tarball: terser/download/terser-4.8.0.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.20
    dev: true

  /text-table/0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=, tarball: text-table/download/text-table-0.2.0.tgz}
    dev: true

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=, tarball: through/download/through-2.3.8.tgz}
    dev: true

  /through2/2.0.5:
    resolution: {integrity: sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=, tarball: through2/download/through2-2.0.5.tgz}
    dependencies:
      readable-stream: 2.3.7
      xtend: 4.0.2
    dev: true

  /tinymce/5.10.2:
    resolution: {integrity: sha512-5QhnZ6c8F28fYucLLc00MM37fZoAZ4g7QCYzwIl38i5TwJR5xGqzOv6YMideyLM4tytCzLCRwJoQen2LI66p5A==, tarball: tinymce/download/tinymce-5.10.2.tgz?cache=0&sync_timestamp=1637126736818&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftinymce%2Fdownload%2Ftinymce-5.10.2.tgz}
    dev: false

  /title-case/2.1.1:
    resolution: {integrity: sha1-PhJyFtpY0rxb7PE3q5Ha46fNj6o=, tarball: title-case/download/title-case-2.1.1.tgz}
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3
    dev: true

  /tmp/0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=, tarball: tmp/download/tmp-0.0.33.tgz}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: true

  /to-object-path/0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=, tarball: to-object-path/download/to-object-path-0.3.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /to-regex-range/2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=, tarball: to-regex-range/download/to-regex-range-2.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /to-regex/3.0.2:
    resolution: {integrity: sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=, tarball: to-regex/download/to-regex-3.0.2.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0
    dev: true

  /tough-cookie/2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=, tarball: tough-cookie/download/tough-cookie-2.5.0.tgz}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.8.0
      punycode: 2.1.1
    dev: true

  /traverse/0.6.6:
    resolution: {integrity: sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc=, tarball: traverse/download/traverse-0.6.6.tgz}
    dev: true

  /trim-newlines/3.0.1:
    resolution: {integrity: sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=, tarball: trim-newlines/download/trim-newlines-3.0.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /tslib/1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: tslib/download/tslib-1.14.1.tgz?cache=0&sync_timestamp=1632920830240&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslib%2Fdownload%2Ftslib-1.14.1.tgz}
    dev: true

  /tslib/2.1.0:
    resolution: {integrity: sha1-2mCGDxwuyqVwOrfTm8Bba/mIuXo=, tarball: tslib/download/tslib-2.1.0.tgz?cache=0&sync_timestamp=1632920830240&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslib%2Fdownload%2Ftslib-2.1.0.tgz}
    dev: true

  /tslib/2.3.1:
    resolution: {integrity: sha1-6KM1rdXOrlGqJh0ypJAVjvBC7wE=, tarball: tslib/download/tslib-2.3.1.tgz?cache=0&sync_timestamp=1632920830240&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslib%2Fdownload%2Ftslib-2.3.1.tgz}
    dev: true

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=, tarball: tunnel-agent/download/tunnel-agent-0.6.0.tgz}
    dependencies:
      safe-buffer: registry.nlark.com/safe-buffer/5.2.1
    dev: true

  /tweetnacl/0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=, tarball: tweetnacl/download/tweetnacl-0.14.5.tgz}
    dev: true

  /type-check/0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: type-check/download/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-fest/0.18.1:
    resolution: {integrity: sha1-20vBUaSiz07r+a3V23VQjbbMhB8=, tarball: type-fest/download/type-fest-0.18.1.tgz}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=, tarball: type-fest/download/type-fest-0.20.2.tgz}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.6.0:
    resolution: {integrity: sha1-jSojcNPfiG61yQraHFv2GIrPg4s=, tarball: type-fest/download/type-fest-0.6.0.tgz}
    engines: {node: '>=8'}
    dev: true

  /type-fest/0.8.1:
    resolution: {integrity: sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=, tarball: type-fest/download/type-fest-0.8.1.tgz}
    engines: {node: '>=8'}
    dev: true

  /typedarray-to-buffer/3.1.5:
    resolution: {integrity: sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=, tarball: typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz}
    dependencies:
      is-typedarray: 1.0.0
    dev: true

  /typedarray/0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=, tarball: typedarray/download/typedarray-0.0.6.tgz}
    dev: true

  /uglify-js/3.14.3:
    resolution: {integrity: sha1-wPJd/qHo5TI+zPWWEL4ItgQ8Fc8=, tarball: uglify-js/download/uglify-js-3.14.3.tgz}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dev: true
    optional: true

  /unc-path-regex/0.1.2:
    resolution: {integrity: sha1-5z3T17DXxe2G+6xrCufYxqadUPo=, tarball: unc-path-regex/download/unc-path-regex-0.1.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /underscore.string/3.3.5:
    resolution: {integrity: sha1-/CrSVbi9MJ4jnLxYFv0jqbfqQCM=, tarball: underscore.string/download/underscore.string-3.3.5.tgz}
    dependencies:
      sprintf-js: 1.1.2
      util-deprecate: registry.nlark.com/util-deprecate/1.0.2
    dev: true

  /underscore/1.13.1:
    resolution: {integrity: sha1-DBxr0t9UtrafIxQGbWW2zeb8+dE=, tarball: underscore/download/underscore-1.13.1.tgz}
    dev: true

  /union-value/1.0.1:
    resolution: {integrity: sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=, tarball: union-value/download/union-value-1.0.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1
    dev: true

  /uniq/1.0.1:
    resolution: {integrity: sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=, tarball: uniq/download/uniq-1.0.1.tgz}
    dev: true

  /universalify/2.0.0:
    resolution: {integrity: sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=, tarball: universalify/download/universalify-2.0.0.tgz}
    engines: {node: '>= 10.0.0'}
    dev: true

  /unpipe/1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=, tarball: unpipe/download/unpipe-1.0.0.tgz}
    engines: {node: '>= 0.8'}
    dev: true

  /unplugin-auto-import/0.4.18_vite@2.6.14:
    resolution: {integrity: sha512-cbb6uuNo07ZyeaTQtFChDWxpApZC5z9aKBCR2SxLzJ8JYY70kDuS9MFOCfRB+v96rLbtenjdsgBlErYGqA+9zQ==, tarball: unplugin-auto-import/download/unplugin-auto-import-0.4.18.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      '@vueuse/core': '*'
    peerDependenciesMeta:
      '@vueuse/core':
        optional: true
    dependencies:
      '@antfu/utils': 0.3.0
      '@rollup/pluginutils': 4.1.1
      local-pkg: 0.4.0
      magic-string: 0.25.7
      resolve: 1.20.0
      unplugin: 0.2.21_vite@2.6.14
    transitivePeerDependencies:
      - rollup
      - vite
      - webpack
    dev: true

  /unplugin-vue-components/0.17.2_vite@2.6.14+vue@3.2.22:
    resolution: {integrity: sha1-235mn6w8w2uHugVvY+LqpHp/G5s=, tarball: unplugin-vue-components/download/unplugin-vue-components-0.17.2.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@babel/traverse': ^7.15.4
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@babel/traverse':
        optional: true
    dependencies:
      '@antfu/utils': 0.3.0
      '@rollup/pluginutils': 4.1.1
      chokidar: 3.5.2
      debug: 4.3.2
      fast-glob: 3.2.7
      local-pkg: 0.1.0
      magic-string: 0.25.7
      minimatch: 3.0.4
      resolve: 1.20.0
      unplugin: 0.2.19_vite@2.6.14
      vue: 3.2.22
    transitivePeerDependencies:
      - rollup
      - supports-color
      - vite
      - webpack
    dev: true

  /unplugin/0.2.19_vite@2.6.14:
    resolution: {integrity: sha1-8WmbNurvFL6Uz16zur/hysN75no=, tarball: unplugin/download/unplugin-0.2.19.tgz}
    peerDependencies:
      rollup: ^2.50.0
      vite: ^2.3.0
      webpack: 4 || 5
    peerDependenciesMeta:
      rollup:
        optional: true
      vite:
        optional: true
      webpack:
        optional: true
    dependencies:
      vite: 2.6.14_sass@1.43.5
      webpack-virtual-modules: 0.4.3
    dev: true

  /unplugin/0.2.21_vite@2.6.14:
    resolution: {integrity: sha512-IJ15/L5XbhnV7J09Zjk0FT5HEkBjkXucWAXQWRsmEtUxmmxwh23yavrmDbCF6ZPxWiVB28+wnKIHePTRRpQPbQ==, tarball: unplugin/download/unplugin-0.2.21.tgz}
    peerDependencies:
      rollup: ^2.50.0
      vite: ^2.3.0
      webpack: 4 || 5
    peerDependenciesMeta:
      rollup:
        optional: true
      vite:
        optional: true
      webpack:
        optional: true
    dependencies:
      vite: 2.6.14_sass@1.43.5
      webpack-virtual-modules: 0.4.3
    dev: true

  /unset-value/1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=, tarball: unset-value/download/unset-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1
    dev: true

  /upper-case-first/1.1.2:
    resolution: {integrity: sha1-XXm+3P8UQZUY/S7bCgUHybaFkRU=, tarball: upper-case-first/download/upper-case-first-1.1.2.tgz}
    dependencies:
      upper-case: 1.1.3
    dev: true

  /upper-case/1.1.3:
    resolution: {integrity: sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=, tarball: upper-case/download/upper-case-1.1.3.tgz}
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: uri-js/download/uri-js-4.4.1.tgz}
    dependencies:
      punycode: 2.1.1
    dev: true

  /urix/0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=, tarball: urix/download/urix-0.1.0.tgz}
    deprecated: Please see https://github.com/lydell/urix#deprecated
    dev: true

  /use/3.1.1:
    resolution: {integrity: sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=, tarball: use/download/use-3.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /user-home/1.1.1:
    resolution: {integrity: sha1-K1viOjK2Onyd640PKNSFcko98ZA=, tarball: user-home/download/user-home-1.1.1.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: util-deprecate/download/util-deprecate-1.0.2.tgz}
    dev: true

  /utils-merge/1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=, tarball: utils-merge/download/utils-merge-1.0.1.tgz}
    engines: {node: '>= 0.4.0'}
    dev: true

  /uuid/3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=, tarball: uuid/download/uuid-3.4.0.tgz}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true
    dev: true

  /v8-compile-cache/2.3.0:
    resolution: {integrity: sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=, tarball: v8-compile-cache/download/v8-compile-cache-2.3.0.tgz}
    dev: true

  /v8flags/2.1.1:
    resolution: {integrity: sha1-qrGh+jDUX4jdMhFIh1rALAtV5bQ=, tarball: v8flags/download/v8flags-2.1.1.tgz}
    engines: {node: '>= 0.10.0'}
    dependencies:
      user-home: 1.1.1
    dev: true

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha1-/JH2uce6FchX9MssXe/uw51PQQo=, tarball: validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz}
    dependencies:
      spdx-correct: 3.1.1
      spdx-expression-parse: 3.0.1
    dev: true

  /vary/1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=, tarball: vary/download/vary-1.1.2.tgz}
    engines: {node: '>= 0.8'}
    dev: true

  /verror/1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0
    dev: true

  /vinyl-file/1.3.0:
    resolution: {integrity: sha1-qgVjTTqGe6kUR77bs0r8sm9E9uc=, tarball: vinyl-file/download/vinyl-file-1.3.0.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      graceful-fs: 4.2.8
      strip-bom: 2.0.0
      strip-bom-stream: 1.0.0
      vinyl: 1.2.0
    dev: true

  /vinyl/1.2.0:
    resolution: {integrity: sha1-XIgDbPVl5d8FVYv8kR+GVt8hiIQ=, tarball: vinyl/download/vinyl-1.2.0.tgz}
    engines: {node: '>= 0.9'}
    dependencies:
      clone: 1.0.4
      clone-stats: 0.0.1
      replace-ext: 0.0.1
    dev: true

  /vite-plugin-banner/0.1.3:
    resolution: {integrity: sha1-09To4oSt3tNf6qVKuMonOp0zSeA=, tarball: vite-plugin-banner/download/vite-plugin-banner-0.1.3.tgz}
    dev: true

  /vite-plugin-compression/0.3.6_vite@2.6.14:
    resolution: {integrity: sha512-aSskQCJsP3VQ8PsnY+vO7UfD5qoFMOEuzg0PG2E9Zqyx+ARmc3wr9KCgOFraZOFW1Y4UAa5BR0SMTjoxHRMJoQ==, tarball: vite-plugin-compression/download/vite-plugin-compression-0.3.6.tgz}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      chalk: 4.1.2
      debug: 4.3.2
      fs-extra: 10.0.0
      vite: 2.6.14_sass@1.43.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vite-plugin-html/2.1.1_vite@2.6.14:
    resolution: {integrity: sha1-AUtEEmpy1FnNRgvRVoAMIl0STL4=, tarball: vite-plugin-html/download/vite-plugin-html-2.1.1.tgz}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      '@rollup/pluginutils': 4.1.1
      dotenv: 10.0.0
      dotenv-expand: 5.1.0
      ejs: 3.1.6
      fs-extra: 10.0.0
      html-minifier-terser: 5.1.1
      vite: 2.6.14_sass@1.43.5
    dev: true

  /vite-plugin-mock/2.9.6_mockjs@1.1.0+vite@2.6.14:
    resolution: {integrity: sha1-BN0j3muqBS+qW5rTF1FMkNYgXiU=, tarball: vite-plugin-mock/download/vite-plugin-mock-2.9.6.tgz}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      mockjs: '>=1.1.0'
      vite: '>=2.0.0'
    dependencies:
      '@rollup/plugin-node-resolve': 13.0.5
      '@types/mockjs': 1.0.4
      chalk: 4.1.2
      chokidar: 3.5.2
      connect: 3.7.0
      debug: 4.3.2
      esbuild: 0.11.3
      fast-glob: 3.2.7
      mockjs: 1.1.0
      path-to-regexp: 6.2.0
      vite: 2.6.14_sass@1.43.5
    transitivePeerDependencies:
      - rollup
      - supports-color
    dev: true

  /vite-plugin-restart/0.0.2_vite@2.6.14:
    resolution: {integrity: sha1-EByTLpw8TV/2BuFaTzLOduHuTdc=, tarball: vite-plugin-restart/download/vite-plugin-restart-0.0.2.tgz}
    peerDependencies:
      vite: ^2.0.0-beta.69
    dependencies:
      chalk: 4.1.2
      micromatch: 4.0.4
      vite: 2.6.14_sass@1.43.5
    dev: true

  /vite-plugin-spritesmith/0.1.1:
    resolution: {integrity: sha1-sb1P3NVi10sbVRBYrG48ONdXkgw=, tarball: vite-plugin-spritesmith/download/vite-plugin-spritesmith-0.1.1.tgz}
    dependencies:
      fs-extra: 10.0.0
      gaze: 1.1.3
      lodash: 4.17.21
      mkdirp: 1.0.4
      spritesheet-templates: 10.5.2
      spritesmith: 3.4.0
    dev: true

  /vite-plugin-svg-icons/1.0.5_vite@2.6.14:
    resolution: {integrity: sha1-gHHj2nxQ1WFOk3JlW5uR+vkd9ZE=, tarball: vite-plugin-svg-icons/download/vite-plugin-svg-icons-1.0.5.tgz}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      '@types/svgo': 2.6.0
      cors: 2.8.5
      debug: 4.3.2
      etag: 1.8.1
      fs-extra: 10.0.0
      svg-baker: 1.7.0
      svgo: 2.8.0
      vite: 2.6.14_sass@1.43.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vite-plugin-vue-setup-extend/0.1.0_vite@2.6.14:
    resolution: {integrity: sha1-nwjvu8Ku0QP1SbNpbe5dRi2yPzg=, tarball: vite-plugin-vue-setup-extend/download/vite-plugin-vue-setup-extend-0.1.0.tgz}
    peerDependencies:
      vite: '>=2.0.0'
    dependencies:
      '@vue/compiler-sfc': 3.2.22
      magic-string: 0.25.7
      vite: 2.6.14_sass@1.43.5
    dev: true

  /vite/2.6.14_sass@1.43.5:
    resolution: {integrity: sha512-2HA9xGyi+EhY2MXo0+A2dRsqsAG3eFNEVIo12olkWhOmc8LfiM+eMdrXf+Ruje9gdXgvSqjLI9freec1RUM5EA==, tarball: vite/download/vite-2.6.14.tgz}
    engines: {node: '>=12.2.0'}
    hasBin: true
    peerDependencies:
      less: '*'
      sass: '*'
      stylus: '*'
    peerDependenciesMeta:
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
    dependencies:
      esbuild: 0.13.5
      postcss: 8.3.9
      resolve: 1.20.0
      rollup: 2.58.0
      sass: 1.43.5
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /vue-demi/0.12.1_vue@3.2.22:
    resolution: {integrity: sha1-9+GO++z/0RqwadFHLXoG4xm0F0w=, tarball: vue-demi/download/vue-demi-0.12.1.tgz}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: 3.2.22
    dev: false

  /vue-eslint-parser/8.0.1_eslint@8.3.0:
    resolution: {integrity: sha1-JeCLIKQUVRUx8+GfmZkC4ez0XxM=, tarball: vue-eslint-parser/download/vue-eslint-parser-8.0.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'
    dependencies:
      debug: 4.3.2
      eslint: 8.3.0
      eslint-scope: 6.0.0
      eslint-visitor-keys: 3.0.0
      espree: 9.0.0
      esquery: 1.4.0
      lodash: 4.17.21
      semver: 7.3.5
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-router/4.0.12_vue@3.2.22:
    resolution: {integrity: sha1-jceSzd9bsavMOQj5BkE23n4TxGA=, tarball: vue-router/download/vue-router-4.0.12.tgz}
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      '@vue/devtools-api': 6.0.0-beta.19
      vue: 3.2.22
    dev: false

  /vue/3.2.22:
    resolution: {integrity: sha512-KD5nZpXVZquOC6926Xnp3zOvswrUyO9Rya7ZUoxWFQEjFDW4iACtwzubRB4Um2Om9kj6CaJOqAVRDSFlqLpdgw==, tarball: vue/download/vue-3.2.22.tgz}
    dependencies:
      '@vue/compiler-dom': 3.2.22
      '@vue/compiler-sfc': 3.2.22
      '@vue/runtime-dom': 3.2.22
      '@vue/server-renderer': 3.2.22_vue@3.2.22
      '@vue/shared': 3.2.22
    dev: false

  /vuex/4.0.2_vue@3.2.22:
    resolution: {integrity: sha1-+Jbb1b8qDpY/AMZ+m2EN50nMrMk=, tarball: vuex/download/vuex-4.0.2.tgz}
    peerDependencies:
      vue: ^3.0.2
    dependencies:
      '@vue/devtools-api': 6.0.0-beta.19
      vue: 3.2.22
    dev: false

  /wcwidth/1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=, tarball: wcwidth/download/wcwidth-1.0.1.tgz}
    dependencies:
      defaults: 1.0.3
    dev: true

  /webpack-virtual-modules/0.4.3:
    resolution: {integrity: sha1-zVl8bVHVpey0c+6hmDpY+ooX3tk=, tarball: webpack-virtual-modules/download/webpack-virtual-modules-0.4.3.tgz}
    dev: true

  /which/1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=, tarball: which/download/which-1.3.1.tgz}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /word-wrap/1.2.3:
    resolution: {integrity: sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=, tarball: word-wrap/download/word-wrap-1.2.3.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /wordwrap/1.0.0:
    resolution: {integrity: sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=, tarball: wordwrap/download/wordwrap-1.0.0.tgz}
    dev: true

  /wrap-ansi/6.2.0:
    resolution: {integrity: sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=, tarball: wrap-ansi/download/wrap-ansi-6.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: wrappy/download/wrappy-1.0.2.tgz}
    dev: true

  /write-file-atomic/3.0.3:
    resolution: {integrity: sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=, tarball: write-file-atomic/download/write-file-atomic-3.0.3.tgz}
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.5
      typedarray-to-buffer: 3.1.5
    dev: true

  /xss/1.0.10:
    resolution: {integrity: sha1-XNY6mxR6dVoUywRVx9uIZhIOtNI=, tarball: xss/download/xss-1.0.10.tgz}
    engines: {node: '>= 0.10.0'}
    hasBin: true
    dependencies:
      commander: 2.20.3
      cssfilter: 0.0.10
    dev: false

  /xtend/4.0.2:
    resolution: {integrity: sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=, tarball: xtend/download/xtend-4.0.2.tgz}
    engines: {node: '>=0.4'}
    dev: true

  /yallist/4.0.0:
    resolution: {integrity: sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=, tarball: yallist/download/yallist-4.0.0.tgz}
    dev: true

  /yaml/1.10.2:
    resolution: {integrity: sha1-IwHF/78StGfejaIzOkWeKeeSDks=, tarball: yaml/download/yaml-1.10.2.tgz}
    engines: {node: '>= 6'}
    dev: true

  /yargs-parser/20.2.9:
    resolution: {integrity: sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=, tarball: yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1634135116887&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz}
    engines: {node: '>=10'}
    dev: true

  registry.nlark.com/async/0.9.2:
    resolution: {integrity: sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/async/download/async-0.9.2.tgz}
    name: async
    version: 0.9.2
    dev: true

  registry.nlark.com/async/2.6.3:
    resolution: {integrity: sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/async/download/async-2.6.3.tgz}
    name: async
    version: 2.6.3
    dependencies:
      lodash: 4.17.21
    dev: true

  registry.nlark.com/basic-auth/2.0.1:
    resolution: {integrity: sha1-uZgnm/R844NEtPPPkW1Gebv1Hjo=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/basic-auth/download/basic-auth-2.0.1.tgz}
    name: basic-auth
    version: 2.0.1
    engines: {node: '>= 0.8'}
    dependencies:
      safe-buffer: registry.nlark.com/safe-buffer/5.1.2
    dev: true

  registry.nlark.com/colors/1.4.0:
    resolution: {integrity: sha1-xQSRR51MG9rtLJztMs98fcI2D3g=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/colors/download/colors-1.4.0.tgz}
    name: colors
    version: 1.4.0
    engines: {node: '>=0.1.90'}
    dev: true

  registry.nlark.com/corser/2.0.1:
    resolution: {integrity: sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/corser/download/corser-2.0.1.tgz}
    name: corser
    version: 2.0.1
    engines: {node: '>= 0.4.0'}
    dev: true

  registry.nlark.com/cssesc/3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/cssesc/download/cssesc-3.0.0.tgz}
    name: cssesc
    version: 3.0.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.nlark.com/eventemitter3/4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/eventemitter3/download/eventemitter3-4.0.7.tgz}
    name: eventemitter3
    version: 4.0.7
    dev: true

  registry.nlark.com/follow-redirects/1.14.4:
    resolution: {integrity: sha1-g4/fSKi73XnlLuUfsclOPtmLk3k=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/follow-redirects/download/follow-redirects-1.14.4.tgz}
    name: follow-redirects
    version: 1.14.4
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  registry.nlark.com/he/1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/he/download/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: true

  registry.nlark.com/html-encoding-sniffer/3.0.0:
    resolution: {integrity: sha1-LLGozw21JBR3blsqegTV3ZgVjek=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/html-encoding-sniffer/download/html-encoding-sniffer-3.0.0.tgz}
    name: html-encoding-sniffer
    version: 3.0.0
    engines: {node: '>=12'}
    dependencies:
      whatwg-encoding: registry.nlark.com/whatwg-encoding/2.0.0
    dev: true

  registry.nlark.com/http-proxy/1.18.1:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/http-proxy/download/http-proxy-1.18.1.tgz}
    name: http-proxy
    version: 1.18.1
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: registry.nlark.com/eventemitter3/4.0.7
      follow-redirects: registry.nlark.com/follow-redirects/1.14.4
      requires-port: registry.nlark.com/requires-port/1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  registry.nlark.com/iconv-lite/0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz}
    name: iconv-lite
    version: 0.4.24
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.nlark.com/safer-buffer/2.1.2
    dev: true

  registry.nlark.com/iconv-lite/0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/iconv-lite/download/iconv-lite-0.6.3.tgz}
    name: iconv-lite
    version: 0.6.3
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.nlark.com/safer-buffer/2.1.2
    dev: true

  registry.nlark.com/lodash/4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21
    dev: true

  registry.nlark.com/mime/1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/mime/download/mime-1.6.0.tgz}
    name: mime
    version: 1.6.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.nlark.com/minimist/1.2.5:
    resolution: {integrity: sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/minimist/download/minimist-1.2.5.tgz?cache=0&sync_timestamp=1631509547344&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-1.2.5.tgz}
    name: minimist
    version: 1.2.5
    dev: true

  registry.nlark.com/mkdirp/0.5.5:
    resolution: {integrity: sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1631509600466&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz}
    name: mkdirp
    version: 0.5.5
    hasBin: true
    dependencies:
      minimist: registry.nlark.com/minimist/1.2.5
    dev: true

  registry.nlark.com/opener/1.5.2:
    resolution: {integrity: sha1-XTfh81B3udysQwE3InGv3rKhNZg=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/opener/download/opener-1.5.2.tgz}
    name: opener
    version: 1.5.2
    hasBin: true
    dev: true

  registry.nlark.com/portfinder/1.0.28:
    resolution: {integrity: sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/portfinder/download/portfinder-1.0.28.tgz}
    name: portfinder
    version: 1.0.28
    engines: {node: '>= 0.12.0'}
    dependencies:
      async: registry.nlark.com/async/2.6.3
      debug: 3.2.7
      mkdirp: registry.nlark.com/mkdirp/0.5.5
    dev: true

  registry.nlark.com/postcss-media-query-parser/0.2.3:
    resolution: {integrity: sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz}
    name: postcss-media-query-parser
    version: 0.2.3
    dev: true

  registry.nlark.com/postcss-resolve-nested-selector/0.1.1:
    resolution: {integrity: sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz}
    name: postcss-resolve-nested-selector
    version: 0.1.1
    dev: true

  registry.nlark.com/postcss-selector-parser/6.0.6:
    resolution: {integrity: sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-6.0.6.tgz}
    name: postcss-selector-parser
    version: 6.0.6
    engines: {node: '>=4'}
    dependencies:
      cssesc: registry.nlark.com/cssesc/3.0.0
      util-deprecate: registry.nlark.com/util-deprecate/1.0.2
    dev: true

  registry.nlark.com/postcss-value-parser/4.1.0:
    resolution: {integrity: sha1-RD9qIM7WSBor2k+oUypuVdeJoss=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz}
    name: postcss-value-parser
    version: 4.1.0
    dev: true

  registry.nlark.com/requires-port/1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/requires-port/download/requires-port-1.0.0.tgz}
    name: requires-port
    version: 1.0.0
    dev: true

  registry.nlark.com/safe-buffer/5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz}
    name: safe-buffer
    version: 5.1.2
    dev: true

  registry.nlark.com/safe-buffer/5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz}
    name: safe-buffer
    version: 5.2.1
    dev: true

  registry.nlark.com/safer-buffer/2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz}
    name: safer-buffer
    version: 2.1.2
    dev: true

  registry.nlark.com/union/0.5.0:
    resolution: {integrity: sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/union/download/union-0.5.0.tgz}
    name: union
    version: 0.5.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      qs: 6.10.1
    dev: true

  registry.nlark.com/url-join/4.0.1:
    resolution: {integrity: sha1-tkLiGiZGgI/6F4xMX9o5hE4Szec=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/url-join/download/url-join-4.0.1.tgz}
    name: url-join
    version: 4.0.1
    dev: true

  registry.nlark.com/util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2
    dev: true

  registry.nlark.com/whatwg-encoding/2.0.0:
    resolution: {integrity: sha1-52NfWX/YcCCFhiaAWicp+naYrFM=, registry: https://registry.npm.taobao.org/, tarball: https://registry.nlark.com/whatwg-encoding/download/whatwg-encoding-2.0.0.tgz}
    name: whatwg-encoding
    version: 2.0.0
    engines: {node: '>=12'}
    dependencies:
      iconv-lite: registry.nlark.com/iconv-lite/0.6.3
    dev: true
